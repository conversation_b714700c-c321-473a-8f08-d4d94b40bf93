(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4039],{2596:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var a=t(95155),r=t(12115),i=t(40646);let c=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var n=t(85339),l=t(81284),o=t(84355);function d(){let[e,s]=(0,r.useState)([]),[t,d]=(0,r.useState)(!1),[m,u]=(0,r.useState)([]),h=e=>{s(s=>[...s,e])},g=async()=>{d(!0),s([]),h({test:"Secure Context",status:window.isSecureContext?"pass":"warning",message:window.isSecureContext?"Running in secure context (HTTPS/localhost)":"Not in secure context - camera may not work",details:window.isSecureContext?"Camera API requires HTTPS or localhost":"Try accessing via HTTPS or localhost"});let e=!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia);if(h({test:"MediaDevices API",status:e?"pass":"fail",message:e?"MediaDevices API is supported":"MediaDevices API not supported",details:e?"Browser supports camera access":"Browser does not support camera access"}),!e)return void d(!1);try{if(navigator.permissions&&navigator.permissions.query){let e=await navigator.permissions.query({name:"camera"});h({test:"Camera Permission",status:"granted"===e.state?"pass":"denied"===e.state?"fail":"warning",message:"Camera permission: ".concat(e.state),details:"granted"===e.state?"Camera access is allowed":"denied"===e.state?"Camera access is denied - check browser settings":"Camera permission not yet requested"})}else h({test:"Permission API",status:"warning",message:"Permissions API not supported",details:"Cannot check permission status - will try direct access"})}catch(e){h({test:"Permission Check",status:"warning",message:"Could not check camera permission",details:"Error: ".concat(e)})}try{let e=(await navigator.mediaDevices.enumerateDevices()).filter(e=>"videoinput"===e.kind);u(e),h({test:"Camera Detection",status:e.length>0?"pass":"fail",message:"Found ".concat(e.length," camera(s)"),details:e.map(e=>e.label||"Unknown camera").join(", ")||"No cameras detected"})}catch(e){h({test:"Device Enumeration",status:"fail",message:"Failed to enumerate devices",details:"Error: ".concat(e)})}try{var t;let e=(await navigator.mediaDevices.getUserMedia({video:!0})).getVideoTracks();h({test:"Camera Access",status:"pass",message:"Successfully accessed camera",details:"Active track: ".concat((null==(t=e[0])?void 0:t.label)||"Unknown")}),e.forEach(e=>e.stop())}catch(e){h({test:"Camera Access",status:"fail",message:"Failed to access camera",details:"Error: ".concat(e.name," - ").concat(e.message)})}d(!1)},x=e=>{switch(e){case"pass":return(0,a.jsx)(i.A,{className:"text-green-500",size:16});case"fail":return(0,a.jsx)(c,{className:"text-red-500",size:16});case"warning":return(0,a.jsx)(n.A,{className:"text-yellow-500",size:16});case"info":return(0,a.jsx)(l.A,{className:"text-blue-500",size:16})}},p=e=>{switch(e){case"pass":return"text-green-300";case"fail":return"text-red-300";case"warning":return"text-yellow-300";case"info":return"text-blue-300"}};return(0,a.jsxs)("div",{className:"max-w-2xl mx-auto p-6 bg-gray-800 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(o.A,{className:"text-purple-400",size:24}),(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"Camera Diagnostic Tool"})]}),(0,a.jsx)("button",{onClick:g,disabled:t,className:"w-full py-3 px-4 rounded-lg font-medium transition-colors mb-6 ".concat(t?"bg-gray-600 text-gray-400 cursor-not-allowed":"bg-purple-600 hover:bg-purple-700 text-white"),children:t?"Running Diagnostics...":"Run Camera Diagnostics"}),e.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Diagnostic Results"}),e.map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[x(e.status),(0,a.jsx)("span",{className:"font-medium text-white",children:e.test})]}),(0,a.jsx)("p",{className:"text-sm ".concat(p(e.status)," mb-1"),children:e.message}),e.details&&(0,a.jsx)("p",{className:"text-xs text-gray-400",children:e.details})]},s)),m.length>0&&(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4 mt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-white mb-2",children:"Available Cameras"}),(0,a.jsx)("ul",{className:"space-y-1",children:m.map((e,s)=>(0,a.jsxs)("li",{className:"text-sm text-gray-300",children:[s+1,". ",e.label||"Camera ".concat(s+1),(0,a.jsxs)("span",{className:"text-gray-500 ml-2",children:["(",e.deviceId.substring(0,8),"...)"]})]},e.deviceId))})]})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-300 mb-2",children:"Troubleshooting Tips"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-200 space-y-1",children:[(0,a.jsx)("li",{children:"• Make sure you're accessing the site via HTTPS or localhost"}),(0,a.jsx)("li",{children:"• Check if another application is using your camera"}),(0,a.jsx)("li",{children:"• Try refreshing the page and allowing camera access"}),(0,a.jsx)("li",{children:"• Check your browser's camera permissions in settings"}),(0,a.jsx)("li",{children:"• Try using a different browser (Chrome, Firefox, Safari)"})]})]})]})}},17468:(e,s,t)=>{Promise.resolve().then(t.bind(t,2596))},19946:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var a=t(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),c=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},l=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:d="",children:m,iconNode:u,...h}=e;return(0,a.createElement)("svg",{ref:s,...o,width:r,height:r,stroke:t,strokeWidth:c?24*Number(i)/Number(r):i,className:n("lucide",d),...!m&&!l(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(m)?m:[m]])}),m=(e,s)=>{let t=(0,a.forwardRef)((t,i)=>{let{className:l,...o}=t;return(0,a.createElement)(d,{ref:i,iconNode:s,className:n("lucide-".concat(r(c(e))),"lucide-".concat(e),l),...o})});return t.displayName=c(e),t}},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},81284:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},84355:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(17468)),_N_E=e.O()}]);