const { PrismaClient } = require('@prisma/client');

async function testBirthChartAPI() {
  console.log('🔮 Testing birth chart API...');

  try {
    const prisma = new PrismaClient();
    
    const user = await prisma.user.findUnique({
      where: { qrToken: 'cosmic789' }
    });

    if (!user) {
      throw new Error('Test user not found');
    }

    console.log('👤 Found user:', user.name);
    console.log('📅 Birth date:', user.birthDate);
    console.log('⏰ Birth time:', user.birthTime);
    console.log('📍 Birth place:', user.birthPlace);
    console.log('🆔 User ID:', user.id);

    console.log('\n🌐 You can now test the birth chart generation by:');
    console.log('1. Opening http://localhost:3002/auth?token=cosmic789 in your browser');
    console.log('2. Going to the Horoscope tab');
    console.log('3. Clicking "Calculate My Birth Chart"');
    console.log('\nOr use this curl command:');
    console.log(`curl -X POST http://localhost:3002/api/birth-chart \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{"userId": "${user.id}"}'`);

    await prisma.$disconnect();

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

testBirthChartAPI()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
