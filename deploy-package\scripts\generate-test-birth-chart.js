const { PrismaClient } = require('@prisma/client');
const { calculateBirthChart } = require('../src/lib/astrology.ts');

async function generateTestBirthChart() {
  console.log('🔮 Generating test birth chart...');

  try {
    const prisma = new PrismaClient();

    const user = await prisma.user.findUnique({
      where: { qrToken: 'cosmic789' }
    });

    if (!user) {
      throw new Error('Test user not found');
    }

    console.log('👤 Found user:', user.name);
    console.log('📅 Birth date:', user.birthDate);
    console.log('⏰ Birth time:', user.birthTime);
    console.log('📍 Birth place:', user.birthPlace);

    // Calculate birth chart directly
    const birthDateTime = new Date(`${user.birthDate.toISOString().split('T')[0]}T${user.birthTime}:00`);
    const timezone = 'Asia/Colombo';

    console.log('🔄 Calculating birth chart...');
    const chartData = await calculateBirthChart(
      birthDateTime,
      user.birthLatitude,
      user.birthLongitude,
      timezone
    );

    console.log('✅ Birth chart calculated successfully!');
    console.log('🌟 Ascendant:', chartData.ascendant);
    console.log('🌙 Moon Sign:', chartData.moonSign);
    console.log('☀️ Sun Sign:', chartData.sunSign);

    // Check if planetary details exist
    if (chartData.planetaryDetails) {
      console.log('🪐 Planetary Details Available:', chartData.planetaryDetails.length, 'planets');
      console.log('🔍 Sample planetary data:');
      chartData.planetaryDetails.slice(0, 5).forEach(planet => {
        console.log(`  - ${planet.planet}: ${planet.rashi} (${planet.nakshatra})`);
      });
    }

    // Save to database
    console.log('💾 Saving birth chart to database...');
    const birthChart = await prisma.birthChart.upsert({
      where: { userId: user.id },
      update: {
        birthDateTime: birthDateTime,
        birthPlace: user.birthPlace || 'Unknown',
        birthLatitude: user.birthLatitude,
        birthLongitude: user.birthLongitude,
        timezone: timezone,
        planetPositions: chartData.planets,
        housePositions: chartData.houses,
        aspects: chartData.aspects,
        nakshatras: chartData.nakshatras,
        dashas: chartData.dashas,
        ascendant: chartData.ascendant,
        moonSign: chartData.moonSign,
        sunSign: chartData.sunSign,
        lagnaChart: chartData.lagnaChart,
        navamsaChart: chartData.navamsaChart,
        chandraChart: chartData.chandraChart,
        karakTable: chartData.karakTable,
        avasthaTable: chartData.avasthaTable,
        planetaryDetails: chartData.planetaryDetails,
        vimshottariDasha: chartData.vimshottariDasha,
        ashtakavarga: chartData.ashtakavarga
      },
      create: {
        userId: user.id,
        birthDateTime: birthDateTime,
        birthPlace: user.birthPlace || 'Unknown',
        birthLatitude: user.birthLatitude,
        birthLongitude: user.birthLongitude,
        timezone: timezone,
        planetPositions: chartData.planets,
        housePositions: chartData.houses,
        aspects: chartData.aspects,
        nakshatras: chartData.nakshatras,
        dashas: chartData.dashas,
        ascendant: chartData.ascendant,
        moonSign: chartData.moonSign,
        sunSign: chartData.sunSign,
        lagnaChart: chartData.lagnaChart,
        navamsaChart: chartData.navamsaChart,
        chandraChart: chartData.chandraChart,
        karakTable: chartData.karakTable,
        avasthaTable: chartData.avasthaTable,
        planetaryDetails: chartData.planetaryDetails,
        vimshottariDasha: chartData.vimshottariDasha,
        ashtakavarga: chartData.ashtakavarga
      }
    });

    console.log('✅ Birth chart saved with ID:', birthChart.id);

    await prisma.$disconnect();

  } catch (error) {
    console.error('❌ Error generating birth chart:', error);
    throw error;
  }
}

generateTestBirthChart()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
