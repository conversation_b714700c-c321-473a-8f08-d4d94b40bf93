const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateAdminRole() {
  try {
    console.log('🔄 Updating existing admin to super_admin role...');
    
    // Find the first admin (should be the demo admin)
    const admin = await prisma.admin.findFirst({
      where: {
        role: 'admin'
      }
    });

    if (!admin) {
      console.log('❌ No admin found with role "admin"');
      return;
    }

    console.log(`📧 Found admin: ${admin.email} (${admin.name})`);

    // Update to super_admin
    const updatedAdmin = await prisma.admin.update({
      where: {
        id: admin.id
      },
      data: {
        role: 'super_admin',
        name: 'Super Admin' // Update name as well
      }
    });

    console.log('✅ Successfully updated admin role to super_admin');
    console.log(`👤 Admin: ${updatedAdmin.name} (${updatedAdmin.email})`);
    console.log(`🔑 Role: ${updatedAdmin.role}`);

  } catch (error) {
    console.error('❌ Error updating admin role:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAdminRole();
