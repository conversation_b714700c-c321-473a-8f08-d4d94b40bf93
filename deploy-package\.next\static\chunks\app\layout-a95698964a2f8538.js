(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{1243:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});let r=(0,o(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},17122:(e,t,o)=>{"use strict";async function r(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en";if(o===t)return e;try{return await n(e,o,t)}catch(t){return console.error("Translation error:",t),e}}async function n(e,t,o){let r=await fetch("/api/translate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e,sourceLanguage:t,targetLanguage:o})});if(!r.ok)throw Error("Translation API request failed");let n=await r.json();if(!n.success)throw Error(n.error||"Translation failed");return n.translatedText}o.d(t,{G:()=>a,s:()=>r});let a={en:"English",si:"සිංහල"}},18466:(e,t,o)=>{"use strict";o.d(t,{DialogProvider:()=>f,Ue:()=>p,G_:()=>h});var r=o(95155),n=o(12115),a=o(81284),l=o(1243),i=o(40646),s=o(85339),c=o(54416);let d={default:{icon:a.A,iconColor:"text-blue-400",confirmButtonClass:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"},danger:{icon:l.A,iconColor:"text-red-400",confirmButtonClass:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"},success:{icon:i.A,iconColor:"text-green-400",confirmButtonClass:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"},warning:{icon:s.A,iconColor:"text-yellow-400",confirmButtonClass:"bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700"},info:{icon:a.A,iconColor:"text-blue-400",confirmButtonClass:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"}};function u(e){let{isOpen:t,title:o,message:a,confirmText:l="Confirm",cancelText:i="Cancel",type:s="default",onConfirm:u,onCancel:m,loading:f=!1}=e,g=(0,n.useRef)(null),h=(0,n.useRef)(null),p=d[s],b=p.icon;return((0,n.useEffect)(()=>{if(!t)return;let e=e=>{"Escape"===e.key?m():"Enter"!==e.key||f||u()};return h.current&&h.current.focus(),document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,m,u,f]),(0,n.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),t)?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:e=>{e.target===e.currentTarget&&m()},children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm animate-fadeIn"}),(0,r.jsxs)("div",{ref:g,className:"relative bg-gray-800/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/10 w-full max-w-md mx-auto animate-slideInScale",role:"dialog","aria-modal":"true","aria-labelledby":"dialog-title","aria-describedby":"dialog-description",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 pb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full bg-white/10 ".concat(p.iconColor),children:(0,r.jsx)(b,{size:20})}),(0,r.jsx)("h3",{id:"dialog-title",className:"text-xl font-semibold text-white",children:o})]}),(0,r.jsx)("button",{onClick:m,className:"text-gray-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-white/10","aria-label":"Close dialog",children:(0,r.jsx)(c.A,{size:20})})]}),(0,r.jsxs)("div",{className:"px-6 pb-6",children:[(0,r.jsx)("p",{id:"dialog-description",className:"text-gray-300 leading-relaxed mb-6",children:a}),(0,r.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row gap-3 sm:justify-end",children:[(0,r.jsx)("button",{onClick:m,disabled:f,className:"px-6 py-3 text-gray-300 hover:text-white bg-white/10 hover:bg-white/20 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:i}),(0,r.jsx)("button",{ref:h,onClick:u,disabled:f,className:"px-6 py-3 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none ".concat(p.confirmButtonClass),children:f?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Processing..."})]}):l})]})]})]})]}):null}let m=(0,n.createContext)(void 0);function f(e){let{children:t}=e,[o,a]=(0,n.useState)({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1}),l=(0,n.useCallback)(e=>new Promise(t=>{a({isOpen:!0,options:{confirmText:"Confirm",cancelText:"Cancel",type:"default",...e},resolve:t,loading:!1})}),[]),i=(0,n.useCallback)(function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"info";return new Promise(r=>{a({isOpen:!0,options:{title:e,message:t,confirmText:"OK",cancelText:"",type:o},resolve:e=>r(),loading:!1})})},[]),s=(0,n.useCallback)(()=>{o.resolve&&(a(e=>({...e,loading:!0})),setTimeout(()=>{o.resolve(!0),a({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1})},100))},[o.resolve]),c=(0,n.useCallback)(()=>{o.resolve&&(o.resolve(!1),a({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1}))},[o.resolve]);return(0,r.jsxs)(m.Provider,{value:{showConfirmation:l,showAlert:i},children:[t,(0,r.jsx)(u,{isOpen:o.isOpen,title:o.options.title,message:o.options.message,confirmText:o.options.confirmText,cancelText:o.options.cancelText,type:o.options.type,onConfirm:s,onCancel:c,loading:o.loading})]})}function g(){let e=(0,n.useContext)(m);if(void 0===e)throw Error("useDialog must be used within a DialogProvider");return e}function h(){let{showConfirmation:e}=g();return{confirmDelete:t=>e({title:"Confirm Delete",message:t?'Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'):"Are you sure you want to delete this item? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",type:"danger"}),confirmLogout:()=>e({title:"Confirm Logout",message:"Are you sure you want to logout?",confirmText:"Logout",cancelText:"Cancel",type:"warning"}),confirmAction:function(t,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Confirm";return e({title:t,message:o,confirmText:r,cancelText:"Cancel",type:"default"})}}}function p(){let{showAlert:e}=g();return{showSuccess:(t,o)=>e(t,o,"success"),showError:(t,o)=>e(t,o,"danger"),showWarning:(t,o)=>e(t,o,"warning"),showInfo:(t,o)=>e(t,o,"info")}}},19946:(e,t,o)=>{"use strict";o.d(t,{A:()=>u});var r=o(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,o)=>o?o.toUpperCase():t.toLowerCase()),l=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return t.filter((e,t,o)=>!!e&&""!==e.trim()&&o.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:o="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:d="",children:u,iconNode:m,...f}=e;return(0,r.createElement)("svg",{ref:t,...c,width:n,height:n,stroke:o,strokeWidth:l?24*Number(a)/Number(n):a,className:i("lucide",d),...!u&&!s(f)&&{"aria-hidden":"true"},...f},[...m.map(e=>{let[t,o]=e;return(0,r.createElement)(t,o)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let o=(0,r.forwardRef)((o,a)=>{let{className:s,...c}=o;return(0,r.createElement)(d,{ref:a,iconNode:t,className:i("lucide-".concat(n(l(e))),"lucide-".concat(e),s),...c})});return o.displayName=l(e),o}},27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},30347:()=>{},40646:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});let r=(0,o(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54416:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});let r=(0,o(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59377:(e,t,o)=>{"use strict";o.d(t,{LanguageProvider:()=>i,o:()=>s});var r=o(95155),n=o(12115),a=o(17122);let l=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[o,i]=(0,n.useState)(()=>window.location.pathname.startsWith("/admin")?"en":"si"),[s,c]=(0,n.useState)(!1),[d,u]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=async()=>{try{window.location.pathname.startsWith("/admin")?i("en"):i("si")}catch(e){console.error("Failed to initialize language:",e),i(window.location.pathname.startsWith("/admin")?"en":"si")}finally{u(!0)}};d||e()},[d]);let m=(0,n.useCallback)(e=>{console.log("\uD83C\uDF10 Language update requested:",{from:o,to:e}),o!==e?(i(e),console.log("✅ Language state updated to:",e)):console.log("⚠️ Language already set to:",e)},[o]),f=(0,n.useCallback)(async e=>{if("en"===o)return e;console.log("⚠️ Using translation API for:",e.substring(0,50)+"..."),c(!0);try{return await (0,a.s)(e,o,"en")}catch(t){return console.error("Translation error:",t),e}finally{c(!1)}},[o]);return(0,n.useEffect)(()=>{console.log("\uD83D\uDD04 Language context updated:",{language:o,isInitialized:d})},[o,d]),(0,r.jsx)(l.Provider,{value:{language:o,setLanguage:m,translate:f,isTranslating:s,isInitialized:d},children:t})}function s(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},75968:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,62093,23)),Promise.resolve().then(o.t.bind(o,27735,23)),Promise.resolve().then(o.t.bind(o,30347,23)),Promise.resolve().then(o.bind(o,18466)),Promise.resolve().then(o.bind(o,59377))},81284:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});let r=(0,o(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,o)=>{"use strict";o.d(t,{A:()=>r});let r=(0,o(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6360,8441,1684,7358],()=>t(75968)),_N_E=e.O()}]);