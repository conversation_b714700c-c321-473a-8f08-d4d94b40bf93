#!/bin/bash

# AstroConnect AWS EC2 Deployment Script
# Domain: onlinekubera.store
# Server: **************

set -e  # Exit on any error

# Configuration
DOMAIN="onlinekubera.store"
SERVER_IP="**************"
SSH_KEY="./kuberahoroscope.pem"
SSH_USER="ubuntu"
APP_NAME="astroconnect"
REMOTE_DIR="/opt/astroconnect"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if [ ! -f "$SSH_KEY" ]; then
        error "SSH key not found: $SSH_KEY"
    fi
    
    if [ ! -f ".env.production" ]; then
        error ".env.production file not found"
    fi
    
    if ! command -v ssh &> /dev/null; then
        error "SSH is not installed"
    fi
    
    if ! command -v scp &> /dev/null; then
        error "SCP is not installed"
    fi
    
    # Set correct permissions for SSH key
    chmod 600 "$SSH_KEY"
    
    success "Prerequisites check passed"
}

# Test SSH connection
test_ssh_connection() {
    log "Testing SSH connection to $SERVER_IP..."
    
    if ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" "echo 'SSH connection successful'" &> /dev/null; then
        success "SSH connection successful"
    else
        error "SSH connection failed. Please check your SSH key and server IP."
    fi
}

# Setup server environment
setup_server() {
    log "Setting up server environment..."
    
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" << 'EOF'
        # Update system packages
        sudo apt update && sudo apt upgrade -y
        
        # Install required packages
        sudo apt install -y curl wget git nginx certbot python3-certbot-nginx ufw
        
        # Install Docker
        if ! command -v docker &> /dev/null; then
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            rm get-docker.sh
        fi
        
        # Install Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
        fi
        
        # Configure firewall
        sudo ufw allow ssh
        sudo ufw allow 80
        sudo ufw allow 443
        sudo ufw --force enable
        
        # Create application directory
        sudo mkdir -p /opt/astroconnect
        sudo chown $USER:$USER /opt/astroconnect
        
        echo "Server setup completed"
EOF
    
    success "Server environment setup completed"
}

# Build and transfer application
build_and_transfer() {
    log "Building application locally..."
    
    # Build the application
    npm run build || error "Application build failed"
    
    log "Transferring application files to server..."
    
    # Create a temporary directory for deployment files
    TEMP_DIR=$(mktemp -d)
    
    # Copy necessary files
    cp -r .next "$TEMP_DIR/"
    cp -r public "$TEMP_DIR/"
    cp -r prisma "$TEMP_DIR/"
    cp -r database "$TEMP_DIR/"
    cp package.json "$TEMP_DIR/"
    cp package-lock.json "$TEMP_DIR/"
    cp next.config.js "$TEMP_DIR/"
    cp docker-compose.yml "$TEMP_DIR/"
    cp Dockerfile "$TEMP_DIR/"
    cp nginx.conf "$TEMP_DIR/"
    cp .env.production "$TEMP_DIR/"
    cp -r scripts "$TEMP_DIR/"
    
    # Transfer files to server
    scp -i "$SSH_KEY" -r "$TEMP_DIR"/* "$SSH_USER@$SERVER_IP:$REMOTE_DIR/"
    
    # Clean up
    rm -rf "$TEMP_DIR"
    
    success "Application files transferred successfully"
}

# Deploy application on server
deploy_on_server() {
    log "Deploying application on server..."
    
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" << EOF
        cd $REMOTE_DIR
        
        # Stop existing containers
        docker-compose down || true
        
        # Remove old images
        docker system prune -f || true
        
        # Start the application
        docker-compose up -d --build
        
        # Wait for services to be ready
        echo "Waiting for services to start..."
        sleep 30
        
        # Check if containers are running
        docker-compose ps
        
        echo "Deployment completed"
EOF
    
    success "Application deployed successfully"
}

# Configure SSL with Let's Encrypt
configure_ssl() {
    log "Configuring SSL certificate..."
    
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SSH_USER@$SERVER_IP" << EOF
        # Stop nginx if running
        sudo systemctl stop nginx || true
        
        # Obtain SSL certificate
        sudo certbot certonly --standalone -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN
        
        # Copy certificates to nginx directory
        sudo mkdir -p $REMOTE_DIR/ssl
        sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $REMOTE_DIR/ssl/cert.pem
        sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $REMOTE_DIR/ssl/key.pem
        sudo chown -R $USER:$USER $REMOTE_DIR/ssl
        
        # Restart containers with SSL
        cd $REMOTE_DIR
        docker-compose --profile production up -d
        
        # Setup auto-renewal
        echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
        
        echo "SSL configuration completed"
EOF
    
    success "SSL certificate configured successfully"
}

# Health check
health_check() {
    log "Running health checks..."
    
    # Wait a bit for services to fully start
    sleep 10
    
    # Check HTTP response
    if curl -f "http://$SERVER_IP:3000/api/health" &> /dev/null; then
        success "Application health check passed"
    else
        warning "Application health check failed - checking logs..."
        ssh -i "$SSH_KEY" "$SSH_USER@$SERVER_IP" "cd $REMOTE_DIR && docker-compose logs --tail=20"
    fi
    
    # Check HTTPS response
    if curl -f "https://$DOMAIN/api/health" &> /dev/null; then
        success "HTTPS health check passed"
    else
        warning "HTTPS health check failed"
    fi
}

# Main deployment process
main() {
    log "Starting AWS EC2 deployment for $APP_NAME..."
    log "Domain: $DOMAIN"
    log "Server: $SERVER_IP"
    
    case "${1:-deploy}" in
        "setup")
            check_prerequisites
            test_ssh_connection
            setup_server
            success "Server setup completed!"
            ;;
        "deploy")
            check_prerequisites
            test_ssh_connection
            build_and_transfer
            deploy_on_server
            success "Deployment completed!"
            ;;
        "ssl")
            check_prerequisites
            test_ssh_connection
            configure_ssl
            success "SSL configuration completed!"
            ;;
        "full")
            check_prerequisites
            test_ssh_connection
            setup_server
            build_and_transfer
            deploy_on_server
            configure_ssl
            health_check
            success "Full deployment completed successfully!"
            ;;
        "health")
            health_check
            ;;
        *)
            echo "Usage: $0 {setup|deploy|ssl|full|health}"
            echo "  setup  - Setup server environment only"
            echo "  deploy - Deploy application only"
            echo "  ssl    - Configure SSL only"
            echo "  full   - Complete deployment process"
            echo "  health - Run health checks"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
