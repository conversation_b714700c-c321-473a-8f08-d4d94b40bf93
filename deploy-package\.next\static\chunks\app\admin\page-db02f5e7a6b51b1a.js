(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{18466:(e,t,s)=>{"use strict";s.d(t,{DialogProvider:()=>h,Ue:()=>p,G_:()=>g});var a=s(95155),r=s(12115),l=s(81284),i=s(1243),n=s(40646),c=s(85339),o=s(54416);let d={default:{icon:l.A,iconColor:"text-blue-400",confirmButtonClass:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"},danger:{icon:i.A,iconColor:"text-red-400",confirmButtonClass:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"},success:{icon:n.A,iconColor:"text-green-400",confirmButtonClass:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"},warning:{icon:c.A,iconColor:"text-yellow-400",confirmButtonClass:"bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700"},info:{icon:l.A,iconColor:"text-blue-400",confirmButtonClass:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"}};function x(e){let{isOpen:t,title:s,message:l,confirmText:i="Confirm",cancelText:n="Cancel",type:c="default",onConfirm:x,onCancel:m,loading:h=!1}=e,u=(0,r.useRef)(null),g=(0,r.useRef)(null),p=d[c],b=p.icon;return((0,r.useEffect)(()=>{if(!t)return;let e=e=>{"Escape"===e.key?m():"Enter"!==e.key||h||x()};return g.current&&g.current.focus(),document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,m,x,h]),(0,r.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),t)?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:e=>{e.target===e.currentTarget&&m()},children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm animate-fadeIn"}),(0,a.jsxs)("div",{ref:u,className:"relative bg-gray-800/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/10 w-full max-w-md mx-auto animate-slideInScale",role:"dialog","aria-modal":"true","aria-labelledby":"dialog-title","aria-describedby":"dialog-description",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-white/10 ".concat(p.iconColor),children:(0,a.jsx)(b,{size:20})}),(0,a.jsx)("h3",{id:"dialog-title",className:"text-xl font-semibold text-white",children:s})]}),(0,a.jsx)("button",{onClick:m,className:"text-gray-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-white/10","aria-label":"Close dialog",children:(0,a.jsx)(o.A,{size:20})})]}),(0,a.jsxs)("div",{className:"px-6 pb-6",children:[(0,a.jsx)("p",{id:"dialog-description",className:"text-gray-300 leading-relaxed mb-6",children:l}),(0,a.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row gap-3 sm:justify-end",children:[(0,a.jsx)("button",{onClick:m,disabled:h,className:"px-6 py-3 text-gray-300 hover:text-white bg-white/10 hover:bg-white/20 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:n}),(0,a.jsx)("button",{ref:g,onClick:x,disabled:h,className:"px-6 py-3 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none ".concat(p.confirmButtonClass),children:h?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Processing..."})]}):i})]})]})]})]}):null}let m=(0,r.createContext)(void 0);function h(e){let{children:t}=e,[s,l]=(0,r.useState)({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1}),i=(0,r.useCallback)(e=>new Promise(t=>{l({isOpen:!0,options:{confirmText:"Confirm",cancelText:"Cancel",type:"default",...e},resolve:t,loading:!1})}),[]),n=(0,r.useCallback)(function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"info";return new Promise(a=>{l({isOpen:!0,options:{title:e,message:t,confirmText:"OK",cancelText:"",type:s},resolve:e=>a(),loading:!1})})},[]),c=(0,r.useCallback)(()=>{s.resolve&&(l(e=>({...e,loading:!0})),setTimeout(()=>{s.resolve(!0),l({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1})},100))},[s.resolve]),o=(0,r.useCallback)(()=>{s.resolve&&(s.resolve(!1),l({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1}))},[s.resolve]);return(0,a.jsxs)(m.Provider,{value:{showConfirmation:i,showAlert:n},children:[t,(0,a.jsx)(x,{isOpen:s.isOpen,title:s.options.title,message:s.options.message,confirmText:s.options.confirmText,cancelText:s.options.cancelText,type:s.options.type,onConfirm:c,onCancel:o,loading:s.loading})]})}function u(){let e=(0,r.useContext)(m);if(void 0===e)throw Error("useDialog must be used within a DialogProvider");return e}function g(){let{showConfirmation:e}=u();return{confirmDelete:t=>e({title:"Confirm Delete",message:t?'Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'):"Are you sure you want to delete this item? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",type:"danger"}),confirmLogout:()=>e({title:"Confirm Logout",message:"Are you sure you want to logout?",confirmText:"Logout",cancelText:"Cancel",type:"warning"}),confirmAction:function(t,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Confirm";return e({title:t,message:s,confirmText:a,cancelText:"Cancel",type:"default"})}}}function p(){let{showAlert:e}=u();return{showSuccess:(t,s)=>e(t,s,"success"),showError:(t,s)=>e(t,s,"danger"),showWarning:(t,s)=>e(t,s,"warning"),showInfo:(t,s)=>e(t,s,"info")}}},37831:(e,t,s)=>{"use strict";s.d(t,{Wh:()=>r,qK:()=>a});let a=["aries","taurus","gemini","cancer","leo","virgo","libra","scorpio","sagittarius","capricorn","aquarius","pisces"],r={aries:{name:"Aries",symbol:"♈",dates:"Mar 21 - Apr 19",element:"Fire"},taurus:{name:"Taurus",symbol:"♉",dates:"Apr 20 - May 20",element:"Earth"},gemini:{name:"Gemini",symbol:"♊",dates:"May 21 - Jun 20",element:"Air"},cancer:{name:"Cancer",symbol:"♋",dates:"Jun 21 - Jul 22",element:"Water"},leo:{name:"Leo",symbol:"♌",dates:"Jul 23 - Aug 22",element:"Fire"},virgo:{name:"Virgo",symbol:"♍",dates:"Aug 23 - Sep 22",element:"Earth"},libra:{name:"Libra",symbol:"♎",dates:"Sep 23 - Oct 22",element:"Air"},scorpio:{name:"Scorpio",symbol:"♏",dates:"Oct 23 - Nov 21",element:"Water"},sagittarius:{name:"Sagittarius",symbol:"♐",dates:"Nov 22 - Dec 21",element:"Fire"},capricorn:{name:"Capricorn",symbol:"♑",dates:"Dec 22 - Jan 19",element:"Earth"},aquarius:{name:"Aquarius",symbol:"♒",dates:"Jan 20 - Feb 18",element:"Air"},pisces:{name:"Pisces",symbol:"♓",dates:"Feb 19 - Mar 20",element:"Water"}}},67961:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>X});var a=s(95155),r=s(12115),l=s(35695),i=s(75525),n=s(34835),c=s(72713),o=s(17580),d=s(57434),x=s(38564),m=s(381),h=s(92731),u=s(69783),g=s(47924),p=s(66932),b=s(84616),j=s(97939),f=s(38164),y=s(13717),N=s(62525),w=s(24357),v=s(37831),S=s(18466);function k(){var e,t;let[s,l]=(0,r.useState)([]),[i,n]=(0,r.useState)(!0),[c,o]=(0,r.useState)(""),[d,x]=(0,r.useState)([]),[m,h]=(0,r.useState)(!1),[u,k]=(0,r.useState)(!1),[A,C]=(0,r.useState)(!1),[z,D]=(0,r.useState)(null),[E,T]=(0,r.useState)(null),[L,P]=(0,r.useState)("en"),{confirmDelete:I}=(0,S.G_)(),{showSuccess:R,showError:U}=(0,S.Ue)(),[B,O]=(0,r.useState)({name:"",email:"",phoneNumber:"",address:"",birthDate:"",birthTime:"",birthPlace:"",zodiacSign:"",languagePreference:"en"}),[F,q]=(0,r.useState)({name:"",email:"",phoneNumber:"",address:"",birthDate:"",birthTime:"",birthPlace:"",zodiacSign:"",languagePreference:"en"});(0,r.useEffect)(()=>{G(),_()},[]);let _=async()=>{try{let e=await fetch("/api/settings/default-language"),t=await e.json();t.success&&t.data.defaultLanguage&&(P(t.data.defaultLanguage),O(e=>({...e,languagePreference:t.data.defaultLanguage})))}catch(e){console.error("Failed to fetch default language:",e)}},G=async()=>{try{n(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/users",{headers:{Authorization:"Bearer ".concat(e)}}),s=await t.json();s.success&&l(s.data.users)}catch(e){console.error("Error fetching users:",e)}finally{n(!1)}},W=async e=>{let t=s.find(t=>t.id===e),a=(null==t?void 0:t.name)||"this user";if(await I(a))try{console.log("\uD83D\uDDD1️ Deleting user:",e);let t=localStorage.getItem("admin-token"),a=await fetch("/api/admin/users?userId=".concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}}),r=await a.json();console.log("Delete response:",r),a.ok&&r.success?(console.log("✅ User deleted successfully"),l(s.filter(t=>t.id!==e)),R("Success","User deleted successfully!")):(console.error("❌ Delete failed:",r.error),U("Delete Failed",r.error||"Unknown error occurred"))}catch(e){console.error("❌ Error deleting user:",e),U("Error","Error deleting user. Please try again.")}},Q=async e=>{try{if(console.log("\uD83D\uDCF1 Generating QR code for user:",e.name),e.qrToken){console.log("✅ User already has QR token, generating image...");let t=await fetch("/api/admin/qr-generate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("admin-token"))},body:JSON.stringify({qrToken:e.qrToken,userName:e.name})});if(t.ok){let s=await t.json();if(s.success&&s.data.qrCodeImage){let t=document.createElement("a");t.href=s.data.qrCodeImage,t.download="".concat(e.name,"-qr-code.png"),document.body.appendChild(t),t.click(),document.body.removeChild(t),alert("QR code downloaded successfully!");return}}}console.log("⚠️ Creating new QR code for existing user...");let t=localStorage.getItem("admin-token"),s=await fetch("/api/admin/users",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({name:e.name,email:e.email,phoneNumber:e.phoneNumber,address:e.address,birthDate:e.birthDate,birthTime:e.birthTime,birthPlace:e.birthPlace,languagePreference:e.languagePreference})}),a=await s.json();if(console.log("QR generation response:",a),a.success&&a.data.qrCodeImage){console.log("✅ QR code generated successfully");let t=document.createElement("a");t.href=a.data.qrCodeImage,t.download="".concat(e.name,"-qr-code.png"),document.body.appendChild(t),t.click(),document.body.removeChild(t),R("Success","QR code downloaded successfully!")}else console.error("❌ QR generation failed:",a.error),U("QR Generation Failed",a.error||"Unknown error occurred")}catch(e){console.error("❌ Error generating QR code:",e),U("Error","Error generating QR code. Please try again.")}},H=async e=>{if(console.log("\uD83D\uDD17 Copying QR URL for user:",e.name),!e.qrToken){console.error("❌ User has no QR token"),alert("This user does not have a QR code yet. Please generate one first.");return}D(e),C(!0)},J=e=>{console.log("✏️ Editing user:",e.name),T(e),q({name:e.name||"",email:e.email||"",phoneNumber:e.phoneNumber||"",address:e.address||"",birthDate:e.birthDate?new Date(e.birthDate).toISOString().split("T")[0]:"",birthTime:e.birthTime||"",birthPlace:e.birthPlace||"",zodiacSign:e.zodiacSign||"",languagePreference:e.languagePreference||"en"}),k(!0)},M=async e=>{try{await navigator.clipboard.writeText(e);let t=document.querySelector(".copy-url-modal-btn");if(t){let e=t.innerHTML;t.innerHTML="✓ Copied!",setTimeout(()=>{t.innerHTML=e},2e3)}}catch(e){console.error("Error copying QR URL:",e),U("Copy Failed","Failed to copy URL to clipboard")}},Z=async()=>{if(0!==d.length&&("Are you sure you want to delete ".concat(d.length," user").concat(d.length>1?"s":"","? This action cannot be undone."),await I("".concat(d.length," user").concat(d.length>1?"s":""))))try{console.log("\uD83D\uDDD1️ Bulk deleting users:",d);let e=localStorage.getItem("admin-token"),t=d.map(t=>fetch("/api/admin/users?userId=".concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat(e)}})),a=(await Promise.all(t)).filter(e=>e.ok).length;a===d.length?(console.log("✅ All users deleted successfully"),l(s.filter(e=>!d.includes(e.id))),x([]),R("Success","Successfully deleted ".concat(a," user").concat(a>1?"s":"","!"))):(console.warn("⚠️ Some deletions failed"),U("Partial Success","Deleted ".concat(a," out of ").concat(d.length," users. Please refresh to see current state.")),G())}catch(e){console.error("❌ Bulk delete error:",e),U("Error","Error during bulk delete. Please try again.")}},V=()=>{if(0===d.length)return;console.log("\uD83D\uDCCA Exporting users:",d);let e=new Blob([["Name,Email,Phone,Address,Birth Date,Birth Time,Zodiac Sign,Language,Scan Count,Last Scanned,QR Token",...s.filter(e=>d.includes(e.id)).map(e=>[e.name,e.email||"",e.phoneNumber||"",e.address||"",e.birthDate?new Date(e.birthDate).toLocaleDateString():"",e.birthTime||"",e.zodiacSign||"",e.languagePreference||"en",e.scan_count||0,e.last_scanned?new Date(e.last_scanned).toLocaleDateString():"Never",e.qrToken||""].map(e=>'"'.concat(String(e).replace(/"/g,'""'),'"')).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),t=document.createElement("a");t.href=URL.createObjectURL(e),t.download="astroconnect-users-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(t),t.click(),document.body.removeChild(t),alert("Exported ".concat(d.length," user").concat(d.length>1?"s":""," to CSV file!"))},K=async e=>{if(e.preventDefault(),!E)return void alert("No user selected for editing");try{console.log("\uD83D\uDCBE Updating user:",E.id,F);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/users/".concat(E.id),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({...F,zodiacSign:F.zodiacSign||void 0})}),a=await t.json();console.log("Update response:",a),a.success?(console.log("✅ User updated successfully"),l(s.map(e=>e.id===E.id?{...e,...a.data.user}:e)),q({name:"",email:"",phoneNumber:"",address:"",birthDate:"",birthTime:"",birthPlace:"",zodiacSign:"",languagePreference:"en"}),k(!1),T(null),alert("User updated successfully!")):(console.error("❌ Update failed:",a.error),alert("Failed to update user: "+(a.error||"Unknown error")))}catch(e){console.error("❌ Error updating user:",e),alert("Error updating user. Please try again.")}},Y=async e=>{e.preventDefault();try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/users",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({...B,zodiacSign:B.zodiacSign||void 0})}),a=await t.json();if(a.success){if(l([a.data.user,...s]),O({name:"",email:"",phoneNumber:"",address:"",birthDate:"",birthTime:"",birthPlace:"",zodiacSign:"",languagePreference:"en"}),h(!1),a.data.qrCodeImage){let e=document.createElement("a");e.href=a.data.qrCodeImage,e.download="".concat(a.data.user.name,"-qr-code.png"),document.body.appendChild(e),e.click(),document.body.removeChild(e)}}else U("Add User Failed",a.error||"Unknown error occurred")}catch(e){console.error("Error adding user:",e),alert("Error adding user. Please try again.")}},X=s.filter(e=>{var t;return e.name.toLowerCase().includes(c.toLowerCase())||(null==(t=e.email)?void 0:t.toLowerCase().includes(c.toLowerCase()))});return i?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,a.jsx)("input",{type:"text",placeholder:"Search users...",value:c,onChange:e=>o(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,a.jsxs)("button",{onClick:()=>alert("Advanced filtering coming soon! Currently you can search by name or email."),className:"flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors",children:[(0,a.jsx)(p.A,{size:16}),(0,a.jsx)("span",{children:"Filter"})]}),(0,a.jsxs)("button",{onClick:()=>h(!0),className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[(0,a.jsx)(b.A,{size:16}),(0,a.jsx)("span",{children:"Add User"})]})]}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-white/5",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300",onChange:e=>{e.target.checked?x(X.map(e=>e.id)):x([])}})}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Name"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Email"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Phone"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Zodiac"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Language"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Scans"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Last Active"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-300 font-semibold",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:X.map(e=>(0,a.jsxs)("tr",{className:"border-t border-white/10 hover:bg-white/5",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300",checked:d.includes(e.id),onChange:t=>{t.target.checked?x([...d,e.id]):x(d.filter(t=>t!==e.id))}})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"text-2xl",children:e.zodiacSign&&v.Wh[e.zodiacSign]?v.Wh[e.zodiacSign].symbol:"⭐"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:e.zodiacSign&&v.Wh[e.zodiacSign]?v.Wh[e.zodiacSign].name:"Unknown"})]})]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:e.email||"N/A"}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:e.phoneNumber||"N/A"}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:e.zodiacSign&&v.Wh[e.zodiacSign]?v.Wh[e.zodiacSign].name:"Unknown"}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("en"===e.languagePreference?"bg-blue-500/20 text-blue-300":"bg-green-500/20 text-green-300"),children:"en"===e.languagePreference?"English":"සිංහල"})}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:e.scan_count||0}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:e.last_scanned?new Date(e.last_scanned).toLocaleDateString():"Never"}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2","data-user-id":e.id,children:[(0,a.jsx)("button",{onClick:()=>Q(e),className:"text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 p-2 rounded transition-all duration-200 active:scale-95",title:"Generate QR Code",children:(0,a.jsx)(j.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>H(e),className:"text-green-400 hover:text-green-300 hover:bg-green-400/10 p-2 rounded transition-all duration-200 active:scale-95 copy-url-btn",title:"Copy QR URL",children:(0,a.jsx)(f.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>J(e),className:"text-purple-400 hover:text-purple-300 hover:bg-purple-400/10 p-2 rounded transition-all duration-200 active:scale-95",title:"Edit",children:(0,a.jsx)(y.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>W(e.id),className:"text-red-400 hover:text-red-300 hover:bg-red-400/10 p-2 rounded transition-all duration-200 active:scale-95",title:"Delete",children:(0,a.jsx)(N.A,{size:16})})]})})]},e.id))})]})})}),d.length>0&&(0,a.jsx)("div",{className:"bg-purple-600/20 border border-purple-500/50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-white",children:[d.length," user",d.length>1?"s":""," selected"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>Z(),className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors",children:"Delete Selected"}),(0,a.jsx)("button",{onClick:()=>V(),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors",children:"Export Selected"})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-gray-300 text-sm",children:["Showing ",X.length," of ",s.length," users"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>alert("Pagination coming soon!"),className:"bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded text-sm transition-colors",children:"Previous"}),(0,a.jsx)("button",{className:"bg-purple-600 text-white px-3 py-1 rounded text-sm",children:"1"}),(0,a.jsx)("button",{onClick:()=>alert("Pagination coming soon!"),className:"bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded text-sm transition-colors",children:"Next"})]})]}),m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"Add New User"}),(0,a.jsxs)("form",{onSubmit:Y,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Name"}),(0,a.jsx)("input",{type:"text",value:B.name,onChange:e=>O({...B,name:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Email (Optional)"}),(0,a.jsx)("input",{type:"email",value:B.email,onChange:e=>O({...B,email:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Phone Number (Optional)"}),(0,a.jsx)("input",{type:"tel",value:B.phoneNumber,onChange:e=>O({...B,phoneNumber:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"+94 77 123 4567"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Address (Optional)"}),(0,a.jsx)("textarea",{value:B.address,onChange:e=>O({...B,address:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",rows:2,placeholder:"Enter full address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Birth Date"}),(0,a.jsx)("input",{type:"date",value:B.birthDate,onChange:e=>O({...B,birthDate:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Birth Time (Optional)"}),(0,a.jsx)("input",{type:"time",value:B.birthTime,onChange:e=>O({...B,birthTime:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Birth Place (City)"}),(0,a.jsx)("input",{type:"text",value:B.birthPlace,onChange:e=>O({...B,birthPlace:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"e.g., Colombo, Sri Lanka"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required for accurate horoscope calculations"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Zodiac Sign"}),(0,a.jsxs)("select",{value:B.zodiacSign,onChange:e=>O({...B,zodiacSign:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Auto-detect from birth date"}),v.qK.map(e=>(0,a.jsxs)("option",{value:e,className:"bg-gray-800 text-white",children:[v.Wh[e].symbol," ",v.Wh[e].name," (",v.Wh[e].dates,")"]},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Language Preference"}),(0,a.jsxs)("select",{value:B.languagePreference,onChange:e=>O({...B,languagePreference:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"en",className:"bg-gray-800 text-white",children:"English"}),(0,a.jsx)("option",{value:"si",className:"bg-gray-800 text-white",children:"Sinhala"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Add User & Generate QR"}),(0,a.jsx)("button",{type:"button",onClick:()=>{h(!1),O({name:"",email:"",phoneNumber:"",address:"",birthDate:"",birthTime:"",birthPlace:"",zodiacSign:"",languagePreference:"en"})},className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Cancel"})]})]})]})}),u&&E&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-white",children:["Edit User: ",E.name]}),(0,a.jsxs)("div",{className:"text-sm text-gray-400 space-y-1",children:[(0,a.jsxs)("p",{children:["User ID: ",E.id]}),(0,a.jsxs)("p",{children:["Created: ",new Date(E.createdAt).toLocaleDateString()]}),E.updatedAt&&(0,a.jsxs)("p",{children:["Last Updated: ",new Date(E.updatedAt).toLocaleDateString()]})]})]}),(0,a.jsxs)("form",{onSubmit:K,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Name"}),(0,a.jsx)("input",{type:"text",value:F.name,onChange:e=>q({...F,name:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Email (Optional)"}),(0,a.jsx)("input",{type:"email",value:F.email,onChange:e=>q({...F,email:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Phone Number (Optional)"}),(0,a.jsx)("input",{type:"tel",value:F.phoneNumber,onChange:e=>q({...F,phoneNumber:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"+94 77 123 4567"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Address (Optional)"}),(0,a.jsx)("textarea",{value:F.address,onChange:e=>q({...F,address:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",rows:2,placeholder:"Enter full address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Birth Date"}),(0,a.jsx)("input",{type:"date",value:F.birthDate,onChange:e=>q({...F,birthDate:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Birth Time (Optional)"}),(0,a.jsx)("input",{type:"time",value:F.birthTime,onChange:e=>q({...F,birthTime:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Birth Place (City)"}),(0,a.jsx)("input",{type:"text",value:F.birthPlace,onChange:e=>q({...F,birthPlace:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"e.g., Colombo, Sri Lanka"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required for accurate horoscope calculations"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:["Zodiac Sign",(null==E?void 0:E.zodiacSign)&&(0,a.jsxs)("span",{className:"ml-2 text-xs text-gray-400",children:["(Current: ",null==(e=v.Wh[E.zodiacSign])?void 0:e.symbol," ",null==(t=v.Wh[E.zodiacSign])?void 0:t.name,")"]})]}),(0,a.jsxs)("select",{value:F.zodiacSign,onChange:e=>q({...F,zodiacSign:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Auto-detect from birth date"}),v.qK.map(e=>(0,a.jsxs)("option",{value:e,className:"bg-gray-800 text-white",children:[v.Wh[e].symbol," ",v.Wh[e].name," (",v.Wh[e].dates,")"]},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Language Preference"}),(0,a.jsxs)("select",{value:F.languagePreference,onChange:e=>q({...F,languagePreference:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"en",className:"bg-gray-800 text-white",children:"English"}),(0,a.jsx)("option",{value:"si",className:"bg-gray-800 text-white",children:"Sinhala"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Update User"}),(0,a.jsx)("button",{type:"button",onClick:()=>{k(!1),T(null),q({name:"",email:"",phoneNumber:"",address:"",birthDate:"",birthTime:"",birthPlace:"",zodiacSign:"",languagePreference:"en"})},className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Cancel"})]})]})]})}),A&&z&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"QR Code URL"}),(0,a.jsxs)("p",{className:"text-gray-300 mb-4",children:["Share this URL with ",(0,a.jsx)("strong",{children:z.name})," to access their dashboard:"]}),(0,a.jsx)("div",{className:"bg-gray-700 rounded-lg p-3 mb-4",children:(0,a.jsx)("code",{className:"text-green-400 text-sm break-all",children:"".concat(window.location.origin,"/qr/").concat(z.qrToken)})}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)("button",{onClick:()=>M("".concat(window.location.origin,"/qr/").concat(z.qrToken)),className:"flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors copy-url-modal-btn flex items-center justify-center gap-2",children:[(0,a.jsx)(w.A,{size:16}),"Copy URL"]}),(0,a.jsx)("button",{onClick:()=>{C(!1),D(null)},className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Close"})]})]})})]})}var A=s(25657),C=s(69074),z=s(34869),D=s(54416);function E(e){let{isOpen:t,onClose:s,onAdd:l}=e,[i,n]=(0,r.useState)({zodiacSign:"",type:"",content:"",date:new Date().toISOString().split("T")[0],language:"en"}),[c,o]=(0,r.useState)(!1),d=async e=>{if(e.preventDefault(),!i.zodiacSign||!i.type||!i.content)return void alert("Please fill in all required fields");o(!0);try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/horoscopes",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(i)}),a=await t.json();a.success?(l(a.data),n({zodiacSign:"",type:"",content:"",date:new Date().toISOString().split("T")[0],language:"en"}),s()):alert("Error adding horoscope: "+a.error)}catch(e){console.error("Error adding horoscope:",e),alert("Error adding horoscope. Please try again.")}finally{o(!1)}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white",children:"Add New Horoscope"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-white",children:(0,a.jsx)(D.A,{size:20})})]}),(0,a.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Zodiac Sign *"}),(0,a.jsxs)("select",{value:i.zodiacSign,onChange:e=>n({...i,zodiacSign:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},required:!0,children:[(0,a.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select zodiac sign"}),v.qK.map(e=>(0,a.jsxs)("option",{value:e,className:"bg-gray-800 text-white",children:[v.Wh[e].symbol," ",v.Wh[e].name]},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Type *"}),(0,a.jsxs)("select",{value:i.type,onChange:e=>n({...i,type:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},required:!0,children:[(0,a.jsx)("option",{value:"",className:"bg-gray-800 text-white",children:"Select type"}),(0,a.jsx)("option",{value:"weekly",className:"bg-gray-800 text-white",children:"Weekly"}),(0,a.jsx)("option",{value:"monthly",className:"bg-gray-800 text-white",children:"Monthly"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"ℹ️ Daily guides are automatically generated using AI. Only weekly and monthly horoscopes can be created manually."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Date *"}),(0,a.jsx)("input",{type:"date",value:i.date,onChange:e=>n({...i,date:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Language"}),(0,a.jsxs)("select",{value:i.language,onChange:e=>n({...i,language:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"en",className:"bg-gray-800 text-white",children:"English"}),(0,a.jsx)("option",{value:"si",className:"bg-gray-800 text-white",children:"සිංහල"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Content *"}),(0,a.jsx)("textarea",{value:i.content,onChange:e=>n({...i,content:e.target.value}),rows:6,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none",placeholder:"Enter horoscope content...",required:!0})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",disabled:c,className:"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 text-white py-2 px-4 rounded-lg transition-colors",children:c?"Adding...":"Add Horoscope"}),(0,a.jsx)("button",{type:"button",onClick:s,className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Cancel"})]})]})]})}):null}var T=s(53904),L=s(85339),P=s(40646),I=s(71539),R=s(92657);function U(){let[e,t]=(0,r.useState)(null),[s,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)(!1),[c,o]=(0,r.useState)(new Date().toISOString().split("T")[0]),[d,x]=(0,r.useState)([]),[m,h]=(0,r.useState)(!1),{showSuccess:u,showError:g}=(0,S.Ue)();(0,r.useEffect)(()=>{p();let e=setInterval(p,3e4);return()=>clearInterval(e)},[]);let p=async()=>{try{let e=localStorage.getItem("admin-token"),s=await fetch("/api/admin/scheduler?action=status",{headers:{Authorization:"Bearer ".concat(e)}}),a=await s.json();a.success&&t(a.data)}catch(e){console.error("Error fetching scheduler status:",e)}finally{l(!1)}},b=async()=>{if(!i){n(!0);try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/scheduler",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({action:"generate",date:c})}),s=await t.json();s.success?(u("Success","Successfully generated ".concat(s.data.count," daily readings for ").concat(s.data.date)),p()):g("Generation Failed",s.error||"Unknown error occurred")}catch(e){console.error("Error generating readings:",e),g("Error","Error generating readings. Please try again.")}finally{n(!1)}}},j=async e=>{try{let t=localStorage.getItem("admin-token"),s=await fetch("/api/admin/scheduler?action=readings&date=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),a=await s.json();a.success&&(x(a.data.readings),h(!0))}catch(e){console.error("Error fetching readings:",e)}};return s?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)(T.A,{className:"animate-spin text-purple-400",size:32}),(0,a.jsx)("span",{className:"ml-2 text-white",children:"Loading scheduler status..."})]}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Automated Daily Guides"}),(0,a.jsx)("p",{className:"text-gray-300",children:"Daily guides are automatically generated using Gemini AI for all zodiac signs"})]}),(0,a.jsxs)("button",{onClick:p,className:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2",children:[(0,a.jsx)(T.A,{size:16}),(0,a.jsx)("span",{children:"Refresh"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Scheduler Status"}),(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.scheduler.isRunning?"bg-green-400":"bg-red-400")})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Status:"}),(0,a.jsx)("span",{className:e.scheduler.isRunning?"text-green-400":"text-red-400",children:e.scheduler.isRunning?"Running":"Stopped"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Next Check:"}),(0,a.jsx)("span",{className:"text-white",children:e.scheduler.nextCheck})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Last Generated:"}),(0,a.jsx)("span",{className:"text-white",children:e.scheduler.lastGeneratedDate||"Never"})]}),e.scheduler.currentSriLankanTime&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Sri Lankan Time:"}),(0,a.jsx)("span",{className:"text-white text-xs",children:e.scheduler.currentSriLankanTime})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Today's Status"}),e.isComplete?(0,a.jsx)(P.A,{className:"text-green-400",size:20}):(0,a.jsx)(L.A,{className:"text-yellow-400",size:20})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Generated:"}),(0,a.jsxs)("span",{className:"text-white",children:[e.todayReadingsCount,"/12"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"Status:"}),(0,a.jsx)("span",{className:e.isComplete?"text-green-400":"text-yellow-400",children:e.isComplete?"Complete":"Incomplete"})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2 mt-3",children:(0,a.jsx)("div",{className:"h-2 rounded-full ".concat(e.isComplete?"bg-green-400":"bg-yellow-400"),style:{width:"".concat(e.todayReadingsCount/12*100,"%")}})})]})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Manual Generation"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("input",{type:"date",value:c,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"}),(0,a.jsx)("button",{onClick:b,disabled:i,className:"w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.A,{className:"animate-spin",size:16}),(0,a.jsx)("span",{children:"Generating..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I.A,{size:16}),(0,a.jsx)("span",{children:"Generate Now"})]})})]})]})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Daily Guides (Last 7 Days)"}),(0,a.jsx)("div",{className:"space-y-3",children:Object.entries(e.recentReadings).sort((e,t)=>{let[s]=e,[a]=t;return new Date(a).getTime()-new Date(s).getTime()}).slice(0,7).map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(C.A,{size:16,className:"text-gray-400"}),(0,a.jsx)("span",{className:"text-white font-medium",children:new Date(t).toLocaleDateString()}),(0,a.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs ".concat(12===s.count?"bg-green-500/20 text-green-400":"bg-yellow-500/20 text-yellow-400"),children:[s.count,"/12 signs"]})]}),(0,a.jsxs)("button",{onClick:()=>j(t),className:"bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm flex items-center space-x-1",children:[(0,a.jsx)(R.A,{size:14}),(0,a.jsx)("span",{children:"View"})]})]},t)})})]}),m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white",children:"Daily Readings"}),(0,a.jsx)("button",{onClick:()=>h(!1),className:"text-gray-400 hover:text-white",children:"\xd7"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:d.map(e=>{var t,s;return(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"text-xl",children:null==(t=v.Wh[e.zodiacSign])?void 0:t.symbol}),(0,a.jsx)("span",{className:"text-white font-semibold",children:null==(s=v.Wh[e.zodiacSign])?void 0:s.name})]}),(0,a.jsx)("p",{className:"text-gray-300 text-sm line-clamp-3",children:e.advice}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-gray-400",children:["Lucky: ",e.luckyNumber," • ",e.luckyColor]})]},e.id)})})]})})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(L.A,{className:"mx-auto mb-4 text-red-400",size:48}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Unable to Load Status"}),(0,a.jsx)("p",{className:"text-gray-300",children:"Failed to fetch scheduler status."}),(0,a.jsx)("button",{onClick:p,className:"mt-4 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg",children:"Retry"})]})}function B(){let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)("all"),[c,d]=(0,r.useState)("all"),[x,m]=(0,r.useState)("all"),[h,u]=(0,r.useState)(!1),[g,p]=(0,r.useState)({}),[j,f]=(0,r.useState)("daily-guides"),{confirmDelete:w}=(0,S.G_)();(0,r.useEffect)(()=>{k(),D()},[i,c,x]);let k=async()=>{try{l(!0);let e=new URLSearchParams;"all"!==i&&e.append("zodiacSign",i),"all"!==c&&e.append("type",c),"all"!==x&&e.append("language",x);let s=await fetch("/api/admin/horoscopes?".concat(e)),a=await s.json();if(a.success){let e=a.data.filter(e=>"daily"!==e.type);t(e)}}catch(e){console.error("Error fetching horoscopes:",e)}finally{l(!1)}},D=async()=>{try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/users/counts",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();e.success&&p(e.data)}}catch(e){console.error("Error fetching user counts:",e)}},T=async s=>{let a=e.find(e=>e.id===s),r=a?"".concat(a.type," horoscope for ").concat(a.zodiacSign):"this horoscope";if(await w(r))try{(await fetch("/api/admin/horoscopes?id=".concat(s),{method:"DELETE"})).ok&&t(e.filter(e=>e.id!==s))}catch(e){console.error("Error deleting horoscope:",e)}};return s?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Content Management"}),(0,a.jsx)("p",{className:"text-gray-300",children:"Manage automated daily guides and manual horoscope content"})]})}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-white/5 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>f("daily-guides"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ".concat("daily-guides"===j?"bg-purple-600 text-white":"text-gray-300 hover:text-white hover:bg-white/10"),children:[(0,a.jsx)(A.A,{size:16}),(0,a.jsx)("span",{children:"Automated Daily Guides"})]}),(0,a.jsxs)("button",{onClick:()=>f("horoscopes"),className:"flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-colors ".concat("horoscopes"===j?"bg-purple-600 text-white":"text-gray-300 hover:text-white hover:bg-white/10"),children:[(0,a.jsx)(y.A,{size:16}),(0,a.jsx)("span",{children:"Manual Horoscopes"})]})]}),"daily-guides"===j?(0,a.jsx)(U,{}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-300 text-sm mb-2",children:"Zodiac Sign"}),(0,a.jsxs)("select",{value:i,onChange:e=>n(e.target.value),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"all",className:"bg-gray-800 text-white",children:"All Signs"}),v.qK.map(e=>(0,a.jsxs)("option",{value:e,className:"bg-gray-800 text-white",children:[v.Wh[e].symbol," ",v.Wh[e].name]},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-300 text-sm mb-2",children:"Type"}),(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"all",className:"bg-gray-800 text-white",children:"All Types"}),(0,a.jsx)("option",{value:"weekly",className:"bg-gray-800 text-white",children:"Weekly"}),(0,a.jsx)("option",{value:"monthly",className:"bg-gray-800 text-white",children:"Monthly"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Daily guides are automated"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-300 text-sm mb-2",children:"Language"}),(0,a.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",style:{colorScheme:"dark"},children:[(0,a.jsx)("option",{value:"all",className:"bg-gray-800 text-white",children:"All Languages"}),(0,a.jsx)("option",{value:"en",className:"bg-gray-800 text-white",children:"English"}),(0,a.jsx)("option",{value:"si",className:"bg-gray-800 text-white",children:"සිංහල"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("button",{onClick:()=>u(!0),className:"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors",children:[(0,a.jsx)(b.A,{size:16}),(0,a.jsx)("span",{children:"Add Horoscope"})]})})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"text-2xl",children:e.zodiacSign&&v.Wh[e.zodiacSign]?v.Wh[e.zodiacSign].symbol:"⭐"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-white font-semibold",children:e.zodiacSign&&v.Wh[e.zodiacSign]?v.Wh[e.zodiacSign].name:"Unknown"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("daily"===e.type?"bg-green-500/20 text-green-300":"weekly"===e.type?"bg-blue-500/20 text-blue-300":"bg-purple-500/20 text-purple-300"),children:e.type}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("en"===e.language?"bg-blue-500/20 text-blue-300":"bg-green-500/20 text-green-300"),children:"en"===e.language?"EN":"SI"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{className:"text-purple-400 hover:text-purple-300 p-1",title:"Edit",children:(0,a.jsx)(y.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>T(e.id),className:"text-red-400 hover:text-red-300 p-1",title:"Delete",children:(0,a.jsx)(N.A,{size:16})})]})]}),(0,a.jsx)("p",{className:"text-gray-300 text-sm mb-4 line-clamp-3",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(C.A,{size:12}),(0,a.jsx)("span",{children:new Date(e.date).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(o.A,{size:12}),(0,a.jsxs)("span",{children:[g[e.zodiacSign]||0," users"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(z.A,{size:12}),(0,a.jsx)("span",{children:"en"===e.language?"English":"සිංහල"})]})]})]},e.id))}),0===e.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:"\uD83D\uDCDD"}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-2",children:"No horoscopes found"}),(0,a.jsx)("p",{className:"text-gray-400 mb-4",children:"No horoscopes match your current filters. Try adjusting your search criteria."}),(0,a.jsx)("button",{onClick:()=>u(!0),className:"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors",children:"Add First Horoscope"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.filter(e=>"weekly"===e.type).length}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Weekly"})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.filter(e=>"monthly"===e.type).length}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Monthly"})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.filter(e=>"si"===e.language).length}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Sinhala"})]})]}),(0,a.jsx)(E,{isOpen:h,onClose:()=>u(!1),onAdd:s=>{t([s,...e]),D()}})]})]})}var O=s(53311),F=s(71007),q=s(4516),_=s(14186);let G=()=>{let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)([]),[i,n]=(0,r.useState)(!0),[c,o]=(0,r.useState)(""),[d,m]=(0,r.useState)(null),[h,u]=(0,r.useState)(!1),[p,j]=(0,r.useState)(null),{confirmDelete:f}=(0,S.G_)(),{showSuccess:y,showError:N}=(0,S.Ue)();(0,r.useEffect)(()=>{w(),v()},[]);let w=async()=>{try{n(!0);let e=localStorage.getItem("admin-token"),s=await fetch("/api/admin/birth-charts",{headers:{Authorization:"Bearer ".concat(e)}}),a=await s.json();a.success&&t(a.data)}catch(e){console.error("Error fetching birth charts:",e)}finally{n(!1)}},v=async()=>{try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/users",{headers:{Authorization:"Bearer ".concat(e)}}),s=await t.json();s.success&&l(s.data.users)}catch(e){console.error("Error fetching users:",e)}},k=async(e,t)=>{if(await f("Generate Birth Chart","Generate birth chart for ".concat(t,"? This will calculate their personalized horoscope based on birth details.")))try{j(e);let t=localStorage.getItem("admin-token"),s=await fetch("/api/admin/birth-charts/generate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({userId:e})}),a=await s.json();a.success?(y("Success","Birth chart generated successfully!"),w()):N("Error",a.error||"Failed to generate birth chart")}catch(e){console.error("Error generating birth chart:",e),N("Error","Failed to generate birth chart")}finally{j(null)}},A=async(e,t)=>{if(await f("Regenerate Birth Chart","Regenerate birth chart for ".concat(t,"? This will recalculate their horoscope with updated interpretations.")))try{j(e);let t=localStorage.getItem("admin-token"),s=await fetch("/api/admin/birth-charts/regenerate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({userId:e})}),a=await s.json();a.success?(y("Success","Birth chart regenerated successfully!"),w()):N("Error",a.error||"Failed to regenerate birth chart")}catch(e){console.error("Error regenerating birth chart:",e),N("Error","Failed to regenerate birth chart")}finally{j(null)}},z=e=>{m(e),u(!0)},D=e.filter(e=>e.user.name.toLowerCase().includes(c.toLowerCase())||e.user.email.toLowerCase().includes(c.toLowerCase())||e.user.zodiacSign.toLowerCase().includes(c.toLowerCase())),E=s.filter(t=>t.birthDate&&t.birthTime&&t.birthLatitude&&t.birthLongitude&&!e.some(e=>e.userId===t.id));return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center",children:[(0,a.jsx)(O.A,{className:"mr-2 text-purple-400",size:24}),"User Birth Charts (Handahana)"]}),(0,a.jsx)("p",{className:"text-gray-300 mt-1",children:"View and manage automatically generated birth charts that users see in their dashboard"})]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name, email, or zodiac sign...",value:c,onChange:e=>o(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-white/10 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"text-purple-400 mr-3",size:24}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Total Birth Charts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.length})]})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-sm border border-white/10 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(F.A,{className:"text-blue-400 mr-3",size:24}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Users with Charts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.length})]})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-white/10 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"text-green-400 mr-3",size:24}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Pending Generation"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:E.length})]})]})})]}),E.length>0&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-sm border border-green-400/20 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[(0,a.jsx)(b.A,{className:"mr-2 text-green-400",size:20}),"Users Ready for Birth Chart Generation (",E.length,")"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:E.map(e=>(0,a.jsx)("div",{className:"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-white",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:e.email}),(0,a.jsx)("p",{className:"text-xs text-blue-300",children:e.zodiacSign})]}),(0,a.jsx)("button",{onClick:()=>k(e.id,e.name),disabled:p===e.id,className:"bg-green-500 hover:bg-green-600 disabled:bg-gray-500 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center",children:p===e.id?(0,a.jsx)(T.A,{className:"animate-spin",size:16}):(0,a.jsx)(b.A,{size:16})})]})},e.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-500/10 to-blue-500/10 backdrop-blur-sm border border-purple-400/20 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[(0,a.jsx)(x.A,{className:"mr-2 text-purple-400",size:20}),"Generated Birth Charts (",D.length,")"]}),0===D.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(x.A,{className:"mx-auto text-gray-400 mb-4",size:48}),(0,a.jsx)("p",{className:"text-gray-400 text-lg",children:"No birth charts found"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Birth charts will appear here once generated"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:D.map(e=>(0,a.jsxs)("div",{className:"bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-semibold text-white",children:e.user.name}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:e.user.email}),(0,a.jsx)("p",{className:"text-xs text-blue-300",children:e.user.zodiacSign})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>z(e),className:"bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-lg transition-colors",title:"View Details",children:(0,a.jsx)(R.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>A(e.userId,e.user.name),disabled:p===e.userId,className:"bg-orange-500 hover:bg-orange-600 disabled:bg-gray-500 text-white p-2 rounded-lg transition-colors",title:"Regenerate",children:p===e.userId?(0,a.jsx)(T.A,{className:"animate-spin",size:16}):(0,a.jsx)(T.A,{size:16})})]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,a.jsx)(C.A,{className:"mr-2",size:14}),new Date(e.birthDateTime).toLocaleDateString()]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,a.jsx)(q.A,{className:"mr-2",size:14}),e.birthPlace]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,a.jsx)(_.A,{className:"mr-2",size:14}),new Date(e.calculatedAt).toLocaleDateString()]})]}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-white/10",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-400",children:"Ascendant"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:e.ascendant})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-400",children:"Moon"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:e.moonSign})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-400",children:"Sun"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:e.sunSign})]})]})})]},e.id))})]})]})};var W=s(33109);function Q(){var e,t;let[s,l]=(0,r.useState)(null),[i,n]=(0,r.useState)(!0),[d,x]=(0,r.useState)("30d"),[m,h]=(0,r.useState)(null),[u,g]=(0,r.useState)(!1),{showSuccess:p,showError:b}=(0,S.Ue)();(0,r.useEffect)(()=>{j(),f()},[d]);let j=async()=>{try{n(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/analytics?range=".concat(d),{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();l(e.data)}else{let e=await t.text();console.error("Failed to load analytics:",t.status,e),l({totalUsers:0,totalHoroscopes:0,totalPersonalHoroscopes:0,dailyReadings:0,userGrowth:[],horoscopesByType:[],usersByZodiac:[]})}}catch(e){console.error("Error fetching analytics:",e),l({totalUsers:0,totalHoroscopes:0,totalPersonalHoroscopes:0,dailyReadings:0,userGrowth:[],horoscopesByType:[],usersByZodiac:[]})}finally{n(!1)}},f=async()=>{try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/scheduler",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();h(e.data)}}catch(e){console.error("Failed to load scheduler status:",e)}},y=async()=>{try{g(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/scheduler",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({action:"generate"})});if(t.ok){let e=await t.json();e.success?(p("Success","Successfully generated ".concat(e.data.count," daily readings!")),f()):b("Generation Failed",e.error||"Unknown error occurred")}else{let e=await t.text();console.error("Generate readings error:",t.status,e),429===t.status?b("Rate Limited","Please wait before generating again to avoid API rate limits"):b("Error","Failed to generate readings. Please try again.")}}catch(e){console.error("Error generating readings:",e),alert("Error generating readings. Please try again.")}finally{g(!1)}};return i?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-white/10 rounded w-48 mb-6"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[1,2,3,4].map(e=>(0,a.jsx)("div",{className:"bg-white/10 rounded-lg p-6 h-32"},e))})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Analytics Dashboard"}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("select",{value:d,onChange:e=>x(e.target.value),className:"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",children:[(0,a.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90d",children:"Last 90 days"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Total Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:(null==s?void 0:s.totalUsers)||0}),(0,a.jsxs)("p",{className:"text-green-400 text-sm",children:["+",(null==s?void 0:s.userGrowth)||0,"% growth"]})]}),(0,a.jsx)(o.A,{className:"w-8 h-8 text-blue-400"})]})}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Active Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:(null==s?void 0:s.activeUsers)||0}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Users with scans"})]}),(0,a.jsx)(W.A,{className:"w-8 h-8 text-green-400"})]})}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Total Scans"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:(null==s?void 0:s.totalScans)||0}),(0,a.jsxs)("p",{className:"text-green-400 text-sm",children:["+",(null==s?void 0:s.scanGrowth)||0,"% growth"]})]}),(0,a.jsx)(c.A,{className:"w-8 h-8 text-yellow-400"})]})}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Content Items"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:(null==s?void 0:s.totalHoroscopes)||0}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Horoscopes & guides"})]}),(0,a.jsx)(C.A,{className:"w-8 h-8 text-purple-400"})]})})]}),m&&(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white flex items-center",children:[(0,a.jsx)(_.A,{className:"mr-2 text-blue-400"}),"Daily Readings Scheduler"]}),(0,a.jsx)("button",{onClick:f,className:"text-gray-400 hover:text-white transition-colors",title:"Refresh Status",children:(0,a.jsx)(T.A,{size:16})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[m.scheduler.isRunning?(0,a.jsx)(P.A,{className:"text-green-400 mr-2",size:20}):(0,a.jsx)(L.A,{className:"text-red-400 mr-2",size:20}),(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Status"})]}),(0,a.jsx)("p",{className:"text-sm ".concat(m.scheduler.isRunning?"text-green-300":"text-red-300"),children:m.scheduler.isRunning?"Running":"Stopped"})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(C.A,{className:"text-blue-400 mr-2",size:20}),(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Last Generated"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:m.scheduler.lastGeneratedDate||"Never"})]}),(0,a.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(_.A,{className:"text-purple-400 mr-2",size:20}),(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Next Check"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-300",children:m.scheduler.nextCheck})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-sm text-gray-300",children:(0,a.jsxs)("p",{children:["Server Time: ",new Date(m.serverTime).toLocaleString()]})}),(0,a.jsx)("button",{onClick:y,disabled:u,className:"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.A,{className:"animate-spin",size:16}),(0,a.jsx)("span",{children:"Generating..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.A,{size:16}),(0,a.jsx)("span",{children:"Generate Now"})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Popular Zodiac Signs"}),(0,a.jsx)("div",{className:"space-y-3",children:(null==s||null==(e=s.topZodiacSigns)?void 0:e.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:{Aries:"♈",Taurus:"♉",Gemini:"♊",Cancer:"♋",Leo:"♌",Virgo:"♍",Libra:"♎",Scorpio:"♏",Sagittarius:"♐",Capricorn:"♑",Aquarius:"♒",Pisces:"♓"}[e.sign]||"⭐"}),(0,a.jsx)("span",{className:"text-white",children:e.sign})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})}),(0,a.jsx)("span",{className:"text-gray-300 text-sm w-12",children:e.count})]})]},e.sign)))||(0,a.jsx)("p",{className:"text-gray-400 text-center py-4",children:"No data available"})})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Language Preferences"}),(0,a.jsx)("div",{className:"space-y-3",children:(null==s||null==(t=s.languageDistribution)?void 0:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(z.A,{className:"w-5 h-5 text-blue-400"}),(0,a.jsx)("span",{className:"text-white",children:"en"===e.language?"English":"Sinhala"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"".concat(e.percentage,"%")}})}),(0,a.jsx)("span",{className:"text-gray-300 text-sm w-12",children:e.count})]})]},e.language)))||(0,a.jsx)("p",{className:"text-gray-400 text-center py-4",children:"No data available"})})]})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"Advanced Analytics"}),(0,a.jsx)("p",{className:"text-gray-300 mb-6",children:"More detailed charts, user behavior analysis, and engagement metrics coming soon."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-400",children:[(0,a.jsx)("div",{children:"\uD83D\uDCCA User Engagement Trends"}),(0,a.jsx)("div",{children:"\uD83D\uDCC8 Content Performance"}),(0,a.jsx)("div",{children:"\uD83C\uDFAF Conversion Analytics"})]})]})]})}var H=s(51154),J=s(4229);function M(){let[e,t]=(0,r.useState)(null),[s,l]=(0,r.useState)(!0),[i,n]=(0,r.useState)(!1),[c,o]=(0,r.useState)(null),[d,x]=(0,r.useState)(null);(0,r.useEffect)(()=>{h()},[]);let h=async()=>{try{l(!0);let e=localStorage.getItem("admin-token"),s=await fetch("/api/admin/settings",{headers:{Authorization:"Bearer ".concat(e)}}),a=await s.json();a.success?t(a.data):o(a.error||"Failed to load settings")}catch(e){console.error("Settings fetch error:",e),o("Failed to load settings")}finally{l(!1)}},u=async()=>{if(e)try{n(!0),o(null),x(null);let s=localStorage.getItem("admin-token"),a=await fetch("/api/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({defaultLanguage:e.defaultLanguage})}),r=await a.json();r.success?(t(r.data),x("Settings saved successfully!"),setTimeout(()=>x(null),3e3)):o(r.error||"Failed to save settings")}catch(e){console.error("Settings save error:",e),o("Failed to save settings")}finally{n(!1)}},g=s=>{e&&t({...e,defaultLanguage:s})};return s?(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(H.A,{className:"animate-spin text-white",size:32}),(0,a.jsx)("span",{className:"ml-3 text-white",children:"Loading settings..."})]})}):c&&!e?(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-red-400 mb-4",children:"⚠️ Error loading settings"}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:c}),(0,a.jsx)("button",{onClick:h,className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Retry"})]})}):(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(m.A,{className:"text-purple-400 mr-3",size:24}),(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"System Settings"})]}),c&&(0,a.jsx)("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6",children:(0,a.jsx)("p",{className:"text-red-200",children:c})}),d&&(0,a.jsx)("div",{className:"bg-green-500/20 border border-green-500/50 rounded-lg p-4 mb-6",children:(0,a.jsx)("p",{className:"text-green-200",children:d})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-300 text-sm font-medium mb-3",children:"Default Language for New Users"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"This language will be automatically assigned to new users when they are created. Users can change their language preference later."}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 max-w-md",children:[(0,a.jsx)("button",{onClick:()=>g("en"),className:"p-4 rounded-lg border-2 transition-all ".concat((null==e?void 0:e.defaultLanguage)==="en"?"border-purple-500 bg-purple-500/20 text-white":"border-white/20 bg-white/5 text-gray-300 hover:border-white/40"),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDDFA\uD83C\uDDF8"}),(0,a.jsx)("div",{className:"font-medium",children:"English"})]})}),(0,a.jsx)("button",{onClick:()=>g("si"),className:"p-4 rounded-lg border-2 transition-all ".concat((null==e?void 0:e.defaultLanguage)==="si"?"border-purple-500 bg-purple-500/20 text-white":"border-white/20 bg-white/5 text-gray-300 hover:border-white/40"),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDDF1\uD83C\uDDF0"}),(0,a.jsx)("div",{className:"font-medium",children:"සිංහල"})]})})]})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4 border-t border-white/20",children:(0,a.jsx)("button",{onClick:u,disabled:i,className:"flex items-center bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white px-6 py-2 rounded-lg transition-colors",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(H.A,{className:"animate-spin mr-2",size:16}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(J.A,{className:"mr-2",size:16}),"Save Settings"]})})})]}),e&&(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-white/20",children:(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:["Last updated: ",new Date(e.updatedAt).toLocaleString()]})})]})})}var Z=s(17951),V=s(78749);function K(e){let{currentAdmin:t}=e,[s,l]=(0,r.useState)([]),[n,c]=(0,r.useState)(!0),[d,x]=(0,r.useState)(!1),[m,h]=(0,r.useState)(!1),[u,g]=(0,r.useState)(null),[p,j]=(0,r.useState)({name:"",email:"",password:"",role:"admin"}),[f,y]=(0,r.useState)(!1),{confirmDelete:w}=(0,S.G_)(),{showSuccess:v,showError:k}=(0,S.Ue)(),A="super_admin"===t.role;(0,r.useEffect)(()=>{A&&C()},[A]);let C=async()=>{try{c(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/admins",{headers:{Authorization:"Bearer ".concat(e)}}),s=await t.json();s.success?l(s.data):k("Error",s.error||"Failed to fetch admins")}catch(e){console.error("Error fetching admins:",e),k("Error","Failed to fetch admins")}finally{c(!1)}},z=async e=>{if(e.preventDefault(),!p.name||!p.email||!p.password)return void k("Validation Error","All fields are required");try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/admins",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(p)}),a=await t.json();a.success?(l([a.data,...s]),j({name:"",email:"",password:"",role:"admin"}),x(!1),v("Success","Admin created successfully")):k("Error",a.error||"Failed to create admin")}catch(e){console.error("Error creating admin:",e),k("Error","Failed to create admin")}},D=async e=>{let t=s.find(t=>t.id===e),a=(null==t?void 0:t.name)||"this admin";if(await w(a))try{let t=localStorage.getItem("admin-token"),a=await fetch("/api/admin/admins?adminId=".concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}}),r=await a.json();r.success?(l(s.filter(t=>t.id!==e)),v("Success","Admin deleted successfully")):k("Error",r.error||"Failed to delete admin")}catch(e){console.error("Error deleting admin:",e),k("Error","Failed to delete admin")}},E=async(e,t)=>{try{let a=localStorage.getItem("admin-token"),r=await fetch("/api/admin/admins/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify({isActive:!t})}),i=await r.json();i.success?(l(s.map(s=>s.id===e?{...s,isActive:!t}:s)),v("Success","Admin ".concat(t?"deactivated":"activated"," successfully"))):k("Error",i.error||"Failed to update admin status")}catch(e){console.error("Error updating admin status:",e),k("Error","Failed to update admin status")}},T=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),L=e=>"super_admin"===e?(0,a.jsx)(Z.A,{className:"w-4 h-4 text-yellow-400"}):(0,a.jsx)(i.A,{className:"w-4 h-4 text-blue-400"}),P=e=>"super_admin"===e?(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium bg-yellow-500/20 text-yellow-400 rounded-full",children:"Super Admin"}):(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium bg-blue-500/20 text-blue-400 rounded-full",children:"Admin"});return A?n?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(o.A,{className:"w-6 h-6 text-purple-400"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Admin Management"})]}),(0,a.jsxs)("button",{onClick:()=>x(!0),className:"flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200",children:[(0,a.jsx)(b.A,{size:20}),(0,a.jsx)("span",{children:"Add Admin"})]})]}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg border border-white/20",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-white/10",children:[(0,a.jsx)("th",{className:"text-left p-4 text-gray-300 font-medium",children:"Admin"}),(0,a.jsx)("th",{className:"text-left p-4 text-gray-300 font-medium",children:"Role"}),(0,a.jsx)("th",{className:"text-left p-4 text-gray-300 font-medium",children:"Status"}),(0,a.jsx)("th",{className:"text-left p-4 text-gray-300 font-medium",children:"Last Login"}),(0,a.jsx)("th",{className:"text-left p-4 text-gray-300 font-medium",children:"Created By"}),(0,a.jsx)("th",{className:"text-left p-4 text-gray-300 font-medium",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:s.map(e=>(0,a.jsxs)("tr",{className:"border-b border-white/5 hover:bg-white/5",children:[(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[L(e.role),(0,a.jsx)("span",{className:"font-medium text-white",children:e.name}),e.id===t.id&&(0,a.jsx)("span",{className:"text-xs text-purple-400",children:"(You)"})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:e.email})]})}),(0,a.jsx)("td",{className:"p-4",children:P(e.role)}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(e.isActive?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"p-4 text-gray-300 text-sm",children:e.lastLogin?T(e.lastLogin):"Never"}),(0,a.jsx)("td",{className:"p-4 text-gray-300 text-sm",children:e.creator?e.creator.name:"System"}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("div",{className:"flex items-center space-x-2",children:e.id!==t.id&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>E(e.id,e.isActive),className:"p-2 rounded-lg transition-colors ".concat(e.isActive?"text-red-400 hover:bg-red-500/20":"text-green-400 hover:bg-green-500/20"),title:e.isActive?"Deactivate":"Activate",children:e.isActive?(0,a.jsx)(V.A,{size:16}):(0,a.jsx)(R.A,{size:16})}),(0,a.jsx)("button",{onClick:()=>D(e.id),className:"p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors",title:"Delete Admin",children:(0,a.jsx)(N.A,{size:16})})]})})})]},e.id))})]})})}),d&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"Add New Admin"}),(0,a.jsxs)("form",{onSubmit:z,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Name"}),(0,a.jsx)("input",{type:"text",value:p.name,onChange:e=>j({...p,name:e.target.value}),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"Admin name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Email"}),(0,a.jsx)("input",{type:"email",value:p.email,onChange:e=>j({...p,email:e.target.value}),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:f?"text":"password",value:p.password,onChange:e=>j({...p,password:e.target.value}),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500",placeholder:"Password",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>y(!f),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:f?(0,a.jsx)(V.A,{size:16}):(0,a.jsx)(R.A,{size:16})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Role"}),(0,a.jsxs)("select",{value:p.role,onChange:e=>j({...p,role:e.target.value}),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500",children:[(0,a.jsx)("option",{value:"admin",children:"Admin"}),(0,a.jsx)("option",{value:"super_admin",children:"Super Admin"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{x(!1),j({name:"",email:"",password:"",role:"admin"})},className:"flex-1 px-4 py-2 text-gray-300 hover:text-white bg-white/10 hover:bg-white/20 rounded-lg transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all duration-200",children:"Create Admin"})]})]})]})})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(i.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Access Restricted"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Only Super Admins can manage admin accounts."})]})}var Y=s(35084);function X(){let[e,t]=(0,r.useState)(null),[s,g]=(0,r.useState)(null),[p,b]=(0,r.useState)([]),[j,f]=(0,r.useState)(!0),[y,N]=(0,r.useState)(null),[w,v]=(0,r.useState)("overview"),S=(0,l.useRouter)(),{t:A}=(0,Y.LL)();(0,r.useEffect)(()=>{C()},[]);let C=async()=>{try{let e=localStorage.getItem("admin-token");if(!e)return void S.push("/admin/login");let s=await fetch("/api/admin/auth/verify",{headers:{Authorization:"Bearer ".concat(e)}});if(s.ok){let e=await s.json();t(e.data.admin),await z(),await D()}else localStorage.removeItem("admin-token"),localStorage.removeItem("admin-user"),S.push("/admin/login")}catch(e){console.error("Auth check failed:",e),S.push("/admin/login")}},z=async()=>{try{f(!0);let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/stats",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();g(e.data)}else N("Failed to load admin statistics")}catch(e){console.error("Error fetching admin stats:",e),N("Failed to load admin statistics")}finally{f(!1)}},D=async()=>{try{let e=localStorage.getItem("admin-token"),t=await fetch("/api/admin/activity",{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();b(e.data)}else console.error("Failed to load recent activities")}catch(e){console.error("Error fetching activities:",e)}},E=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),localStorage.removeItem("admin-token"),localStorage.removeItem("admin-user"),S.push("/admin/login")}catch(e){console.error("Logout error:",e),localStorage.removeItem("admin-token"),localStorage.removeItem("admin-user"),S.push("/admin/login")}};return j||!e?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,a.jsx)(h.A,{message:"Loading admin dashboard..."})}):y?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,a.jsx)(u.A,{title:A("admin_dashboard_error"),message:y,onRetry:z})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900",children:[(0,a.jsx)("header",{className:"bg-black/20 backdrop-blur-sm border-b border-white/10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:A("astroconnect_admin")}),(0,a.jsx)("p",{className:"text-gray-300",children:A("manage_users_content_analytics")})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:e.email})]}),(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-4 h-4 text-white"})})]}),(0,a.jsxs)("button",{onClick:E,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[(0,a.jsx)(n.A,{size:16}),(0,a.jsx)("span",{children:"Logout"})]})]})]})})}),(0,a.jsx)("nav",{className:"bg-black/10 backdrop-blur-sm border-b border-white/10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,a.jsx)("div",{className:"flex space-x-8",children:[{id:"overview",label:"Overview",icon:c.A},{id:"users",label:"Users",icon:o.A},{id:"content",label:"Content",icon:d.A},{id:"horoscopes",label:"User Horoscopes",icon:x.A},{id:"analytics",label:"Analytics",icon:c.A},...(null==e?void 0:e.role)==="super_admin"?[{id:"admins",label:"Admins",icon:i.A}]:[],{id:"settings",label:"Settings",icon:m.A}].map(e=>{let{id:t,label:s,icon:r}=e;return(0,a.jsxs)("button",{onClick:()=>v(t),className:"flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ".concat(w===t?"border-purple-400 text-white":"border-transparent text-gray-300 hover:text-white"),children:[(0,a.jsx)(r,{size:16}),(0,a.jsx)("span",{children:s})]},t)})})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 py-8",children:["overview"===w&&s&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:A("total_users")}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:s.totalUsers.toLocaleString()})]}),(0,a.jsx)(o.A,{className:"w-8 h-8 text-blue-400"})]})}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:A("active_users")}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:s.activeUsers.toLocaleString()})]}),(0,a.jsx)(o.A,{className:"w-8 h-8 text-green-400"})]})}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:A("total_horoscopes")}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:s.totalHoroscopes.toLocaleString()})]}),(0,a.jsx)(d.A,{className:"w-8 h-8 text-purple-400"})]})}),(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:A("qr_scans")}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white",children:s.totalScans.toLocaleString()})]}),(0,a.jsx)(c.A,{className:"w-8 h-8 text-yellow-400"})]})})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:A("recent_activity")}),(0,a.jsx)("div",{className:"space-y-4",children:p.length>0?p.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-white/10 last:border-b-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:e.action}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:e.user})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:e.time})]},e.id)):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-400",children:A("no_recent_activity")})})})]})]}),"users"===w&&(0,a.jsx)(k,{}),"content"===w&&(0,a.jsx)(B,{}),"horoscopes"===w&&(0,a.jsx)(G,{}),"analytics"===w&&(0,a.jsx)(Q,{}),"admins"===w&&e&&(0,a.jsx)(K,{currentAdmin:e}),"settings"===w&&(0,a.jsx)(M,{})]})]})}},69783:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(95155),r=s(85339),l=s(53904),i=s(35084);function n(e){let{title:t,message:s,onRetry:n,className:c=""}=e,{t:o}=(0,i.LL)(),d=t||o("something_went_wrong");return(0,a.jsxs)("div",{className:"text-center max-w-md mx-auto p-6 ".concat(c),children:[(0,a.jsx)(r.A,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:d}),(0,a.jsx)("p",{className:"text-gray-300 mb-6",children:s}),n&&(0,a.jsxs)("button",{onClick:n,className:"flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors mx-auto",children:[(0,a.jsx)(l.A,{size:16}),o("try_again")]})]})}},92731:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(95155),r=s(51154),l=s(35084);function i(e){let{message:t,size:s="md",className:i=""}=e,{t:n}=(0,l.LL)(),c=t||n("loading_ellipsis");return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(i),children:[(0,a.jsx)(r.A,{className:"".concat({sm:"w-6 h-6",md:"w-12 h-12",lg:"w-16 h-16"}[s]," text-white animate-spin mb-4")}),c&&(0,a.jsx)("p",{className:"text-white text-center",children:c})]})}},93438:(e,t,s)=>{Promise.resolve().then(s.bind(s,67961))}},e=>{var t=t=>e(e.s=t);e.O(0,[9048,5084,8441,1684,7358],()=>t(93438)),_N_E=e.O()}]);