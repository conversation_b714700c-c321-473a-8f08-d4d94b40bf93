"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7145],{27145:(e,t,n)=>{n.r(t),n.d(t,{BarcodeFormat:()=>r.BarcodeFormat,BrowserAztecCodeReader:()=>p,BrowserCodeReader:()=>f,BrowserCodeSvgWriter:()=>_,BrowserDatamatrixCodeReader:()=>g,BrowserMultiFormatOneDReader:()=>v,BrowserMultiFormatReader:()=>E,BrowserPDF417Reader:()=>C,BrowserQRCodeReader:()=>O,BrowserQRCodeSvgWriter:()=>R,HTMLCanvasElementLuminanceSource:()=>i});var r=n(76142),o=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=function(e){function t(n){var r=e.call(this,n.width,n.height)||this;return r.canvas=n,r.tempCanvasElement=null,r.buffer=t.makeBufferFromCanvasImageData(n),r}return o(t,e),t.makeBufferFromCanvasImageData=function(e){try{n=e.getContext("2d",{willReadFrequently:!0})}catch(t){n=e.getContext("2d")}if(!n)throw Error("Couldn't get canvas context.");var n,r=n.getImageData(0,0,e.width,e.height);return t.toGrayscaleBuffer(r.data,e.width,e.height)},t.toGrayscaleBuffer=function(e,t,n){for(var r=new Uint8ClampedArray(t*n),o=0,i=0,a=e.length;o<a;o+=4,i++){var c=void 0;c=0===e[o+3]?255:306*e[o]+601*e[o+1]+117*e[o+2]+512>>10,r[i]=c}return r},t.prototype.getRow=function(e,t){if(e<0||e>=this.getHeight())throw new r.IllegalArgumentException("Requested row is outside the image: "+e);var n=this.getWidth(),o=e*n;return null===t?t=this.buffer.slice(o,o+n):(t.length<n&&(t=new Uint8ClampedArray(n)),t.set(this.buffer.slice(o,o+n))),t},t.prototype.getMatrix=function(){return this.buffer},t.prototype.isCropSupported=function(){return!0},t.prototype.crop=function(t,n,r,o){return e.prototype.crop.call(this,t,n,r,o),this},t.prototype.isRotateSupported=function(){return!0},t.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},t.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},t.prototype.invert=function(){return new r.InvertedLuminanceSource(this)},t.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var e=this.canvas.ownerDocument.createElement("canvas");e.width=this.canvas.width,e.height=this.canvas.height,this.tempCanvasElement=e}return this.tempCanvasElement},t.prototype.rotate=function(e){var n=this.getTempCanvasElement();if(!n)throw Error("Could not create a Canvas element.");var r=e*t.DEGREE_TO_RADIANS,o=this.canvas.width,i=this.canvas.height,a=Math.ceil(Math.abs(Math.cos(r))*o+Math.abs(Math.sin(r))*i),c=Math.ceil(Math.abs(Math.sin(r))*o+Math.abs(Math.cos(r))*i);n.width=a,n.height=c;var u=n.getContext("2d");if(!u)throw Error("Could not create a Canvas Context element.");return u.translate(a/2,c/2),u.rotate(r),u.drawImage(this.canvas,-(o/2),-(i/2)),this.buffer=t.makeBufferFromCanvasImageData(n),this},t.DEGREE_TO_RADIANS=Math.PI/180,t}(r.LuminanceSource);function a(){return"undefined"!=typeof navigator}var c=function(){return(c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},u=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{u(r.next(e))}catch(e){i(e)}}function c(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,c)}u((r=r.apply(e,t||[])).next())})},s=function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(i){return function(c){var u=[i,c];if(n)throw TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=t.call(e,a)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},l=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},d={delayBetweenScanAttempts:500,delayBetweenScanSuccess:500,tryPlayVideoTimeout:5e3},f=function(){function e(e,t,n){void 0===t&&(t=new Map),void 0===n&&(n={}),this.reader=e,this.hints=t,this.options=c(c({},d),n)}return Object.defineProperty(e.prototype,"possibleFormats",{set:function(e){this.hints.set(r.DecodeHintType.POSSIBLE_FORMATS,e)},enumerable:!1,configurable:!0}),e.addVideoSource=function(e,t){try{e.srcObject=t}catch(e){console.error("got interrupted by new loading request")}},e.mediaStreamSetTorch=function(e,t){return u(this,void 0,void 0,function(){return s(this,function(n){switch(n.label){case 0:return[4,e.applyConstraints({advanced:[{fillLightMode:t?"flash":"off",torch:!!t}]})];case 1:return n.sent(),[2]}})})},e.mediaStreamIsTorchCompatible=function(t){var n,r,o=t.getVideoTracks();try{for(var i=l(o),a=i.next();!a.done;a=i.next()){var c=a.value;if(e.mediaStreamIsTorchCompatibleTrack(c))return!0}}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return!1},e.mediaStreamIsTorchCompatibleTrack=function(e){try{var t=e.getCapabilities();return"torch"in t}catch(e){return console.error(e),console.warn("Your browser may be not fully compatible with WebRTC and/or ImageCapture specs. Torch will not be available."),!1}},e.isVideoPlaying=function(e){return e.currentTime>0&&!e.paused&&e.readyState>2},e.getMediaElement=function(e,t){var n=document.getElementById(e);if(!n)throw new r.ArgumentException("element with id '".concat(e,"' not found"));if(n.nodeName.toLowerCase()!==t.toLowerCase())throw new r.ArgumentException("element with id '".concat(e,"' must be an ").concat(t," element"));return n},e.createVideoElement=function(t){if(t instanceof HTMLVideoElement)return t;if("string"==typeof t)return e.getMediaElement(t,"video");if(!t&&"undefined"!=typeof document){var n=document.createElement("video");return n.width=200,n.height=200,n}throw Error("Couldn't get videoElement from videoSource!")},e.prepareImageElement=function(t){if(t instanceof HTMLImageElement)return t;if("string"==typeof t)return e.getMediaElement(t,"img");if(void 0===t){var n=document.createElement("img");return n.width=200,n.height=200,n}throw Error("Couldn't get imageElement from imageSource!")},e.prepareVideoElement=function(t){var n=e.createVideoElement(t);return n.setAttribute("autoplay","true"),n.setAttribute("muted","true"),n.setAttribute("playsinline","true"),n},e.isImageLoaded=function(e){return!!e.complete&&0!==e.naturalWidth},e.createBinaryBitmapFromCanvas=function(e){var t=new i(e),n=new r.HybridBinarizer(t);return new r.BinaryBitmap(n)},e.drawImageOnCanvas=function(e,t){e.drawImage(t,0,0)},e.getMediaElementDimensions=function(e){if(e instanceof HTMLVideoElement)return{height:e.videoHeight,width:e.videoWidth};if(e instanceof HTMLImageElement)return{height:e.naturalHeight||e.height,width:e.naturalWidth||e.width};throw Error("Couldn't find the Source's dimensions!")},e.createCaptureCanvas=function(t){if(!t)throw new r.ArgumentException("Cannot create a capture canvas without a media element.");if("undefined"==typeof document)throw Error('The page "Document" is undefined, make sure you\'re running in a browser.');var n=document.createElement("canvas"),o=e.getMediaElementDimensions(t),i=o.width,a=o.height;return n.style.width=i+"px",n.style.height=a+"px",n.width=i,n.height=a,n},e.tryPlayVideo=function(t){return u(this,void 0,void 0,function(){return s(this,function(n){switch(n.label){case 0:if(null==t?void 0:t.ended)return console.error("Trying to play video that has ended."),[2,!1];if(e.isVideoPlaying(t))return console.warn("Trying to play video that is already playing."),[2,!0];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,t.play()];case 2:return n.sent(),[2,!0];case 3:return console.warn("It was not possible to play the video.",n.sent()),[2,!1];case 4:return[2]}})})},e.createCanvasFromMediaElement=function(t){var n=e.createCaptureCanvas(t),r=n.getContext("2d");if(!r)throw Error("Couldn't find Canvas 2D Context.");return e.drawImageOnCanvas(r,t),n},e.createBinaryBitmapFromMediaElem=function(t){var n=e.createCanvasFromMediaElement(t);return e.createBinaryBitmapFromCanvas(n)},e.destroyImageElement=function(e){e.src="",e.removeAttribute("src"),e=void 0},e.listVideoInputDevices=function(){return u(this,void 0,void 0,function(){var e,t,n,r,o,i,c,u,d,f,h,p;return s(this,function(s){switch(s.label){case 0:if(!a())throw Error("Can't enumerate devices, navigator is not present.");if(!(a()&&navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices))throw Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:e=s.sent(),t=[];try{for(r=(n=l(e)).next();!r.done;r=n.next())o=r.value,i="video"===o.kind?"videoinput":o.kind,"videoinput"===i&&(c=o.deviceId||o.id,u=o.label||"Video device ".concat(t.length+1),d=o.groupId,f={deviceId:c,label:u,kind:i,groupId:d},t.push(f))}catch(e){h={error:e}}finally{try{r&&!r.done&&(p=n.return)&&p.call(n)}finally{if(h)throw h.error}}return[2,t]}})})},e.findDeviceById=function(t){return u(this,void 0,void 0,function(){var n;return s(this,function(r){switch(r.label){case 0:return[4,e.listVideoInputDevices()];case 1:if(!(n=r.sent()))return[2];return[2,n.find(function(e){return e.deviceId===t})]}})})},e.cleanVideoSource=function(e){if(e){try{e.srcObject=null}catch(t){e.src=""}e&&e.removeAttribute("src")}},e.releaseAllStreams=function(){0!==e.streamTracker.length&&e.streamTracker.forEach(function(e){e.getTracks().forEach(function(e){return e.stop()})}),e.streamTracker=[]},e.playVideoOnLoadAsync=function(t,n){return u(this,void 0,void 0,function(){return s(this,function(r){switch(r.label){case 0:return[4,e.tryPlayVideo(t)];case 1:if(r.sent())return[2,!0];return[2,new Promise(function(r,o){var i=setTimeout(function(){e.isVideoPlaying(t)||(o(!1),t.removeEventListener("canplay",a))},n),a=function(){e.tryPlayVideo(t).then(function(e){clearTimeout(i),t.removeEventListener("canplay",a),r(e)})};t.addEventListener("canplay",a)})]}})})},e.attachStreamToVideo=function(t,n,r){return void 0===r&&(r=5e3),u(this,void 0,void 0,function(){var o;return s(this,function(i){switch(i.label){case 0:return o=e.prepareVideoElement(n),e.addVideoSource(o,t),[4,e.playVideoOnLoadAsync(o,r)];case 1:return i.sent(),[2,o]}})})},e._waitImageLoad=function(t){return new Promise(function(n,r){var o=setTimeout(function(){e.isImageLoaded(t)||(t.removeEventListener("load",i),r())},1e4),i=function(){clearTimeout(o),t.removeEventListener("load",i),n()};t.addEventListener("load",i)})},e.checkCallbackFnOrThrow=function(e){if(!e)throw new r.ArgumentException("`callbackFn` is a required parameter, you cannot capture results without it.")},e.disposeMediaStream=function(e){e.getVideoTracks().forEach(function(e){return e.stop()}),e=void 0},e.prototype.decode=function(t){var n=e.createCanvasFromMediaElement(t);return this.decodeFromCanvas(n)},e.prototype.decodeBitmap=function(e){return this.reader.decode(e,this.hints)},e.prototype.decodeFromCanvas=function(t){var n=e.createBinaryBitmapFromCanvas(t);return this.decodeBitmap(n)},e.prototype.decodeFromImageElement=function(t){return u(this,void 0,void 0,function(){var n;return s(this,function(o){switch(o.label){case 0:if(!t)throw new r.ArgumentException("An image element must be provided.");return n=e.prepareImageElement(t),[4,this._decodeOnLoadImage(n)];case 1:return[2,o.sent()]}})})},e.prototype.decodeFromImageUrl=function(t){return u(this,void 0,void 0,function(){var n;return s(this,function(o){switch(o.label){case 0:if(!t)throw new r.ArgumentException("An URL must be provided.");(n=e.prepareImageElement()).src=t,o.label=1;case 1:return o.trys.push([1,,3,4]),[4,this.decodeFromImageElement(n)];case 2:return[2,o.sent()];case 3:return e.destroyImageElement(n),[7];case 4:return[2]}})})},e.prototype.decodeFromConstraints=function(t,n,r){return u(this,void 0,void 0,function(){var o,i;return s(this,function(a){switch(a.label){case 0:return e.checkCallbackFnOrThrow(r),[4,this.getUserMedia(t)];case 1:o=a.sent(),a.label=2;case 2:return a.trys.push([2,4,,5]),[4,this.decodeFromStream(o,n,r)];case 3:return[2,a.sent()];case 4:throw i=a.sent(),e.disposeMediaStream(o),i;case 5:return[2]}})})},e.prototype.decodeFromStream=function(t,n,r){return u(this,void 0,void 0,function(){var o,i,a,d,f,h,p,m,v=this;return s(this,function(y){switch(y.label){case 0:return e.checkCallbackFnOrThrow(r),o=this.options.tryPlayVideoTimeout,[4,e.attachStreamToVideo(t,n,o)];case 1:return i=y.sent(),a=function(){e.disposeMediaStream(t),e.cleanVideoSource(i)},d=this.scan(i,r,a),f=t.getVideoTracks(),h=c(c({},d),{stop:function(){d.stop()},streamVideoConstraintsApply:function(e,t){return u(this,void 0,void 0,function(){var n,r,o,i,a;return s(this,function(c){switch(c.label){case 0:n=t?f.filter(t):f,c.label=1;case 1:c.trys.push([1,6,7,8]),o=(r=l(n)).next(),c.label=2;case 2:if(o.done)return[3,5];return[4,o.value.applyConstraints(e)];case 3:c.sent(),c.label=4;case 4:return o=r.next(),[3,2];case 5:return[3,8];case 6:return i={error:c.sent()},[3,8];case 7:try{o&&!o.done&&(a=r.return)&&a.call(r)}finally{if(i)throw i.error}return[7];case 8:return[2]}})})},streamVideoConstraintsGet:function(e){return f.find(e).getConstraints()},streamVideoSettingsGet:function(e){return f.find(e).getSettings()},streamVideoCapabilitiesGet:function(e){return f.find(e).getCapabilities()}}),e.mediaStreamIsTorchCompatible(t)&&(p=null==f?void 0:f.find(function(t){return e.mediaStreamIsTorchCompatibleTrack(t)}),h.switchTorch=m=function(t){return u(v,void 0,void 0,function(){return s(this,function(n){switch(n.label){case 0:return[4,e.mediaStreamSetTorch(p,t)];case 1:return n.sent(),[2]}})})},h.stop=function(){return u(v,void 0,void 0,function(){return s(this,function(e){switch(e.label){case 0:return d.stop(),[4,m(!1)];case 1:return e.sent(),[2]}})})}),[2,h]}})})},e.prototype.decodeFromVideoDevice=function(t,n,r){return u(this,void 0,void 0,function(){var o,i;return s(this,function(o){switch(o.label){case 0:return e.checkCallbackFnOrThrow(r),i={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeFromConstraints(i,n,r)];case 1:return[2,o.sent()]}})})},e.prototype.decodeFromVideoElement=function(t,n){return u(this,void 0,void 0,function(){var o,i;return s(this,function(a){switch(a.label){case 0:if(e.checkCallbackFnOrThrow(n),!t)throw new r.ArgumentException("A video element must be provided.");return o=e.prepareVideoElement(t),i=this.options.tryPlayVideoTimeout,[4,e.playVideoOnLoadAsync(o,i)];case 1:return a.sent(),[2,this.scan(o,n)]}})})},e.prototype.decodeFromVideoUrl=function(t,n){return u(this,void 0,void 0,function(){var o,i,a;return s(this,function(c){switch(c.label){case 0:if(e.checkCallbackFnOrThrow(n),!t)throw new r.ArgumentException("An URL must be provided.");return(o=e.prepareVideoElement()).src=t,i=function(){e.cleanVideoSource(o)},a=this.options.tryPlayVideoTimeout,[4,e.playVideoOnLoadAsync(o,a)];case 1:return c.sent(),[2,this.scan(o,n,i)]}})})},e.prototype.decodeOnceFromConstraints=function(e,t){return u(this,void 0,void 0,function(){var n;return s(this,function(r){switch(r.label){case 0:return[4,this.getUserMedia(e)];case 1:return n=r.sent(),[4,this.decodeOnceFromStream(n,t)];case 2:return[2,r.sent()]}})})},e.prototype.decodeOnceFromStream=function(t,n){return u(this,void 0,void 0,function(){var r,o;return s(this,function(i){switch(i.label){case 0:return r=!!n,[4,e.attachStreamToVideo(t,n)];case 1:o=i.sent(),i.label=2;case 2:return i.trys.push([2,,4,5]),[4,this.scanOneResult(o)];case 3:return[2,i.sent()];case 4:return r||e.cleanVideoSource(o),[7];case 5:return[2]}})})},e.prototype.decodeOnceFromVideoDevice=function(e,t){return u(this,void 0,void 0,function(){var n,r;return s(this,function(n){switch(n.label){case 0:return r={video:e?{deviceId:{exact:e}}:{facingMode:"environment"}},[4,this.decodeOnceFromConstraints(r,t)];case 1:return[2,n.sent()]}})})},e.prototype.decodeOnceFromVideoElement=function(t){return u(this,void 0,void 0,function(){var n,o;return s(this,function(i){switch(i.label){case 0:if(!t)throw new r.ArgumentException("A video element must be provided.");return n=e.prepareVideoElement(t),o=this.options.tryPlayVideoTimeout,[4,e.playVideoOnLoadAsync(n,o)];case 1:return i.sent(),[4,this.scanOneResult(n)];case 2:return[2,i.sent()]}})})},e.prototype.decodeOnceFromVideoUrl=function(t){return u(this,void 0,void 0,function(){var n,o;return s(this,function(i){switch(i.label){case 0:if(!t)throw new r.ArgumentException("An URL must be provided.");(n=e.prepareVideoElement()).src=t,o=this.decodeOnceFromVideoElement(n),i.label=1;case 1:return i.trys.push([1,,3,4]),[4,o];case 2:return[2,i.sent()];case 3:return e.cleanVideoSource(n),[7];case 4:return[2]}})})},e.prototype.scanOneResult=function(e,t,n,o){var i=this;return void 0===t&&(t=!0),void 0===n&&(n=!0),void 0===o&&(o=!0),new Promise(function(a,c){i.scan(e,function(e,i,u){if(e){a(e),u.stop();return}if(i){if(i instanceof r.NotFoundException&&t||i instanceof r.ChecksumException&&n||i instanceof r.FormatException&&o)return;u.stop(),c(i)}})})},e.prototype.scan=function(t,n,o){var i,a,c=this;e.checkCallbackFnOrThrow(n);var u=e.createCaptureCanvas(t);try{i=u.getContext("2d",{willReadFrequently:!0})}catch(e){i=u.getContext("2d")}if(!i)throw Error("Couldn't create canvas for visual element scan.");var s=function(){i=void 0,u=void 0},l=!1,d={stop:function(){l=!0,clearTimeout(a),s(),o&&o()}},f=function(){if(!l)try{e.drawImageOnCanvas(i,t);var h=c.decodeFromCanvas(u);n(h,void 0,d),a=setTimeout(f,c.options.delayBetweenScanSuccess)}catch(e){n(void 0,e,d);var p=e instanceof r.ChecksumException,m=e instanceof r.FormatException,v=e instanceof r.NotFoundException;if(p||m||v){a=setTimeout(f,c.options.delayBetweenScanAttempts);return}s(),o&&o(e)}};return f(),d},e.prototype._decodeOnLoadImage=function(t){return u(this,void 0,void 0,function(){return s(this,function(n){switch(n.label){case 0:if(e.isImageLoaded(t))return[3,2];return[4,e._waitImageLoad(t)];case 1:n.sent(),n.label=2;case 2:return[2,this.decode(t)]}})})},e.prototype.getUserMedia=function(t){return u(this,void 0,void 0,function(){var n;return s(this,function(r){switch(r.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return n=r.sent(),e.streamTracker.push(n),[2,n]}})})},e.streamTracker=[],e}(),h=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),p=function(e){function t(t,n){return e.call(this,new r.AztecCodeReader,t,n)||this}return h(t,e),t}(f),m=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),v=function(e){function t(t,n){return e.call(this,new r.MultiFormatOneDReader(t),t,n)||this}return m(t,e),t}(f),y=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),g=function(e){function t(t,n){return e.call(this,new r.DataMatrixReader,t,n)||this}return y(t,e),t}(f),w=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),E=function(e){function t(t,n){var o=this,i=new r.MultiFormatReader;return i.setHints(t),(o=e.call(this,i,t,n)||this).reader=i,o}return w(t,e),Object.defineProperty(t.prototype,"possibleFormats",{set:function(e){this.hints.set(r.DecodeHintType.POSSIBLE_FORMATS,e),this.reader.setHints(this.hints)},enumerable:!1,configurable:!0}),t.prototype.decodeBitmap=function(e){return this.reader.decodeWithState(e)},t.prototype.setHints=function(e){this.hints=e,this.reader.setHints(this.hints)},t}(f),b=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),C=function(e){function t(t,n){return e.call(this,new r.PDF417Reader,t,n)||this}return b(t,e),t}(f),S=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),O=function(e){function t(t,n){return e.call(this,new r.QRCodeReader,t,n)||this}return S(t,e),t}(f),T="http://www.w3.org/2000/svg",_=function(){function e(e){if("string"==typeof e){var t=document.getElementById(e);if(!t)throw Error("Could not find a Container element with '".concat(e,"'."));this.containerElement=t}else this.containerElement=e}return e.prototype.write=function(t,n,o,i){if(0===t.length)throw new r.IllegalArgumentException("Found empty contents");if(n<0||o<0)throw new r.IllegalArgumentException("Requested dimensions are too small: "+n+"x"+o);var a=i&&void 0!==i.get(r.EncodeHintType.MARGIN)?Number.parseInt(i.get(r.EncodeHintType.MARGIN).toString(),10):e.QUIET_ZONE_SIZE,c=this.encode(i,t);return this.renderResult(c,n,o,a)},e.prototype.createSVGElement=function(t,n){var r=document.createElementNS(e.SVG_NS,"svg");return r.setAttributeNS(T,"width",n.toString()),r.setAttributeNS(T,"height",t.toString()),r},e.prototype.createSvgPathPlaceholderElement=function(t,n){var r=document.createElementNS(e.SVG_NS,"path");return r.setAttributeNS(T,"d","M0 0h".concat(t,"v").concat(n,"H0z")),r.setAttributeNS(T,"fill","none"),r},e.prototype.createSvgRectElement=function(t,n,r,o){var i=document.createElementNS(e.SVG_NS,"rect");return i.setAttributeNS(T,"x",t.toString()),i.setAttributeNS(T,"y",n.toString()),i.setAttributeNS(T,"height",r.toString()),i.setAttributeNS(T,"width",o.toString()),i.setAttributeNS(T,"fill","#000000"),i},e.prototype.encode=function(e,t){var n=r.QRCodeDecoderErrorCorrectionLevel.L;if(e&&void 0!==e.get(r.EncodeHintType.ERROR_CORRECTION)){var o=e.get(r.EncodeHintType.ERROR_CORRECTION).toString();n=r.QRCodeDecoderErrorCorrectionLevel.fromString(o)}return r.QRCodeEncoder.encode(t,n,e)},e.prototype.renderResult=function(e,t,n,o){var i=e.getMatrix();if(null===i)throw new r.IllegalStateException;var a=i.getWidth(),c=i.getHeight(),u=a+2*o,s=c+2*o,l=Math.max(t,u),d=Math.max(n,s),f=Math.min(Math.floor(l/u),Math.floor(d/s)),h=Math.floor((l-a*f)/2),p=Math.floor((d-c*f)/2),m=this.createSVGElement(l,d),v=this.createSvgPathPlaceholderElement(t,n);m.appendChild(v),this.containerElement.appendChild(m);for(var y=0,g=p;y<c;y++,g+=f)for(var w=0,E=h;w<a;w++,E+=f)if(1===i.get(w,y)){var b=this.createSvgRectElement(E,g,f,f);m.appendChild(b)}return m},e.QUIET_ZONE_SIZE=4,e.SVG_NS="http://www.w3.org/2000/svg",e}(),I="http://www.w3.org/2000/svg",R=function(){function e(){}return e.prototype.write=function(t,n,o,i){if(0===t.length)throw new r.IllegalArgumentException("Found empty contents");if(n<0||o<0)throw new r.IllegalArgumentException("Requested dimensions are too small: "+n+"x"+o);var a=r.QRCodeDecoderErrorCorrectionLevel.L,c=e.QUIET_ZONE_SIZE;if(i){if(void 0!==i.get(r.EncodeHintType.ERROR_CORRECTION)){var u=i.get(r.EncodeHintType.ERROR_CORRECTION).toString();a=r.QRCodeDecoderErrorCorrectionLevel.fromString(u)}void 0!==i.get(r.EncodeHintType.MARGIN)&&(c=Number.parseInt(i.get(r.EncodeHintType.MARGIN).toString(),10))}var s=r.QRCodeEncoder.encode(t,a,i);return this.renderResult(s,n,o,c)},e.prototype.writeToDom=function(e,t,n,r,o){if("string"==typeof e){var i=document.querySelector(e);if(!i)throw Error("Could no find the target HTML element.");e=i}var a=this.write(t,n,r,o);e instanceof HTMLElement&&e.appendChild(a)},e.prototype.renderResult=function(e,t,n,o){var i=e.getMatrix();if(null===i)throw new r.IllegalStateException;for(var a=i.getWidth(),c=i.getHeight(),u=a+2*o,s=c+2*o,l=Math.max(t,u),d=Math.max(n,s),f=Math.min(Math.floor(l/u),Math.floor(d/s)),h=Math.floor((l-a*f)/2),p=Math.floor((d-c*f)/2),m=this.createSVGElement(l,d),v=0,y=p;v<c;v++,y+=f)for(var g=0,w=h;g<a;g++,w+=f)if(1===i.get(g,v)){var E=this.createSvgRectElement(w,y,f,f);m.appendChild(E)}return m},e.prototype.createSVGElement=function(e,t){var n=document.createElementNS(I,"svg"),r=e.toString(),o=t.toString();return n.setAttribute("height",o),n.setAttribute("width",r),n.setAttribute("viewBox","0 0 "+r+" "+o),n},e.prototype.createSvgRectElement=function(e,t,n,r){var o=document.createElementNS(I,"rect");return o.setAttribute("x",e.toString()),o.setAttribute("y",t.toString()),o.setAttribute("height",n.toString()),o.setAttribute("width",r.toString()),o.setAttribute("fill","#000000"),o},e.QUIET_ZONE_SIZE=4,e}()}}]);