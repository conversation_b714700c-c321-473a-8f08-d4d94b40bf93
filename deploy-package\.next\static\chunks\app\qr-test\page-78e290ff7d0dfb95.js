(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2819],{23257:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var l=s(95155),r=s(12115),i=s(16862);function a(){let[e,t]=(0,r.useState)(""),[s,a]=(0,r.useState)("https://www.google.com"),[o,n]=(0,r.useState)("website"),d=()=>{switch(o){case"website":default:return s;case"text":return"Hello, this is just plain text!";case"wifi":return"WIFI:T:WPA;S:MyNetwork;P:MyPassword;;";case"email":return"mailto:<EMAIL>";case"phone":return"tel:+1234567890";case"sms":return"sms:+1234567890:Hello there!"}},c=async()=>{try{let e=d(),s=await i.toDataURL(e,{width:300,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});t(s)}catch(e){console.error("Error generating QR code:",e)}};return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8",children:(0,l.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,l.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"QR Code Error Testing"}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"QR Code Type:"}),(0,l.jsxs)("select",{value:o,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/30 rounded-lg text-white",children:[(0,l.jsx)("option",{value:"website",children:"Website URL"}),(0,l.jsx)("option",{value:"text",children:"Plain Text"}),(0,l.jsx)("option",{value:"wifi",children:"WiFi Credentials"}),(0,l.jsx)("option",{value:"email",children:"Email Address"}),(0,l.jsx)("option",{value:"phone",children:"Phone Number"}),(0,l.jsx)("option",{value:"sms",children:"SMS Message"})]})]}),"website"===o&&(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-white text-sm font-medium mb-2",children:"Website URL:"}),(0,l.jsx)("input",{type:"text",value:s,onChange:e=>a(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/30 rounded-lg text-white placeholder-gray-400",placeholder:"Enter any URL to test error handling"})]}),(0,l.jsx)("div",{className:"bg-gray-500/20 border border-gray-500/30 rounded-lg p-3",children:(0,l.jsxs)("p",{className:"text-gray-300 text-sm",children:[(0,l.jsx)("strong",{children:"Content to encode:"})," ",d()]})}),(0,l.jsx)("button",{onClick:c,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-all",children:"Generate Test QR Code"}),e&&(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-white text-lg font-semibold mb-4",children:"Test QR Code"}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg inline-block",children:(0,l.jsx)("img",{src:e,alt:"Test QR Code",className:"w-64 h-64"})}),(0,l.jsx)("p",{className:"text-gray-300 text-sm mt-4",children:"\uD83D\uDCF1 Scan this QR code with the AstroConnect app to test the error handling"}),(0,l.jsxs)("p",{className:"text-yellow-300 text-xs mt-2",children:["⚠️ This QR code contains: ",d()]})]}),(0,l.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"text-blue-300 font-semibold mb-2",children:"How to Test:"}),(0,l.jsxs)("ol",{className:"text-blue-200 text-sm space-y-1 list-decimal list-inside",children:[(0,l.jsx)("li",{children:"Generate a test QR code above"}),(0,l.jsx)("li",{children:"Go to the main AstroConnect page"}),(0,l.jsx)("li",{children:'Click "Scan Your QR Code"'}),(0,l.jsx)("li",{children:"Scan the test QR code you generated"}),(0,l.jsx)("li",{children:"You should see a user-friendly error message"})]})]}),(0,l.jsx)("div",{className:"text-center",children:(0,l.jsx)("a",{href:"/",className:"inline-block bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-semibold py-2 px-4 rounded-lg transition-all",children:"Go to AstroConnect Main Page"})})]})]})})})}},56407:(e,t,s)=>{Promise.resolve().then(s.bind(s,23257))}},e=>{var t=t=>e(e.s=t);e.O(0,[6862,8441,1684,7358],()=>t(56407)),_N_E=e.O()}]);