"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "runInCleanSnapshot", {
    enumerable: true,
    get: function() {
        return runInCleanSnapshot;
    }
});
const _asynclocalstorage = require("../app-render/async-local-storage");
const runInCleanSnapshot = (0, _asynclocalstorage.createSnapshot)();

//# sourceMappingURL=clean-async-snapshot-instance.js.map