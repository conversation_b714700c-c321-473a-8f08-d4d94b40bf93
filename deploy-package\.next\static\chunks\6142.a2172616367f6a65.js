(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6142],{322:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(){}return t.arraycopy=function(t,e,r,n,o){for(;o--;)r[n++]=t[e++]},t.currentTimeMillis=function(){return Date.now()},t}()},438:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.getNotFoundInstance=function(){return new e},e.kind="NotFoundException",e}(n.A)},667:(t,e,r)=>{"use strict";r.d(e,{$:()=>R});var n=r(39778),o=r(68883),i=r(42563),a=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),s=function(t){function e(e){return t.call(this,e)||this}return a(e,t),e.prototype.encodeCompressedGtin=function(t,e){t.append("(01)");var r=t.length();t.append("9"),this.encodeCompressedGtinWithoutAI(t,e,r)},e.prototype.encodeCompressedGtinWithoutAI=function(t,r,n){for(var o=0;o<4;++o){var i=this.getGeneralDecoder().extractNumericValueFromBitArray(r+10*o,10);i/100==0&&t.append("0"),i/10==0&&t.append("0"),t.append(i)}e.appendCheckDigit(t,n)},e.appendCheckDigit=function(t,e){for(var r=0,n=0;n<13;n++){var o=t.charAt(n+e).charCodeAt(0)-48;r+=(1&n)==0?3*o:o}10==(r=10-r%10)&&(r=0),t.append(r)},e.GTIN_SIZE=40,e}(i.A),u=r(1933),c=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),f=function(t){function e(e){return t.call(this,e)||this}return c(e,t),e.prototype.parseInformation=function(){var t=new u.A;t.append("(01)");var r=t.length(),n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE,4);return t.append(n),this.encodeCompressedGtinWithoutAI(t,e.HEADER_SIZE+4,r),this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE+44)},e.HEADER_SIZE=4,e}(s),h=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),l=function(t){function e(e){return t.call(this,e)||this}return h(e,t),e.prototype.parseInformation=function(){var t=new u.A;return this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE)},e.HEADER_SIZE=5,e}(i.A),d=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),p=function(t){function e(e){return t.call(this,e)||this}return d(e,t),e.prototype.encodeCompressedWeight=function(t,e,r){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e,r);this.addWeightCode(t,n);for(var o=this.checkWeight(n),i=1e5,a=0;a<5;++a)o/i==0&&t.append("0"),i/=10;t.append(o)},e}(s),A=r(438),g=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),y=function(t){function e(e){return t.call(this,e)||this}return g(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+p.GTIN_SIZE+e.WEIGHT_SIZE)throw new A.A;var t=new u.A;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+p.GTIN_SIZE,e.WEIGHT_SIZE),t.toString()},e.HEADER_SIZE=5,e.WEIGHT_SIZE=15,e}(p),w=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),v=function(t){function e(e){return t.call(this,e)||this}return w(e,t),e.prototype.addWeightCode=function(t,e){t.append("(3103)")},e.prototype.checkWeight=function(t){return t},e}(y),_=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),C=function(t){function e(e){return t.call(this,e)||this}return _(e,t),e.prototype.addWeightCode=function(t,e){e<1e4?t.append("(3202)"):t.append("(3203)")},e.prototype.checkWeight=function(t){return t<1e4?t:t-1e4},e}(y),m=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),E=function(t){function e(e){return t.call(this,e)||this}return m(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+s.GTIN_SIZE)throw new A.A;var t=new u.A;this.encodeCompressedGtin(t,e.HEADER_SIZE);var r=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+s.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(392"),t.append(r),t.append(")");var n=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+s.GTIN_SIZE+e.LAST_DIGIT_SIZE,null);return t.append(n.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e}(s),I=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),S=function(t){function e(e){return t.call(this,e)||this}return I(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+s.GTIN_SIZE)throw new A.A;var t=new u.A;this.encodeCompressedGtin(t,e.HEADER_SIZE);var r=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+s.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(393"),t.append(r),t.append(")");var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+s.GTIN_SIZE+e.LAST_DIGIT_SIZE,e.FIRST_THREE_DIGITS_SIZE);n/100==0&&t.append("0"),n/10==0&&t.append("0"),t.append(n);var o=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+s.GTIN_SIZE+e.LAST_DIGIT_SIZE+e.FIRST_THREE_DIGITS_SIZE,null);return t.append(o.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e.FIRST_THREE_DIGITS_SIZE=10,e}(s),T=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),O=function(t){function e(e,r,n){var o=t.call(this,e)||this;return o.dateCode=n,o.firstAIdigits=r,o}return T(e,t),e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE+e.DATE_SIZE)throw new A.A;var t=new u.A;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+e.GTIN_SIZE,e.WEIGHT_SIZE),this.encodeCompressedDate(t,e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE),t.toString()},e.prototype.encodeCompressedDate=function(t,r){var n=this.getGeneralDecoder().extractNumericValueFromBitArray(r,e.DATE_SIZE);if(38400!==n){t.append("("),t.append(this.dateCode),t.append(")");var o=n%32,i=(n/=32)%12+1,a=n/=12;a/10==0&&t.append("0"),t.append(a),i/10==0&&t.append("0"),t.append(i),o/10==0&&t.append("0"),t.append(o)}},e.prototype.addWeightCode=function(t,e){t.append("("),t.append(this.firstAIdigits),t.append(e/1e5),t.append(")")},e.prototype.checkWeight=function(t){return t%1e5},e.HEADER_SIZE=8,e.WEIGHT_SIZE=20,e.DATE_SIZE=16,e}(p);function R(t){try{if(t.get(1))return new f(t);if(!t.get(2))return new l(t);switch(o.A.extractNumericValueFromBitArray(t,1,4)){case 4:return new v(t);case 5:return new C(t)}switch(o.A.extractNumericValueFromBitArray(t,1,5)){case 12:return new E(t);case 13:return new S(t)}switch(o.A.extractNumericValueFromBitArray(t,1,7)){case 56:return new O(t,"310","11");case 57:return new O(t,"320","11");case 58:return new O(t,"310","13");case 59:return new O(t,"320","13");case 60:return new O(t,"310","15");case 61:return new O(t,"320","15");case 62:return new O(t,"310","17");case 63:return new O(t,"320","17")}}catch(e){throw console.log(e),new n.A("unknown decoder: "+t)}}},1933:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(23510);let o=function(){function t(t){void 0===t&&(t=""),this.value=t}return t.prototype.enableDecoding=function(t){return this.encoding=t,this},t.prototype.append=function(t){return"string"==typeof t?this.value+=t.toString():this.encoding?this.value+=n.A.castAsNonUtf8Char(t,this.encoding):this.value+=String.fromCharCode(t),this},t.prototype.appendChars=function(t,e,r){for(var n=e;e<e+r;n++)this.append(t[n]);return this},t.prototype.length=function(){return this.value.length},t.prototype.charAt=function(t){return this.value.charAt(t)},t.prototype.deleteCharAt=function(t){this.value=this.value.substr(0,t)+this.value.substring(t+1)},t.prototype.setCharAt=function(t,e){this.value=this.value.substr(0,t)+e+this.value.substr(t+1)},t.prototype.substring=function(t,e){return this.value.substring(t,e)},t.prototype.setLengthToZero=function(){this.value=""},t.prototype.toString=function(){return this.value},t.prototype.insert=function(t,e){this.value=this.value.substring(0,t)+e+this.value.substring(t)},t}()},2257:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(59612),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.forName=function(t){return this.getCharacterSetECIByName(t)},e}(n.A)},2605:(t,e,r)=>{"use strict";r.d(e,{A:()=>v});var n=r(25969),o=r(79886),i=r(438),a=r(69071),s=r(322),u=r(37391),c=r(13997),f=r(87932),h=r(16067),l=r(22868),d=function(){function t(){}return t.buildBitArray=function(t){var e=2*t.length-1;null==t[t.length-1].getRightChar()&&(e-=1);for(var r=12*e,n=new l.A(r),o=0,i=t[0].getRightChar().getValue(),a=11;a>=0;--a)(i&1<<a)!=0&&n.set(o),o++;for(var a=1;a<t.length;++a){for(var s=t[a],u=s.getLeftChar().getValue(),c=11;c>=0;--c)(u&1<<c)!=0&&n.set(o),o++;if(null!==s.getRightChar())for(var f=s.getRightChar().getValue(),c=11;c>=0;--c)(f&1<<c)!=0&&n.set(o),o++}return n},t}(),p=r(667),A=function(){function t(t,e,r,n){this.leftchar=t,this.rightchar=e,this.finderpattern=r,this.maybeLast=n}return t.prototype.mayBeLast=function(){return this.maybeLast},t.prototype.getLeftChar=function(){return this.leftchar},t.prototype.getRightChar=function(){return this.rightchar},t.prototype.getFinderPattern=function(){return this.finderpattern},t.prototype.mustBeLast=function(){return null==this.rightchar},t.prototype.toString=function(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(null==this.finderpattern?"null":this.finderpattern.getValue())+" ]"},t.equals=function(e,r){return e instanceof t&&t.equalsOrNull(e.leftchar,r.leftchar)&&t.equalsOrNull(e.rightchar,r.rightchar)&&t.equalsOrNull(e.finderpattern,r.finderpattern)},t.equalsOrNull=function(e,r){return null===e?null===r:t.equals(e,r)},t.prototype.hashCode=function(){return this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue()},t}(),g=function(){function t(t,e,r){this.pairs=t,this.rowNumber=e,this.wasReversed=r}return t.prototype.getPairs=function(){return this.pairs},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.isReversed=function(){return this.wasReversed},t.prototype.isEquivalent=function(t){return this.checkEqualitity(this,t)},t.prototype.toString=function(){return"{ "+this.pairs+" }"},t.prototype.equals=function(e,r){return e instanceof t&&this.checkEqualitity(e,r)&&e.wasReversed===r.wasReversed},t.prototype.checkEqualitity=function(t,e){var r;if(t&&e)return t.forEach(function(t,n){e.forEach(function(e){t.getLeftChar().getValue()===e.getLeftChar().getValue()&&t.getRightChar().getValue()===e.getRightChar().getValue()&&t.getFinderPatter().getValue()===e.getFinderPatter().getValue()&&(r=!0)})}),r},t}(),y=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),w=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let v=function(t){function e(){var r=null!==t&&t.apply(this,arguments)||this;return r.pairs=Array(e.MAX_PAIRS),r.rows=[],r.startEnd=[2],r}return y(e,t),e.prototype.decodeRow=function(t,r,n){this.pairs.length=0,this.startFromEven=!1;try{return e.constructResult(this.decodeRow2pairs(t,r))}catch(t){}return this.pairs.length=0,this.startFromEven=!0,e.constructResult(this.decodeRow2pairs(t,r))},e.prototype.reset=function(){this.pairs.length=0,this.rows.length=0},e.prototype.decodeRow2pairs=function(t,e){for(var r,n=!1;!n;)try{this.pairs.push(this.retrieveNextPair(e,this.pairs,t))}catch(t){if(t instanceof i.A){if(!this.pairs.length)throw new i.A;n=!0}}if(this.checkChecksum())return this.pairs;if(r=!!this.rows.length,this.storeRow(t,!1),r){var o=this.checkRowsBoolean(!1);if(null!=o||null!=(o=this.checkRowsBoolean(!0)))return o}throw new i.A},e.prototype.checkRowsBoolean=function(t){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,t&&(this.rows=this.rows.reverse());var e=null;try{e=this.checkRows([],0)}catch(t){console.log(t)}return t&&(this.rows=this.rows.reverse()),e},e.prototype.checkRows=function(t,r){for(var n,o,a=r;a<this.rows.length;a++){var s=this.rows[a];this.pairs.length=0;try{for(var u=(n=void 0,w(t)),c=u.next();!c.done;c=u.next()){var f=c.value;this.pairs.push(f.getPairs())}}catch(t){n={error:t}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}if(this.pairs.push(s.getPairs()),e.isValidSequence(this.pairs)){if(this.checkChecksum())return this.pairs;var h=Array(t);h.push(s);try{return this.checkRows(h,a+1)}catch(t){console.log(t)}}}throw new i.A},e.isValidSequence=function(t){var r,n;try{for(var o=w(e.FINDER_PATTERN_SEQUENCES),i=o.next();!i.done;i=o.next()){var a=i.value;if(!(t.length>a.length)){for(var s=!0,u=0;u<t.length;u++)if(t[u].getFinderPattern().getValue()!==a[u]){s=!1;break}if(s)return!0}}}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!1},e.prototype.storeRow=function(t,r){for(var n=0,o=!1,i=!1;n<this.rows.length;){var a=this.rows[n];if(a.getRowNumber()>t){i=a.isEquivalent(this.pairs);break}o=a.isEquivalent(this.pairs),n++}i||o||e.isPartialRow(this.pairs,this.rows)||(this.rows.push(n,new g(this.pairs,t,r)),this.removePartialRows(this.pairs,this.rows))},e.prototype.removePartialRows=function(t,e){var r,n,o,i,a,s;try{for(var u=w(e),c=u.next();!c.done;c=u.next()){var f=c.value;if(f.getPairs().length!==t.length)try{for(var h=(o=void 0,w(f.getPairs())),l=h.next();!l.done;l=h.next()){var d=l.value,p=!1;try{for(var g=(a=void 0,w(t)),y=g.next();!y.done;y=g.next()){var v=y.value;if(A.equals(d,v))break}}catch(t){a={error:t}}finally{try{y&&!y.done&&(s=g.return)&&s.call(g)}finally{if(a)throw a.error}}}}catch(t){o={error:t}}finally{try{l&&!l.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}},e.isPartialRow=function(t,e){var r,n,o,i,a,s;try{for(var u=w(e),c=u.next();!c.done;c=u.next()){var f=c.value,h=!0;try{for(var l=(o=void 0,w(t)),d=l.next();!d.done;d=l.next()){var p=d.value,A=!1;try{for(var g=(a=void 0,w(f.getPairs())),y=g.next();!y.done;y=g.next()){var v=y.value;if(p.equals(v)){A=!0;break}}}catch(t){a={error:t}}finally{try{y&&!y.done&&(s=g.return)&&s.call(g)}finally{if(a)throw a.error}}if(!A){h=!1;break}}}catch(t){o={error:t}}finally{try{d&&!d.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}if(h)return!0}}catch(t){r={error:t}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}return!1},e.prototype.getRows=function(){return this.rows},e.constructResult=function(t){var e=d.buildBitArray(t),r=(0,p.$)(e).parseInformation(),o=t[0].getFinderPattern().getResultPoints(),i=t[t.length-1].getFinderPattern().getResultPoints(),s=[o[0],o[1],i[0],i[1]];return new a.A(r,null,null,s,n.A.RSS_EXPANDED,null)},e.prototype.checkChecksum=function(){var t=this.pairs.get(0),e=t.getLeftChar(),r=t.getRightChar();if(null===r)return!1;for(var n=r.getChecksumPortion(),o=2,i=1;i<this.pairs.size();++i){var a=this.pairs.get(i);n+=a.getLeftChar().getChecksumPortion(),o++;var s=a.getRightChar();null!=s&&(n+=s.getChecksumPortion(),o++)}return 211*(o-4)+(n%=211)===e.getValue()},e.getNextSecondBar=function(t,e){var r;return t.get(e)?(r=t.getNextUnset(e),r=t.getNextSet(r)):(r=t.getNextSet(e),r=t.getNextUnset(r)),r},e.prototype.retrieveNextPair=function(t,r,n){var o,a,s=r.length%2==0;this.startFromEven&&(s=!s);var u=!0,c=-1;do this.findNextPair(t,r,c),null===(o=this.parseFoundFinderPattern(t,n,s))?c=e.getNextSecondBar(t,this.startEnd[0]):u=!1;while(u);var f=this.decodeDataCharacter(t,o,s,!0);if(!this.isEmptyPair(r)&&r[r.length-1].mustBeLast())throw new i.A;try{a=this.decodeDataCharacter(t,o,s,!1)}catch(t){a=null,console.log(t)}return new A(f,a,o,!0)},e.prototype.isEmptyPair=function(t){return 0===t.length},e.prototype.findNextPair=function(t,r,n){var o,a=this.getDecodeFinderCounters();a[0]=0,a[1]=0,a[2]=0,a[3]=0;var s=t.getSize();o=n>=0?n:this.isEmptyPair(r)?0:r[r.length-1].getFinderPattern().getStartEnd()[1];var u=r.length%2!=0;this.startFromEven&&(u=!u);for(var c=!1;o<s&&(c=!t.get(o));)o++;for(var f=0,h=o,l=o;l<s;l++)if(t.get(l)!==c)a[f]++;else{if(3===f){if(u&&e.reverseCounters(a),e.isFinderPattern(a)){this.startEnd[0]=h,this.startEnd[1]=l;return}u&&e.reverseCounters(a),h+=a[0]+a[1],a[0]=a[2],a[1]=a[3],a[2]=0,a[3]=0,f--}else f++;a[f]=1,c=!c}throw new i.A},e.reverseCounters=function(t){for(var e=t.length,r=0;r<e/2;++r){var n=t[r];t[r]=t[e-r-1],t[e-r-1]=n}},e.prototype.parseFoundFinderPattern=function(t,r,n){if(n){for(var o,i,a,u,c=this.startEnd[0]-1;c>=0&&!t.get(c);)c--;c++,o=this.startEnd[0]-c,i=c,a=this.startEnd[1]}else i=this.startEnd[0],o=(a=t.getNextUnset(this.startEnd[1]+1))-this.startEnd[1];var h=this.getDecodeFinderCounters();s.A.arraycopy(h,0,h,1,h.length-1),h[0]=o;try{u=this.parseFinderValue(h,e.FINDER_PATTERNS)}catch(t){return null}return new f.A(u,[i,a],i,a,r)},e.prototype.decodeDataCharacter=function(t,r,n,a){for(var s=this.getDataCharacterCounters(),u=0;u<s.length;u++)s[u]=0;if(a)e.recordPatternInReverse(t,r.getStartEnd()[0],s);else{e.recordPattern(t,r.getStartEnd()[1],s);for(var f=0,l=s.length-1;f<l;f++,l--){var d=s[f];s[f]=s[l],s[l]=d}}var p=o.A.sum(new Int32Array(s))/17,A=(r.getStartEnd()[1]-r.getStartEnd()[0])/15;if(Math.abs(p-A)/A>.3)throw new i.A;for(var g=this.getOddCounts(),y=this.getEvenCounts(),w=this.getOddRoundingErrors(),v=this.getEvenRoundingErrors(),f=0;f<s.length;f++){var _=s[f]/p,C=_+.5;if(C<1){if(_<.3)throw new i.A;C=1}else if(C>8){if(_>8.7)throw new i.A;C=8}var m=f/2;(1&f)==0?(g[m]=C,w[m]=_-C):(y[m]=C,v[m]=_-C)}this.adjustOddEvenCounts(17);for(var E=4*r.getValue()+2*!n+ +!a-1,I=0,S=0,f=g.length-1;f>=0;f--){if(e.isNotA1left(r,n,a)){var T=e.WEIGHTS[E][2*f];S+=g[f]*T}I+=g[f]}for(var O=0,f=y.length-1;f>=0;f--)if(e.isNotA1left(r,n,a)){var T=e.WEIGHTS[E][2*f+1];O+=y[f]*T}var R=S+O;if((1&I)!=0||I>13||I<4)throw new i.A;var b=(13-I)/2,N=e.SYMBOL_WIDEST[b],D=h.A.getRSSvalue(g,N,!0),M=h.A.getRSSvalue(y,9-N,!1),P=e.EVEN_TOTAL_SUBSET[b],B=e.GSUM[b];return new c.A(D*P+M+B,R)},e.isNotA1left=function(t,e,r){return!(0===t.getValue()&&e&&r)},e.prototype.adjustOddEvenCounts=function(t){var r=o.A.sum(new Int32Array(this.getOddCounts())),n=o.A.sum(new Int32Array(this.getEvenCounts())),a=!1,s=!1;r>13?s=!0:r<4&&(a=!0);var u=!1,c=!1;n>13?c=!0:n<4&&(u=!0);var f=r+n-t,h=(1&r)==1,l=(1&n)==0;if(1===f)if(h){if(l)throw new i.A;s=!0}else{if(!l)throw new i.A;c=!0}else if(-1===f)if(h){if(l)throw new i.A;a=!0}else{if(!l)throw new i.A;u=!0}else if(0===f){if(h){if(!l)throw new i.A;r<n?(a=!0,c=!0):(s=!0,u=!0)}else if(l)throw new i.A}else throw new i.A;if(a){if(s)throw new i.A;e.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(s&&e.decrement(this.getOddCounts(),this.getOddRoundingErrors()),u){if(c)throw new i.A;e.increment(this.getEvenCounts(),this.getOddRoundingErrors())}c&&e.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.SYMBOL_WIDEST=[7,5,4,3,1],e.EVEN_TOTAL_SUBSET=[4,20,52,104,204],e.GSUM=[0,348,1388,2948,3988],e.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],e.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],e.FINDER_PAT_A=0,e.FINDER_PAT_B=1,e.FINDER_PAT_C=2,e.FINDER_PAT_D=3,e.FINDER_PAT_E=4,e.FINDER_PAT_F=5,e.FINDER_PATTERN_SEQUENCES=[[e.FINDER_PAT_A,e.FINDER_PAT_A],[e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B],[e.FINDER_PAT_A,e.FINDER_PAT_C,e.FINDER_PAT_B,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_C],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F]],e.MAX_PAIRS=11,e}(u.A)},3411:(t,e,r)=>{"use strict";r.d(e,{A:()=>p});var n=r(25969),o=r(79417),i=r(71534),a=r(438),s=r(69071),u=r(56595),c=r(1933),f=r(322),h=r(22152),l=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),d=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.narrowLineWidth=-1,e}return l(e,t),e.prototype.decodeRow=function(t,r,a){var f,h,l=this.decodeStart(r),p=this.decodeEnd(r),A=new c.A;e.decodeMiddle(r,l[1],p[0],A);var g=A.toString(),y=null;null!=a&&(y=a.get(o.A.ALLOWED_LENGTHS)),null==y&&(y=e.DEFAULT_ALLOWED_LENGTHS);var w=g.length,v=!1,_=0;try{for(var C=d(y),m=C.next();!m.done;m=C.next()){var E=m.value;if(w===E){v=!0;break}E>_&&(_=E)}}catch(t){f={error:t}}finally{try{m&&!m.done&&(h=C.return)&&h.call(C)}finally{if(f)throw f.error}}if(!v&&w>_&&(v=!0),!v)throw new i.A;var I=[new u.A(l[1],t),new u.A(p[0],t)];return new s.A(g,null,0,I,n.A.ITF,new Date().getTime())},e.decodeMiddle=function(t,r,n,o){var i=new Int32Array(10),a=new Int32Array(5),s=new Int32Array(5);for(i.fill(0),a.fill(0),s.fill(0);r<n;){h.A.recordPattern(t,r,i);for(var u=0;u<5;u++){var c=2*u;a[u]=i[c],s[u]=i[c+1]}var f=e.decodeDigit(a);o.append(f.toString()),f=this.decodeDigit(s),o.append(f.toString()),i.forEach(function(t){r+=t})}},e.prototype.decodeStart=function(t){var r=e.skipWhiteSpace(t),n=e.findGuardPattern(t,r,e.START_PATTERN);return this.narrowLineWidth=(n[1]-n[0])/4,this.validateQuietZone(t,n[0]),n},e.prototype.validateQuietZone=function(t,e){var r=10*this.narrowLineWidth;r=r<e?r:e;for(var n=e-1;r>0&&n>=0&&!t.get(n);n--)r--;if(0!==r)throw new a.A},e.skipWhiteSpace=function(t){var e=t.getSize(),r=t.getNextSet(0);if(r===e)throw new a.A;return r},e.prototype.decodeEnd=function(t){t.reverse();try{var r=e.skipWhiteSpace(t),n=void 0;try{n=e.findGuardPattern(t,r,e.END_PATTERN_REVERSED[0])}catch(o){o instanceof a.A&&(n=e.findGuardPattern(t,r,e.END_PATTERN_REVERSED[1]))}this.validateQuietZone(t,n[0]);var o=n[0];return n[0]=t.getSize()-n[1],n[1]=t.getSize()-o,n}finally{t.reverse()}},e.findGuardPattern=function(t,r,n){var o=n.length,i=new Int32Array(o),s=t.getSize(),u=!1,c=0,l=r;i.fill(0);for(var d=r;d<s;d++)if(t.get(d)!==u)i[c]++;else{if(c===o-1){if(h.A.patternMatchVariance(i,n,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return[l,d];l+=i[0]+i[1],f.A.arraycopy(i,2,i,0,c-1),i[c-1]=0,i[c]=0,c--}else c++;i[c]=1,u=!u}throw new a.A},e.decodeDigit=function(t){for(var r=e.MAX_AVG_VARIANCE,n=-1,o=e.PATTERNS.length,i=0;i<o;i++){var s=e.PATTERNS[i],u=h.A.patternMatchVariance(t,s,e.MAX_INDIVIDUAL_VARIANCE);u<r?(r=u,n=i):u===r&&(n=-1)}if(n>=0)return n%10;throw new a.A},e.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],e.MAX_AVG_VARIANCE=.38,e.MAX_INDIVIDUAL_VARIANCE=.5,e.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],e.START_PATTERN=Int32Array.from([1,1,1,1]),e.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])],e}(h.A)},3451:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="UnsupportedOperationException",e}(n.A)},5106:(t,e,r)=>{"use strict";r.d(e,{w:()=>a});var n=r(11623),o=r(41205),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new o.A,e)||this}return i(e,t),e}(n.J)},6537:(t,e,r)=>{"use strict";r.d(e,{A:()=>c});var n=r(22868),o=r(63479),i=r(6974),a=r(85808),s=r(93682),u=r(38988);let c=function(){function t(){}return t.clearMatrix=function(t){t.clear(255)},t.buildMatrix=function(e,r,n,o,i){t.clearMatrix(i),t.embedBasicPatterns(n,i),t.embedTypeInfo(r,o,i),t.maybeEmbedVersionInfo(n,i),t.embedDataBits(e,o,i)},t.embedBasicPatterns=function(e,r){t.embedPositionDetectionPatternsAndSeparators(r),t.embedDarkDotAtLeftBottomCorner(r),t.maybeEmbedPositionAdjustmentPatterns(e,r),t.embedTimingPatterns(r)},t.embedTypeInfo=function(e,r,o){var i=new n.A;t.makeTypeInfoBits(e,r,i);for(var a=0,s=i.getSize();a<s;++a){var u=i.get(i.getSize()-1-a),c=t.TYPE_INFO_COORDINATES[a],f=c[0],h=c[1];if(o.setBoolean(f,h,u),a<8){var l=o.getWidth()-a-1,d=8;o.setBoolean(l,d,u)}else{var l=8,d=o.getHeight()-7+(a-8);o.setBoolean(l,d,u)}}},t.maybeEmbedVersionInfo=function(e,r){if(!(7>e.getVersionNumber())){var o=new n.A;t.makeVersionInfoBits(e,o);for(var i=17,a=0;a<6;++a)for(var s=0;s<3;++s){var u=o.get(i);i--,r.setBoolean(a,r.getHeight()-11+s,u),r.setBoolean(r.getHeight()-11+s,a,u)}}},t.embedDataBits=function(e,r,n){for(var o=0,i=-1,u=n.getWidth()-1,c=n.getHeight()-1;u>0;){for(6===u&&(u-=1);c>=0&&c<n.getHeight();){for(var f=0;f<2;++f){var h=u-f;if(t.isEmpty(n.get(h,c))){var l=void 0;o<e.getSize()?(l=e.get(o),++o):l=!1,255!==r&&a.A.getDataMaskBit(r,h,c)&&(l=!l),n.setBoolean(h,c,l)}}c+=i}c+=i=-i,u-=2}if(o!==e.getSize())throw new s.A("Not all bits consumed: "+o+"/"+e.getSize())},t.findMSBSet=function(t){return 32-o.A.numberOfLeadingZeros(t)},t.calculateBCHCode=function(e,r){if(0===r)throw new u.A("0 polynomial");var n=t.findMSBSet(r);for(e<<=n-1;t.findMSBSet(e)>=n;)e^=r<<t.findMSBSet(e)-n;return e},t.makeTypeInfoBits=function(e,r,o){if(!i.A.isValidMaskPattern(r))throw new s.A("Invalid mask pattern");var a=e.getBits()<<3|r;o.appendBits(a,5);var u=t.calculateBCHCode(a,t.TYPE_INFO_POLY);o.appendBits(u,10);var c=new n.A;if(c.appendBits(t.TYPE_INFO_MASK_PATTERN,15),o.xor(c),15!==o.getSize())throw new s.A("should not happen but we got: "+o.getSize())},t.makeVersionInfoBits=function(e,r){r.appendBits(e.getVersionNumber(),6);var n=t.calculateBCHCode(e.getVersionNumber(),t.VERSION_INFO_POLY);if(r.appendBits(n,12),18!==r.getSize())throw new s.A("should not happen but we got: "+r.getSize())},t.isEmpty=function(t){return 255===t},t.embedTimingPatterns=function(e){for(var r=8;r<e.getWidth()-8;++r){var n=(r+1)%2;t.isEmpty(e.get(r,6))&&e.setNumber(r,6,n),t.isEmpty(e.get(6,r))&&e.setNumber(6,r,n)}},t.embedDarkDotAtLeftBottomCorner=function(t){if(0===t.get(8,t.getHeight()-8))throw new s.A;t.setNumber(8,t.getHeight()-8,1)},t.embedHorizontalSeparationPattern=function(e,r,n){for(var o=0;o<8;++o){if(!t.isEmpty(n.get(e+o,r)))throw new s.A;n.setNumber(e+o,r,0)}},t.embedVerticalSeparationPattern=function(e,r,n){for(var o=0;o<7;++o){if(!t.isEmpty(n.get(e,r+o)))throw new s.A;n.setNumber(e,r+o,0)}},t.embedPositionAdjustmentPattern=function(e,r,n){for(var o=0;o<5;++o)for(var i=t.POSITION_ADJUSTMENT_PATTERN[o],a=0;a<5;++a)n.setNumber(e+a,r+o,i[a])},t.embedPositionDetectionPattern=function(e,r,n){for(var o=0;o<7;++o)for(var i=t.POSITION_DETECTION_PATTERN[o],a=0;a<7;++a)n.setNumber(e+a,r+o,i[a])},t.embedPositionDetectionPatternsAndSeparators=function(e){var r=t.POSITION_DETECTION_PATTERN[0].length;t.embedPositionDetectionPattern(0,0,e),t.embedPositionDetectionPattern(e.getWidth()-r,0,e),t.embedPositionDetectionPattern(0,e.getWidth()-r,e),t.embedHorizontalSeparationPattern(0,7,e),t.embedHorizontalSeparationPattern(e.getWidth()-8,7,e),t.embedHorizontalSeparationPattern(0,e.getWidth()-8,e),t.embedVerticalSeparationPattern(7,0,e),t.embedVerticalSeparationPattern(e.getHeight()-7-1,0,e),t.embedVerticalSeparationPattern(7,e.getHeight()-7,e)},t.maybeEmbedPositionAdjustmentPatterns=function(e,r){if(!(2>e.getVersionNumber()))for(var n=e.getVersionNumber()-1,o=t.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n],i=0,a=o.length;i!==a;i++){var s=o[i];if(s>=0)for(var u=0;u!==a;u++){var c=o[u];c>=0&&t.isEmpty(r.get(c,s))&&t.embedPositionAdjustmentPattern(c-2,s-2,r)}}},t.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),t.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),t.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),t.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),t.VERSION_INFO_POLY=7973,t.TYPE_INFO_POLY=1335,t.TYPE_INFO_MASK_PATTERN=21522,t}()},6727:(t,e,r)=>{"use strict";r.d(e,{A:()=>c});var n=r(322),o=r(38988),i=r(52067),a=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),s=function(t){function e(e,r){void 0===e&&(e=void 0),void 0===r&&(r=void 0);var n=t.call(this,r)||this;return n.index=e,n.message=r,n}return a(e,t),e.kind="ArrayIndexOutOfBoundsException",e}(i.A),u=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let c=function(){function t(){}return t.fill=function(t,e){for(var r=0,n=t.length;r<n;r++)t[r]=e},t.fillWithin=function(e,r,n,o){t.rangeCheck(e.length,r,n);for(var i=r;i<n;i++)e[i]=o},t.rangeCheck=function(t,e,r){if(e>r)throw new o.A("fromIndex("+e+") > toIndex("+r+")");if(e<0)throw new s(e);if(r>t)throw new s(r)},t.asList=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t},t.create=function(t,e,r){return Array.from({length:t}).map(function(t){return Array.from({length:e}).fill(r)})},t.createInt32Array=function(t,e,r){return Array.from({length:t}).map(function(t){return Int32Array.from({length:e}).fill(r)})},t.equals=function(t,e){if(!t||!e||!t.length||!e.length||t.length!==e.length)return!1;for(var r=0,n=t.length;r<n;r++)if(t[r]!==e[r])return!1;return!0},t.hashCode=function(t){if(null===t)return 0;var e,r,n=1;try{for(var o=u(t),i=o.next();!i.done;i=o.next()){var a=i.value;n=31*n+a}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},t.fillUint8Array=function(t,e){for(var r=0;r!==t.length;r++)t[r]=e},t.copyOf=function(t,e){return t.slice(0,e)},t.copyOfUint8Array=function(t,e){if(t.length<=e){var r=new Uint8Array(e);return r.set(t),r}return t.slice(0,e)},t.copyOfRange=function(t,e,r){var o=r-e,i=new Int32Array(o);return n.A.arraycopy(t,e,i,0,o),i},t.binarySearch=function(e,r,n){void 0===n&&(n=t.numberComparator);for(var o=0,i=e.length-1;o<=i;){var a=i+o>>1,s=n(r,e[a]);if(s>0)o=a+1;else{if(!(s<0))return a;i=a-1}}return-o-1},t.numberComparator=function(t,e){return t-e},t}()},6974:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(1933);let o=function(){function t(){this.maskPattern=-1}return t.prototype.getMode=function(){return this.mode},t.prototype.getECLevel=function(){return this.ecLevel},t.prototype.getVersion=function(){return this.version},t.prototype.getMaskPattern=function(){return this.maskPattern},t.prototype.getMatrix=function(){return this.matrix},t.prototype.toString=function(){var t=new n.A;return t.append("<<\n"),t.append(" mode: "),t.append(this.mode?this.mode.toString():"null"),t.append("\n ecLevel: "),t.append(this.ecLevel?this.ecLevel.toString():"null"),t.append("\n version: "),t.append(this.version?this.version.toString():"null"),t.append("\n maskPattern: "),t.append(this.maskPattern.toString()),this.matrix?(t.append("\n matrix:\n"),t.append(this.matrix.toString())):t.append("\n matrix: null\n"),t.append(">>\n"),t.toString()},t.prototype.setMode=function(t){this.mode=t},t.prototype.setECLevel=function(t){this.ecLevel=t},t.prototype.setVersion=function(t){this.version=t},t.prototype.setMaskPattern=function(t){this.maskPattern=t},t.prototype.setMatrix=function(t){this.matrix=t},t.isValidMaskPattern=function(e){return e>=0&&e<t.NUM_MASK_PATTERNS},t.NUM_MASK_PATTERNS=8,t}()},7779:(t,e,r)=>{"use strict";r.d(e,{A:()=>d});var n=r(58892),o=r(27323),i=r(39894),a=r(93782),s=r(25879),u=r(49202),c=r(9568),f=r(48798),h=r(6727),l=r(63479);let d=function(){function t(){}return t.randomize253State=function(t){var e=a.Qw+(149*t%253+1);return e<=254?e:e-254},t.encodeHighLevel=function(t,e,r,h,l){void 0===e&&(e=0),void 0===r&&(r=null),void 0===h&&(h=null),void 0===l&&(l=!1);var d=new i.S,p=[new n.a,d,new f._,new c.y,new s.b,new o.B],A=new u.Q(t);A.setSymbolShape(e),A.setSizeConstraints(r,h),t.startsWith(a.h_)&&t.endsWith(a.TG)?(A.writeCodeword(a.tf),A.setSkipAtEnd(2),A.pos+=a.h_.length):t.startsWith(a.eB)&&t.endsWith(a.TG)&&(A.writeCodeword(a.mD),A.setSkipAtEnd(2),A.pos+=a.eB.length);var g=a.d2;for(l&&(d.encodeMaximal(A),g=A.getNewEncoding(),A.resetEncoderSignal());A.hasMoreCharacters();)p[g].encode(A),A.getNewEncoding()>=0&&(g=A.getNewEncoding(),A.resetEncoderSignal());var y=A.getCodewordCount();A.updateSymbolInfo();var w=A.getSymbolInfo().getDataCapacity();y<w&&g!==a.d2&&g!==a.mt&&g!==a.uf&&A.writeCodeword("\xfe");var v=A.getCodewords();for(v.length()<w&&v.append(a.Qw);v.length()<w;)v.append(this.randomize253State(v.length()+1));return A.getCodewords().toString()},t.lookAheadTest=function(t,e,r){var n=this.lookAheadTestIntern(t,e,r);if(r===a.VK&&n===a.VK){for(var o=Math.min(e+3,t.length),i=e;i<o;i++)if(!this.isNativeX12(t.charCodeAt(i)))return a.d2}else if(r===a.uf&&n===a.uf){for(var o=Math.min(e+4,t.length),i=e;i<o;i++)if(!this.isNativeEDIFACT(t.charCodeAt(i)))return a.d2}return n},t.lookAheadTestIntern=function(t,e,r){if(e>=t.length)return r;r===a.d2?n=[0,1,1,1,1,1.25]:(n=[1,2,2,2,2,2.25])[r]=0;for(var n,o=0,i=new Uint8Array(6),s=[];;){if(e+o===t.length){h.A.fill(i,0),h.A.fill(s,0);var u=this.findMinimums(n,s,l.A.MAX_VALUE,i),c=this.getMinimumCount(i);if(s[a.d2]===u)return a.d2;if(1===c){if(i[a.mt]>0)return a.mt;if(i[a.uf]>0)return a.uf;if(i[a.VL]>0)return a.VL;if(i[a.VK]>0)return a.VK}return a.fG}var f=t.charCodeAt(e+o);if(o++,this.isDigit(f)?n[a.d2]+=.5:this.isExtendedASCII(f)?(n[a.d2]=Math.ceil(n[a.d2]),n[a.d2]+=2):(n[a.d2]=Math.ceil(n[a.d2]),n[a.d2]++),this.isNativeC40(f)?n[a.fG]+=2/3:this.isExtendedASCII(f)?n[a.fG]+=8/3:n[a.fG]+=4/3,this.isNativeText(f)?n[a.VL]+=2/3:this.isExtendedASCII(f)?n[a.VL]+=8/3:n[a.VL]+=4/3,this.isNativeX12(f)?n[a.VK]+=2/3:this.isExtendedASCII(f)?n[a.VK]+=13/3:n[a.VK]+=10/3,this.isNativeEDIFACT(f)?n[a.uf]+=.75:this.isExtendedASCII(f)?n[a.uf]+=4.25:n[a.uf]+=3.25,this.isSpecialB256(f)?n[a.mt]+=4:n[a.mt]++,o>=4){if(h.A.fill(i,0),h.A.fill(s,0),this.findMinimums(n,s,l.A.MAX_VALUE,i),s[a.d2]<this.min(s[a.mt],s[a.fG],s[a.VL],s[a.VK],s[a.uf]))return a.d2;if(s[a.mt]<s[a.d2]||s[a.mt]+1<this.min(s[a.fG],s[a.VL],s[a.VK],s[a.uf]))return a.mt;if(s[a.uf]+1<this.min(s[a.mt],s[a.fG],s[a.VL],s[a.VK],s[a.d2]))return a.uf;if(s[a.VL]+1<this.min(s[a.mt],s[a.fG],s[a.uf],s[a.VK],s[a.d2]))return a.VL;if(s[a.VK]+1<this.min(s[a.mt],s[a.fG],s[a.uf],s[a.VL],s[a.d2]))return a.VK;if(s[a.fG]+1<this.min(s[a.d2],s[a.mt],s[a.uf],s[a.VL])){if(s[a.fG]<s[a.VK])return a.fG;if(s[a.fG]===s[a.VK]){for(var d=e+o+1;d<t.length;){var p=t.charCodeAt(d);if(this.isX12TermSep(p))return a.VK;if(!this.isNativeX12(p))break;d++}return a.fG}}}}},t.min=function(t,e,r,n,o){var i=Math.min(t,Math.min(e,Math.min(r,n)));return void 0===o?i:Math.min(i,o)},t.findMinimums=function(t,e,r,n){for(var o=0;o<6;o++){var i=e[o]=Math.ceil(t[o]);r>i&&(r=i,h.A.fill(n,0)),r===i&&(n[o]=n[o]+1)}return r},t.getMinimumCount=function(t){for(var e=0,r=0;r<6;r++)e+=t[r];return e||0},t.isDigit=function(t){return t>=48&&t<=57},t.isExtendedASCII=function(t){return t>=128&&t<=255},t.isNativeC40=function(t){return 32===t||t>=48&&t<=57||t>=65&&t<=90},t.isNativeText=function(t){return 32===t||t>=48&&t<=57||t>=97&&t<=122},t.isNativeX12=function(t){return this.isX12TermSep(t)||32===t||t>=48&&t<=57||t>=65&&t<=90},t.isX12TermSep=function(t){return 13===t||42===t||62===t},t.isNativeEDIFACT=function(t){return t>=32&&t<=94},t.isSpecialB256=function(t){return!1},t.determineConsecutiveDigitCount=function(t,e){void 0===e&&(e=0);for(var r=t.length,n=e;n<r&&this.isDigit(t.charCodeAt(n));)n++;return n-e},t.illegalCharacter=function(t){var e=l.A.toHexString(t.charCodeAt(0));throw Error("Illegal character: "+t+" (0x"+(e="0000".substring(0,4-e.length)+e)+")")},t}()},8588:(t,e,r)=>{"use strict";r.d(e,{u:()=>a});var n=r(11623),o=r(72106),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new o.A,e)||this}return i(e,t),e}(n.J)},9568:(t,e,r)=>{"use strict";r.d(e,{y:()=>c});var n=r(23510),o=r(1933),i=r(39894),a=r(7779),s=r(93782),u=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return u(e,t),e.prototype.getEncodingMode=function(){return s.VK},e.prototype.encode=function(t){for(var e=new o.A;t.hasMoreCharacters();){var r=t.getCurrentChar();if(t.pos++,this.encodeChar(r,e),e.length()%3==0&&(this.writeNextTriplet(t,e),a.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode())){t.signalEncoderChange(s.d2);break}}this.handleEOD(t,e)},e.prototype.encodeChar=function(t,e){switch(t){case 13:e.append(0);break;case 42:e.append(1);break;case 62:e.append(2);break;case 32:e.append(3);break;default:t>=48&&t<=57?e.append(t-48+4):t>=65&&t<=90?e.append(t-65+14):a.A.illegalCharacter(n.A.getCharAt(t))}return 1},e.prototype.handleEOD=function(t,e){t.updateSymbolInfo();var r=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),n=e.length();t.pos-=n,(t.getRemainingCharacters()>1||r>1||t.getRemainingCharacters()!==r)&&t.writeCodeword(s.OM),0>t.getNewEncoding()&&t.signalEncoderChange(s.d2)},e}(i.S)},10646:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="ReaderException",e}(n.A)},10659:(t,e,r)=>{"use strict";r.d(e,{A:()=>_});var n=r(37391),o=r(13997),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.count=0,o.finderPattern=n,o}return i(e,t),e.prototype.getFinderPattern=function(){return this.finderPattern},e.prototype.getCount=function(){return this.count},e.prototype.incrementCount=function(){this.count++},e}(o.A),s=r(69071),u=r(79417),c=r(438),f=r(1933),h=r(25969),l=r(56595),d=r(87932),p=r(79886),A=r(16067),g=r(322),y=r(22152),w=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),v=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let _=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.possibleLeftPairs=[],e.possibleRightPairs=[],e}return w(e,t),e.prototype.decodeRow=function(t,r,n){var o,i,a,s,u=this.decodePair(r,!1,t,n);e.addOrTally(this.possibleLeftPairs,u),r.reverse();var f=this.decodePair(r,!0,t,n);e.addOrTally(this.possibleRightPairs,f),r.reverse();try{for(var h=v(this.possibleLeftPairs),l=h.next();!l.done;l=h.next()){var d=l.value;if(d.getCount()>1)try{for(var p=(a=void 0,v(this.possibleRightPairs)),A=p.next();!A.done;A=p.next()){var g=A.value;if(g.getCount()>1&&e.checkChecksum(d,g))return e.constructResult(d,g)}}catch(t){a={error:t}}finally{try{A&&!A.done&&(s=p.return)&&s.call(p)}finally{if(a)throw a.error}}}}catch(t){o={error:t}}finally{try{l&&!l.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}throw new c.A},e.addOrTally=function(t,e){if(null!=e){var r,n,o=!1;try{for(var i=v(t),a=i.next();!a.done;a=i.next()){var s=a.value;if(s.getValue()===e.getValue()){s.incrementCount(),o=!0;break}}}catch(t){r={error:t}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}o||t.push(e)}},e.prototype.reset=function(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0},e.constructResult=function(t,e){for(var r=new String(4537077*t.getValue()+e.getValue()).toString(),n=new f.A,o=13-r.length;o>0;o--)n.append("0");n.append(r);for(var i=0,o=0;o<13;o++){var a=n.charAt(o).charCodeAt(0)-48;i+=(1&o)==0?3*a:a}10==(i=10-i%10)&&(i=0),n.append(i.toString());var u=t.getFinderPattern().getResultPoints(),c=e.getFinderPattern().getResultPoints();return new s.A(n.toString(),null,0,[u[0],u[1],c[0],c[1]],h.A.RSS_14,new Date().getTime())},e.checkChecksum=function(t,e){var r=(t.getChecksumPortion()+16*e.getChecksumPortion())%79,n=9*t.getFinderPattern().getValue()+e.getFinderPattern().getValue();return n>72&&n--,n>8&&n--,r===n},e.prototype.decodePair=function(t,e,r,n){try{var o=this.findFinderPattern(t,e),i=this.parseFoundFinderPattern(t,r,e,o),s=null==n?null:n.get(u.A.NEED_RESULT_POINT_CALLBACK);if(null!=s){var c=(o[0]+o[1])/2;e&&(c=t.getSize()-1-c),s.foundPossibleResultPoint(new l.A(c,r))}var f=this.decodeDataCharacter(t,i,!0),h=this.decodeDataCharacter(t,i,!1);return new a(1597*f.getValue()+h.getValue(),f.getChecksumPortion()+4*h.getChecksumPortion(),i)}catch(t){return null}},e.prototype.decodeDataCharacter=function(t,r,n){for(var i=this.getDataCharacterCounters(),a=0;a<i.length;a++)i[a]=0;if(n)y.A.recordPatternInReverse(t,r.getStartEnd()[0],i);else{y.A.recordPattern(t,r.getStartEnd()[1]+1,i);for(var s=0,u=i.length-1;s<u;s++,u--){var f=i[s];i[s]=i[u],i[u]=f}}for(var h=n?16:15,l=p.A.sum(new Int32Array(i))/h,d=this.getOddCounts(),g=this.getEvenCounts(),w=this.getOddRoundingErrors(),v=this.getEvenRoundingErrors(),s=0;s<i.length;s++){var _=i[s]/l,C=Math.floor(_+.5);C<1?C=1:C>8&&(C=8);var m=Math.floor(s/2);(1&s)==0?(d[m]=C,w[m]=_-C):(g[m]=C,v[m]=_-C)}this.adjustOddEvenCounts(n,h);for(var E=0,I=0,s=d.length-1;s>=0;s--)I*=9,I+=d[s],E+=d[s];for(var S=0,T=0,s=g.length-1;s>=0;s--)S*=9,S+=g[s],T+=g[s];var O=I+3*S;if(n){if((1&E)!=0||E>12||E<4)throw new c.A;var R=(12-E)/2,b=e.OUTSIDE_ODD_WIDEST[R],N=9-b,D=A.A.getRSSvalue(d,b,!1),M=A.A.getRSSvalue(g,N,!0),P=e.OUTSIDE_EVEN_TOTAL_SUBSET[R],B=e.OUTSIDE_GSUM[R];return new o.A(D*P+M+B,O)}if((1&T)!=0||T>10||T<4)throw new c.A;var R=(10-T)/2,b=e.INSIDE_ODD_WIDEST[R],N=9-b,D=A.A.getRSSvalue(d,b,!0),M=A.A.getRSSvalue(g,N,!1),L=e.INSIDE_ODD_TOTAL_SUBSET[R],B=e.INSIDE_GSUM[R];return new o.A(M*L+D+B,O)},e.prototype.findFinderPattern=function(t,e){var r=this.getDecodeFinderCounters();r[0]=0,r[1]=0,r[2]=0,r[3]=0;for(var o=t.getSize(),i=!1,a=0;a<o&&e!==(i=!t.get(a));)a++;for(var s=0,u=a,f=a;f<o;f++)if(t.get(f)!==i)r[s]++;else{if(3===s){if(n.A.isFinderPattern(r))return[u,f];u+=r[0]+r[1],r[0]=r[2],r[1]=r[3],r[2]=0,r[3]=0,s--}else s++;r[s]=1,i=!i}throw new c.A},e.prototype.parseFoundFinderPattern=function(t,r,n,o){for(var i=t.get(o[0]),a=o[0]-1;a>=0&&i!==t.get(a);)a--;a++;var s=o[0]-a,u=this.getDecodeFinderCounters(),c=new Int32Array(u.length);g.A.arraycopy(u,0,c,1,u.length-1),c[0]=s;var f=this.parseFinderValue(c,e.FINDER_PATTERNS),h=a,l=o[1];return n&&(h=t.getSize()-1-h,l=t.getSize()-1-l),new d.A(f,[a,o[1]],h,l,r)},e.prototype.adjustOddEvenCounts=function(t,e){var r=p.A.sum(new Int32Array(this.getOddCounts())),o=p.A.sum(new Int32Array(this.getEvenCounts())),i=!1,a=!1,s=!1,u=!1;t?(r>12?a=!0:r<4&&(i=!0),o>12?u=!0:o<4&&(s=!0)):(r>11?a=!0:r<5&&(i=!0),o>10?u=!0:o<4&&(s=!0));var f=r+o-e,h=(1&r)==+!!t,l=(1&o)==1;if(1===f)if(h){if(l)throw new c.A;a=!0}else{if(!l)throw new c.A;u=!0}else if(-1===f)if(h){if(l)throw new c.A;i=!0}else{if(!l)throw new c.A;s=!0}else if(0===f){if(h){if(!l)throw new c.A;r<o?(i=!0,u=!0):(a=!0,s=!0)}else if(l)throw new c.A}else throw new c.A;if(i){if(a)throw new c.A;n.A.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(a&&n.A.decrement(this.getOddCounts(),this.getOddRoundingErrors()),s){if(u)throw new c.A;n.A.increment(this.getEvenCounts(),this.getOddRoundingErrors())}u&&n.A.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],e.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],e.OUTSIDE_GSUM=[0,161,961,2015,2715],e.INSIDE_GSUM=[0,336,1036,1516],e.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],e.INSIDE_ODD_WIDEST=[2,4,6,8],e.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])],e}(n.A)},10692:(t,e,r)=>{"use strict";r.d(e,{A:()=>N});var n=r(25969),o=r(79417),i=r(438),a=r(45332),s=r(65649),u=r(73977),c=r(3411),f=r(69071),h=r(22152),l=r(28822),d=r(85917),p=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),A=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=Int32Array.from([0,0,0,0]),e}return p(e,t),e.prototype.decodeMiddle=function(t,e,r){var n,o,i,a,s=this.decodeMiddleCounters;s[0]=0,s[1]=0,s[2]=0,s[3]=0;for(var u=t.getSize(),c=e[1],f=0;f<4&&c<u;f++){var h=d.A.decodeDigit(t,s,c,d.A.L_PATTERNS);r+=String.fromCharCode(48+h);try{for(var l=(n=void 0,A(s)),p=l.next();!p.done;p=l.next()){var g=p.value;c+=g}}catch(t){n={error:t}}finally{try{p&&!p.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}}c=d.A.findGuardPattern(t,c,!0,d.A.MIDDLE_PATTERN,new Int32Array(d.A.MIDDLE_PATTERN.length).fill(0))[1];for(var f=0;f<4&&c<u;f++){var h=d.A.decodeDigit(t,s,c,d.A.L_PATTERNS);r+=String.fromCharCode(48+h);try{for(var y=(i=void 0,A(s)),w=y.next();!w.done;w=y.next()){var g=w.value;c+=g}}catch(t){i={error:t}}finally{try{w&&!w.done&&(a=y.return)&&a.call(y)}finally{if(i)throw i.error}}}return{rowOffset:c,resultString:r}},e.prototype.getBarcodeFormat=function(){return n.A.EAN_8},e}(d.A),y=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),w=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ean13Reader=new l.A,e}return y(e,t),e.prototype.getBarcodeFormat=function(){return n.A.UPC_A},e.prototype.decode=function(t,e){return this.maybeReturnResult(this.ean13Reader.decode(t))},e.prototype.decodeRow=function(t,e,r){return this.maybeReturnResult(this.ean13Reader.decodeRow(t,e,r))},e.prototype.decodeMiddle=function(t,e,r){return this.ean13Reader.decodeMiddle(t,e,r)},e.prototype.maybeReturnResult=function(t){var e=t.getText();if("0"===e.charAt(0)){var r=new f.A(e.substring(1),null,null,t.getResultPoints(),n.A.UPC_A);return null!=t.getResultMetadata()&&r.putAllMetadata(t.getResultMetadata()),r}throw new i.A},e.prototype.reset=function(){this.ean13Reader.reset()},e}(d.A),v=r(1933),_=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),C=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},m=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=new Int32Array(4),e}return _(e,t),e.prototype.decodeMiddle=function(t,r,n){var o,i,a=this.decodeMiddleCounters.map(function(t){return t});a[0]=0,a[1]=0,a[2]=0,a[3]=0;for(var s=t.getSize(),u=r[1],c=0,f=0;f<6&&u<s;f++){var h=e.decodeDigit(t,a,u,e.L_AND_G_PATTERNS);n+=String.fromCharCode(48+h%10);try{for(var l=(o=void 0,C(a)),d=l.next();!d.done;d=l.next()){var p=d.value;u+=p}}catch(t){o={error:t}}finally{try{d&&!d.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}h>=10&&(c|=1<<5-f)}return e.determineNumSysAndCheckDigit(new v.A(n),c),u},e.prototype.decodeEnd=function(t,r){return e.findGuardPatternWithoutCounters(t,r,!0,e.MIDDLE_END_PATTERN)},e.prototype.checkChecksum=function(t){return d.A.checkChecksum(e.convertUPCEtoUPCA(t))},e.determineNumSysAndCheckDigit=function(t,e){for(var r=0;r<=1;r++)for(var n=0;n<10;n++)if(e===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[r][n]){t.insert(0,"0"+r),t.append("0"+n);return}throw i.A.getNotFoundInstance()},e.prototype.getBarcodeFormat=function(){return n.A.UPC_E},e.convertUPCEtoUPCA=function(t){var e=t.slice(1,7).split("").map(function(t){return t.charCodeAt(0)}),r=new v.A;r.append(t.charAt(0));var n=e[5];switch(n){case 0:case 1:case 2:r.appendChars(e,0,2),r.append(n),r.append("0000"),r.appendChars(e,2,3);break;case 3:r.appendChars(e,0,3),r.append("00000"),r.appendChars(e,3,2);break;case 4:r.appendChars(e,0,4),r.append("00000"),r.append(e[4]);break;default:r.appendChars(e,0,5),r.append("0000"),r.append(n)}return t.length>=8&&r.append(t.charAt(7)),r.toString()},e.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,1])],e}(d.A),E=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),I=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},S=function(t){function e(e){var r=t.call(this)||this,i=null==e?null:e.get(o.A.POSSIBLE_FORMATS),a=[];return null!=i&&(i.indexOf(n.A.EAN_13)>-1&&a.push(new l.A),i.indexOf(n.A.UPC_A)>-1&&a.push(new w),i.indexOf(n.A.EAN_8)>-1&&a.push(new g),i.indexOf(n.A.UPC_E)>-1&&a.push(new m)),0===a.length&&(a.push(new l.A),a.push(new w),a.push(new g),a.push(new m)),r.readers=a,r}return E(e,t),e.prototype.decodeRow=function(t,e,r){var a,s;try{for(var u=I(this.readers),c=u.next();!c.done;c=u.next()){var h=c.value;try{var l=h.decodeRow(t,e,r),d=l.getBarcodeFormat()===n.A.EAN_13&&"0"===l.getText().charAt(0),p=null==r?null:r.get(o.A.POSSIBLE_FORMATS),A=null==p||p.includes(n.A.UPC_A);if(d&&A){var g=l.getRawBytes(),y=new f.A(l.getText().substring(1),g,g?g.length:null,l.getResultPoints(),n.A.UPC_A);return y.putAllMetadata(l.getResultMetadata()),y}return l}catch(t){}}}catch(t){a={error:t}}finally{try{c&&!c.done&&(s=u.return)&&s.call(u)}finally{if(a)throw a.error}}throw new i.A},e.prototype.reset=function(){var t,e;try{for(var r=I(this.readers),n=r.next();!n.done;n=r.next())n.value.reset()}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e}(h.A),T=r(64476),O=r(2605),R=r(10659),b=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let N=function(t){function e(e){var r=t.call(this)||this;r.readers=[];var i=e?e.get(o.A.POSSIBLE_FORMATS):null,f=e&&void 0!==e.get(o.A.ASSUME_CODE_39_CHECK_DIGIT),h=e&&void 0!==e.get(o.A.ENABLE_CODE_39_EXTENDED_MODE);return i&&((i.includes(n.A.EAN_13)||i.includes(n.A.UPC_A)||i.includes(n.A.EAN_8)||i.includes(n.A.UPC_E))&&r.readers.push(new S(e)),i.includes(n.A.CODE_39)&&r.readers.push(new s.A(f,h)),i.includes(n.A.CODE_93)&&r.readers.push(new u.A),i.includes(n.A.CODE_128)&&r.readers.push(new a.A),i.includes(n.A.ITF)&&r.readers.push(new c.A),i.includes(n.A.CODABAR)&&r.readers.push(new T.A),i.includes(n.A.RSS_14)&&r.readers.push(new R.A),i.includes(n.A.RSS_EXPANDED)&&(console.warn("RSS Expanded reader IS NOT ready for production yet! use at your own risk."),r.readers.push(new O.A))),0===r.readers.length&&(r.readers.push(new S(e)),r.readers.push(new s.A),r.readers.push(new u.A),r.readers.push(new S(e)),r.readers.push(new a.A),r.readers.push(new c.A),r.readers.push(new R.A)),r}return b(e,t),e.prototype.decodeRow=function(t,e,r){for(var n=0;n<this.readers.length;n++)try{return this.readers[n].decodeRow(t,e,r)}catch(t){}throw new i.A},e.prototype.reset=function(){this.readers.forEach(function(t){return t.reset()})},e}(h.A)},10782:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(22868),o=r(322),i=r(6727),a=r(1933),s=r(38988);let u=function(){function t(t,e,r,n){if(this.width=t,this.height=e,this.rowSize=r,this.bits=n,null==e&&(e=t),this.height=e,t<1||e<1)throw new s.A("Both dimensions must be greater than 0");null==r&&(r=Math.floor((t+31)/32)),this.rowSize=r,null==n&&(this.bits=new Int32Array(this.rowSize*this.height))}return t.parseFromBooleanArray=function(e){for(var r=e.length,n=e[0].length,o=new t(n,r),i=0;i<r;i++)for(var a=e[i],s=0;s<n;s++)a[s]&&o.set(s,i);return o},t.parseFromString=function(e,r,n){if(null===e)throw new s.A("stringRepresentation cannot be null");for(var o=Array(e.length),i=0,a=0,u=-1,c=0,f=0;f<e.length;)if("\n"===e.charAt(f)||"\r"===e.charAt(f)){if(i>a){if(-1===u)u=i-a;else if(i-a!==u)throw new s.A("row lengths do not match");a=i,c++}f++}else if(e.substring(f,f+r.length)===r)f+=r.length,o[i]=!0,i++;else if(e.substring(f,f+n.length)===n)f+=n.length,o[i]=!1,i++;else throw new s.A("illegal character encountered: "+e.substring(f));if(i>a){if(-1===u)u=i-a;else if(i-a!==u)throw new s.A("row lengths do not match");c++}for(var h=new t(u,c),l=0;l<i;l++)o[l]&&h.set(Math.floor(l%u),Math.floor(l/u));return h},t.prototype.get=function(t,e){var r=e*this.rowSize+Math.floor(t/32);return(this.bits[r]>>>(31&t)&1)!=0},t.prototype.set=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]|=1<<(31&t)},t.prototype.unset=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]&=~(1<<(31&t))},t.prototype.flip=function(t,e){var r=e*this.rowSize+Math.floor(t/32);this.bits[r]^=1<<(31&t)},t.prototype.xor=function(t){if(this.width!==t.getWidth()||this.height!==t.getHeight()||this.rowSize!==t.getRowSize())throw new s.A("input matrix dimensions do not match");for(var e=new n.A(Math.floor(this.width/32)+1),r=this.rowSize,o=this.bits,i=0,a=this.height;i<a;i++)for(var u=i*r,c=t.getRow(i,e).getBitArray(),f=0;f<r;f++)o[u+f]^=c[f]},t.prototype.clear=function(){for(var t=this.bits,e=t.length,r=0;r<e;r++)t[r]=0},t.prototype.setRegion=function(t,e,r,n){if(e<0||t<0)throw new s.A("Left and top must be nonnegative");if(n<1||r<1)throw new s.A("Height and width must be at least 1");var o=t+r,i=e+n;if(i>this.height||o>this.width)throw new s.A("The region must fit inside the matrix");for(var a=this.rowSize,u=this.bits,c=e;c<i;c++)for(var f=c*a,h=t;h<o;h++)u[f+Math.floor(h/32)]|=1<<(31&h)},t.prototype.getRow=function(t,e){null==e||e.getSize()<this.width?e=new n.A(this.width):e.clear();for(var r=this.rowSize,o=this.bits,i=t*r,a=0;a<r;a++)e.setBulk(32*a,o[i+a]);return e},t.prototype.setRow=function(t,e){o.A.arraycopy(e.getBitArray(),0,this.bits,t*this.rowSize,this.rowSize)},t.prototype.rotate180=function(){for(var t=this.getWidth(),e=this.getHeight(),r=new n.A(t),o=new n.A(t),i=0,a=Math.floor((e+1)/2);i<a;i++)r=this.getRow(i,r),o=this.getRow(e-1-i,o),r.reverse(),o.reverse(),this.setRow(i,o),this.setRow(e-1-i,r)},t.prototype.getEnclosingRectangle=function(){for(var t=this.width,e=this.height,r=this.rowSize,n=this.bits,o=t,i=e,a=-1,s=-1,u=0;u<e;u++)for(var c=0;c<r;c++){var f=n[u*r+c];if(0!==f){if(u<i&&(i=u),u>s&&(s=u),32*c<o){for(var h=0;f<<31-h==0;)h++;32*c+h<o&&(o=32*c+h)}if(32*c+31>a){for(var h=31;f>>>h==0;)h--;32*c+h>a&&(a=32*c+h)}}}return a<o||s<i?null:Int32Array.from([o,i,a-o+1,s-i+1])},t.prototype.getTopLeftOnBit=function(){for(var t=this.rowSize,e=this.bits,r=0;r<e.length&&0===e[r];)r++;if(r===e.length)return null;for(var n=r/t,o=r%t*32,i=e[r],a=0;i<<31-a==0;)a++;return o+=a,Int32Array.from([o,n])},t.prototype.getBottomRightOnBit=function(){for(var t=this.rowSize,e=this.bits,r=e.length-1;r>=0&&0===e[r];)r--;if(r<0)return null;for(var n=Math.floor(r/t),o=32*Math.floor(r%t),i=e[r],a=31;i>>>a==0;)a--;return o+=a,Int32Array.from([o,n])},t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.getRowSize=function(){return this.rowSize},t.prototype.equals=function(e){return e instanceof t&&this.width===e.width&&this.height===e.height&&this.rowSize===e.rowSize&&i.A.equals(this.bits,e.bits)},t.prototype.hashCode=function(){var t=this.width;return 31*(t=31*(t=31*(t=31*t+this.width)+this.height)+this.rowSize)+i.A.hashCode(this.bits)},t.prototype.toString=function(t,e,r){return void 0===t&&(t="X "),void 0===e&&(e="  "),void 0===r&&(r="\n"),this.buildToString(t,e,r)},t.prototype.buildToString=function(t,e,r){for(var n=new a.A,o=0,i=this.height;o<i;o++){for(var s=0,u=this.width;s<u;s++)n.append(this.get(s,o)?t:e);n.append(r)}return n.toString()},t.prototype.clone=function(){return new t(this.width,this.height,this.rowSize,this.bits.slice())},t}()},10997:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(1933),o=r(3451);let i=function(){function t(t,e){this.width=t,this.height=e}return t.prototype.getWidth=function(){return this.width},t.prototype.getHeight=function(){return this.height},t.prototype.isCropSupported=function(){return!1},t.prototype.crop=function(t,e,r,n){throw new o.A("This luminance source does not support cropping.")},t.prototype.isRotateSupported=function(){return!1},t.prototype.rotateCounterClockwise=function(){throw new o.A("This luminance source does not support rotation by 90 degrees.")},t.prototype.rotateCounterClockwise45=function(){throw new o.A("This luminance source does not support rotation by 45 degrees.")},t.prototype.toString=function(){for(var t=new Uint8ClampedArray(this.width),e=new n.A,r=0;r<this.height;r++){for(var o=this.getRow(r,t),i=0;i<this.width;i++){var a=255&o[i],s=void 0;s=a<64?"#":a<128?"+":a<192?".":" ",e.append(s)}e.append("\n")}return e.toString()},t}()},11623:(t,e,r)=>{"use strict";r.d(e,{J:()=>p});var n=r(19106),o=r(16148),i=r(66950),a=r(38972),s=r(71534),u=r(438),c=r(26745),f=r(96768),h=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},l=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},d=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},p=function(){function t(t,e,r){void 0===e&&(e=500),this.reader=t,this.timeBetweenScansMillis=e,this._hints=r,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}return Object.defineProperty(t.prototype,"hasNavigator",{get:function(){return"undefined"!=typeof navigator},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isMediaDevicesSuported",{get:function(){return this.hasNavigator&&!!navigator.mediaDevices},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"canEnumerateDevices",{get:function(){return!!(this.isMediaDevicesSuported&&navigator.mediaDevices.enumerateDevices)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"timeBetweenDecodingAttempts",{get:function(){return this._timeBetweenDecodingAttempts},set:function(t){this._timeBetweenDecodingAttempts=t<0?0:t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hints",{get:function(){return this._hints},set:function(t){this._hints=t||null},enumerable:!1,configurable:!0}),t.prototype.listVideoInputDevices=function(){return h(this,void 0,void 0,function(){var t,e,r,n,o,i,a,s,u,c,f,h;return l(this,function(l){switch(l.label){case 0:if(!this.hasNavigator)throw Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:t=l.sent(),e=[];try{for(n=(r=d(t)).next();!n.done;n=r.next())o=n.value,i="video"===o.kind?"videoinput":o.kind,"videoinput"===i&&(a=o.deviceId||o.id,s=o.label||"Video device "+(e.length+1),u=o.groupId,c={deviceId:a,label:s,kind:i,groupId:u},e.push(c))}catch(t){f={error:t}}finally{try{n&&!n.done&&(h=r.return)&&h.call(r)}finally{if(f)throw f.error}}return[2,e]}})})},t.prototype.getVideoInputDevices=function(){return h(this,void 0,void 0,function(){return l(this,function(t){switch(t.label){case 0:return[4,this.listVideoInputDevices()];case 1:return[2,t.sent().map(function(t){return new f.K(t.deviceId,t.label)})]}})})},t.prototype.findDeviceById=function(t){return h(this,void 0,void 0,function(){var e;return l(this,function(r){switch(r.label){case 0:return[4,this.listVideoInputDevices()];case 1:if(!(e=r.sent()))return[2,null];return[2,e.find(function(e){return e.deviceId===t})]}})})},t.prototype.decodeFromInputVideoDevice=function(t,e){return h(this,void 0,void 0,function(){return l(this,function(r){switch(r.label){case 0:return[4,this.decodeOnceFromVideoDevice(t,e)];case 1:return[2,r.sent()]}})})},t.prototype.decodeOnceFromVideoDevice=function(t,e){return h(this,void 0,void 0,function(){var r,n;return l(this,function(r){switch(r.label){case 0:return this.reset(),n={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeOnceFromConstraints(n,e)];case 1:return[2,r.sent()]}})})},t.prototype.decodeOnceFromConstraints=function(t,e){return h(this,void 0,void 0,function(){var r;return l(this,function(n){switch(n.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return r=n.sent(),[4,this.decodeOnceFromStream(r,e)];case 2:return[2,n.sent()]}})})},t.prototype.decodeOnceFromStream=function(t,e){return h(this,void 0,void 0,function(){var r;return l(this,function(n){switch(n.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return r=n.sent(),[4,this.decodeOnce(r)];case 2:return[2,n.sent()]}})})},t.prototype.decodeFromInputVideoDeviceContinuously=function(t,e,r){return h(this,void 0,void 0,function(){return l(this,function(n){switch(n.label){case 0:return[4,this.decodeFromVideoDevice(t,e,r)];case 1:return[2,n.sent()]}})})},t.prototype.decodeFromVideoDevice=function(t,e,r){return h(this,void 0,void 0,function(){var n,o;return l(this,function(n){switch(n.label){case 0:return o={video:t?{deviceId:{exact:t}}:{facingMode:"environment"}},[4,this.decodeFromConstraints(o,e,r)];case 1:return[2,n.sent()]}})})},t.prototype.decodeFromConstraints=function(t,e,r){return h(this,void 0,void 0,function(){var n;return l(this,function(o){switch(o.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return n=o.sent(),[4,this.decodeFromStream(n,e,r)];case 2:return[2,o.sent()]}})})},t.prototype.decodeFromStream=function(t,e,r){return h(this,void 0,void 0,function(){var n;return l(this,function(o){switch(o.label){case 0:return this.reset(),[4,this.attachStreamToVideo(t,e)];case 1:return n=o.sent(),[4,this.decodeContinuously(n,r)];case 2:return[2,o.sent()]}})})},t.prototype.stopAsyncDecode=function(){this._stopAsyncDecode=!0},t.prototype.stopContinuousDecode=function(){this._stopContinuousDecode=!0},t.prototype.attachStreamToVideo=function(t,e){return h(this,void 0,void 0,function(){var r;return l(this,function(n){switch(n.label){case 0:return r=this.prepareVideoElement(e),this.addVideoSource(r,t),this.videoElement=r,this.stream=t,[4,this.playVideoOnLoadAsync(r)];case 1:return n.sent(),[2,r]}})})},t.prototype.playVideoOnLoadAsync=function(t){var e=this;return new Promise(function(r,n){return e.playVideoOnLoad(t,function(){return r()})})},t.prototype.playVideoOnLoad=function(t,e){var r=this;this.videoEndedListener=function(){return r.stopStreams()},this.videoCanPlayListener=function(){return r.tryPlayVideo(t)},t.addEventListener("ended",this.videoEndedListener),t.addEventListener("canplay",this.videoCanPlayListener),t.addEventListener("playing",e),this.tryPlayVideo(t)},t.prototype.isVideoPlaying=function(t){return t.currentTime>0&&!t.paused&&!t.ended&&t.readyState>2},t.prototype.tryPlayVideo=function(t){return h(this,void 0,void 0,function(){return l(this,function(e){switch(e.label){case 0:if(this.isVideoPlaying(t))return console.warn("Trying to play video that is already playing."),[2];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,t.play()];case 2:return e.sent(),[3,4];case 3:return e.sent(),console.warn("It was not possible to play the video."),[3,4];case 4:return[2]}})})},t.prototype.getMediaElement=function(t,e){var r=document.getElementById(t);if(!r)throw new n.A("element with id '"+t+"' not found");if(r.nodeName.toLowerCase()!==e.toLowerCase())throw new n.A("element with id '"+t+"' must be an "+e+" element");return r},t.prototype.decodeFromImage=function(t,e){if(!t&&!e)throw new n.A("either imageElement with a src set or an url must be provided");return e&&!t?this.decodeFromImageUrl(e):this.decodeFromImageElement(t)},t.prototype.decodeFromVideo=function(t,e){if(!t&&!e)throw new n.A("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrl(e):this.decodeFromVideoElement(t)},t.prototype.decodeFromVideoContinuously=function(t,e,r){if(void 0===t&&void 0===e)throw new n.A("Either an element with a src set or an URL must be provided");return e&&!t?this.decodeFromVideoUrlContinuously(e,r):this.decodeFromVideoElementContinuously(t,r)},t.prototype.decodeFromImageElement=function(t){if(!t)throw new n.A("An image element must be provided.");this.reset();var e,r=this.prepareImageElement(t);return this.imageElement=r,this.isImageLoaded(r)?this.decodeOnce(r,!1,!0):this._decodeOnLoadImage(r)},t.prototype.decodeFromVideoElement=function(t){var e=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideo(e)},t.prototype.decodeFromVideoElementContinuously=function(t,e){var r=this._decodeFromVideoElementSetup(t);return this._decodeOnLoadVideoContinuously(r,e)},t.prototype._decodeFromVideoElementSetup=function(t){if(!t)throw new n.A("A video element must be provided.");this.reset();var e=this.prepareVideoElement(t);return this.videoElement=e,e},t.prototype.decodeFromImageUrl=function(t){if(!t)throw new n.A("An URL must be provided.");this.reset();var e=this.prepareImageElement();this.imageElement=e;var r=this._decodeOnLoadImage(e);return e.src=t,r},t.prototype.decodeFromVideoUrl=function(t){if(!t)throw new n.A("An URL must be provided.");this.reset();var e=this.prepareVideoElement(),r=this.decodeFromVideoElement(e);return e.src=t,r},t.prototype.decodeFromVideoUrlContinuously=function(t,e){if(!t)throw new n.A("An URL must be provided.");this.reset();var r=this.prepareVideoElement(),o=this.decodeFromVideoElementContinuously(r,e);return r.src=t,o},t.prototype._decodeOnLoadImage=function(t){var e=this;return new Promise(function(r,n){e.imageLoadedListener=function(){return e.decodeOnce(t,!1,!0).then(r,n)},t.addEventListener("load",e.imageLoadedListener)})},t.prototype._decodeOnLoadVideo=function(t){return h(this,void 0,void 0,function(){return l(this,function(e){switch(e.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return e.sent(),[4,this.decodeOnce(t)];case 2:return[2,e.sent()]}})})},t.prototype._decodeOnLoadVideoContinuously=function(t,e){return h(this,void 0,void 0,function(){return l(this,function(r){switch(r.label){case 0:return[4,this.playVideoOnLoadAsync(t)];case 1:return r.sent(),this.decodeContinuously(t,e),[2]}})})},t.prototype.isImageLoaded=function(t){return!!t.complete&&0!==t.naturalWidth},t.prototype.prepareImageElement=function(t){var e;return void 0===t&&((e=document.createElement("img")).width=200,e.height=200),"string"==typeof t&&(e=this.getMediaElement(t,"img")),t instanceof HTMLImageElement&&(e=t),e},t.prototype.prepareVideoElement=function(t){var e;return t||"undefined"==typeof document||((e=document.createElement("video")).width=200,e.height=200),"string"==typeof t&&(e=this.getMediaElement(t,"video")),t instanceof HTMLVideoElement&&(e=t),e.setAttribute("autoplay","true"),e.setAttribute("muted","true"),e.setAttribute("playsinline","true"),e},t.prototype.decodeOnce=function(t,e,r){var n=this;void 0===e&&(e=!0),void 0===r&&(r=!0),this._stopAsyncDecode=!1;var o=function(a,c){if(n._stopAsyncDecode){c(new u.A("Video stream has ended before any code could be detected.")),n._stopAsyncDecode=void 0;return}try{var f=n.decode(t);a(f)}catch(t){var h=e&&t instanceof u.A,l=(t instanceof i.A||t instanceof s.A)&&r;if(h||l)return setTimeout(o,n._timeBetweenDecodingAttempts,a,c);c(t)}};return new Promise(function(t,e){return o(t,e)})},t.prototype.decodeContinuously=function(t,e){var r=this;this._stopContinuousDecode=!1;var n=function(){if(r._stopContinuousDecode){r._stopContinuousDecode=void 0;return}try{var o=r.decode(t);e(o,null),setTimeout(n,r.timeBetweenScansMillis)}catch(t){e(null,t);var a=t instanceof i.A||t instanceof s.A,c=t instanceof u.A;(a||c)&&setTimeout(n,r._timeBetweenDecodingAttempts)}};n()},t.prototype.decode=function(t){var e=this.createBinaryBitmap(t);return this.decodeBitmap(e)},t.prototype.createBinaryBitmap=function(t){this.getCaptureCanvasContext(t);var e=!1;t instanceof HTMLVideoElement?(this.drawFrameOnCanvas(t),e=!0):this.drawImageOnCanvas(t);var r=this.getCaptureCanvas(t),n=new c.L(r,e),i=new a.A(n);return new o.A(i)},t.prototype.getCaptureCanvasContext=function(t){if(!this.captureCanvasContext){var e=this.getCaptureCanvas(t),r=void 0;try{r=e.getContext("2d",{willReadFrequently:!0})}catch(t){r=e.getContext("2d")}this.captureCanvasContext=r}return this.captureCanvasContext},t.prototype.getCaptureCanvas=function(t){if(!this.captureCanvas){var e=this.createCaptureCanvas(t);this.captureCanvas=e}return this.captureCanvas},t.prototype.drawFrameOnCanvas=function(t,e,r){void 0===e&&(e={sx:0,sy:0,sWidth:t.videoWidth,sHeight:t.videoHeight,dx:0,dy:0,dWidth:t.videoWidth,dHeight:t.videoHeight}),void 0===r&&(r=this.captureCanvasContext),r.drawImage(t,e.sx,e.sy,e.sWidth,e.sHeight,e.dx,e.dy,e.dWidth,e.dHeight)},t.prototype.drawImageOnCanvas=function(t,e,r){void 0===e&&(e={sx:0,sy:0,sWidth:t.naturalWidth,sHeight:t.naturalHeight,dx:0,dy:0,dWidth:t.naturalWidth,dHeight:t.naturalHeight}),void 0===r&&(r=this.captureCanvasContext),r.drawImage(t,e.sx,e.sy,e.sWidth,e.sHeight,e.dx,e.dy,e.dWidth,e.dHeight)},t.prototype.decodeBitmap=function(t){return this.reader.decode(t,this._hints)},t.prototype.createCaptureCanvas=function(t){if("undefined"==typeof document)return this._destroyCaptureCanvas(),null;var e,r,n=document.createElement("canvas");return void 0!==t&&(t instanceof HTMLVideoElement?(e=t.videoWidth,r=t.videoHeight):t instanceof HTMLImageElement&&(e=t.naturalWidth||t.width,r=t.naturalHeight||t.height)),n.style.width=e+"px",n.style.height=r+"px",n.width=e,n.height=r,n},t.prototype.stopStreams=function(){this.stream&&(this.stream.getVideoTracks().forEach(function(t){return t.stop()}),this.stream=void 0),!1===this._stopAsyncDecode&&this.stopAsyncDecode(),!1===this._stopContinuousDecode&&this.stopContinuousDecode()},t.prototype.reset=function(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()},t.prototype._destroyVideoElement=function(){this.videoElement&&(void 0!==this.videoEndedListener&&this.videoElement.removeEventListener("ended",this.videoEndedListener),void 0!==this.videoPlayingEventListener&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),void 0!==this.videoCanPlayListener&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)},t.prototype._destroyImageElement=function(){this.imageElement&&(void 0!==this.imageLoadedListener&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)},t.prototype._destroyCaptureCanvas=function(){this.captureCanvasContext=void 0,this.captureCanvas=void 0},t.prototype.addVideoSource=function(t,e){try{t.srcObject=e}catch(r){t.src=URL.createObjectURL(e)}},t.prototype.cleanVideoSource=function(t){try{t.srcObject=null}catch(e){t.src=""}this.videoElement.removeAttribute("src")},t}()},13997:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(t,e){this.value=t,this.checksumPortion=e}return t.prototype.getValue=function(){return this.value},t.prototype.getChecksumPortion=function(){return this.checksumPortion},t.prototype.toString=function(){return this.value+"("+this.checksumPortion+")"},t.prototype.equals=function(e){return e instanceof t&&this.value===e.value&&this.checksumPortion===e.checksumPortion},t.prototype.hashCode=function(){return this.value^this.checksumPortion},t}()},14842:(t,e,r)=>{"use strict";r.d(e,{A:()=>d});var n=r(25969),o=r(27217),i=r(23583),a=r(10782),s=r(2257),u=r(91375),c=r(63479),f=r(39778),h=r(38988),l=r(23510);let d=function(){function t(){}return t.prototype.encode=function(t,e,r,n){return this.encodeWithHints(t,e,r,n,null)},t.prototype.encodeWithHints=function(e,r,n,a,f){var h=u.A.ISO_8859_1,l=i.A.DEFAULT_EC_PERCENT,d=i.A.DEFAULT_AZTEC_LAYERS;return null!=f&&(f.has(o.A.CHARACTER_SET)&&(h=s.A.forName(f.get(o.A.CHARACTER_SET).toString())),f.has(o.A.ERROR_CORRECTION)&&(l=c.A.parseInt(f.get(o.A.ERROR_CORRECTION).toString())),f.has(o.A.AZTEC_LAYERS)&&(d=c.A.parseInt(f.get(o.A.AZTEC_LAYERS).toString()))),t.encodeLayers(e,r,n,a,h,l,d)},t.encodeLayers=function(e,r,o,a,s,u,c){if(r!==n.A.AZTEC)throw new h.A("Can only encode AZTEC, but got "+r);var f=i.A.encode(l.A.getBytes(e,s),u,c);return t.renderResult(f,o,a)},t.renderResult=function(t,e,r){var n=t.getMatrix();if(null==n)throw new f.A;for(var o=n.getWidth(),i=n.getHeight(),s=Math.max(e,o),u=Math.max(r,i),c=Math.min(s/o,u/i),h=(s-o*c)/2,l=(u-i*c)/2,d=new a.A(s,u),p=0,A=l;p<i;p++,A+=c)for(var g=0,y=h;g<o;g++,y+=c)n.get(g,p)&&d.setRegion(y,A,c,c);return d},t}()},16067:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let o=function(){function t(){}return t.getRSSvalue=function(e,r,o){var i,a,s=0;try{for(var u=n(e),c=u.next();!c.done;c=u.next()){var f=c.value;s+=f}}catch(t){i={error:t}}finally{try{c&&!c.done&&(a=u.return)&&a.call(u)}finally{if(i)throw i.error}}for(var h=0,l=0,d=e.length,p=0;p<d-1;p++){var A=void 0;for(A=1,l|=1<<p;A<e[p];A++,l&=~(1<<p)){var g=t.combins(s-A-1,d-p-2);if(o&&0===l&&s-A-(d-p-1)>=d-p-1&&(g-=t.combins(s-A-(d-p),d-p-2)),d-p-1>1){for(var y=0,w=s-A-(d-p-2);w>r;w--)y+=t.combins(s-A-w-1,d-p-3);g-=y*(d-1-p)}else s-A>r&&g--;h+=g}s-=A}return h},t.combins=function(t,e){t-e>e?(n=e,r=t-e):(n=t-e,r=e);for(var r,n,o=1,i=1,a=t;a>r;a--)o*=a,i<=n&&(o/=i,i++);for(;i<=n;)o/=i,i++;return o},t}()},16148:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(38988);let o=function(){function t(t){if(this.binarizer=t,null===t)throw new n.A("Binarizer must be non-null.")}return t.prototype.getWidth=function(){return this.binarizer.getWidth()},t.prototype.getHeight=function(){return this.binarizer.getHeight()},t.prototype.getBlackRow=function(t,e){return this.binarizer.getBlackRow(t,e)},t.prototype.getBlackMatrix=function(){return(null===this.matrix||void 0===this.matrix)&&(this.matrix=this.binarizer.getBlackMatrix()),this.matrix},t.prototype.isCropSupported=function(){return this.binarizer.getLuminanceSource().isCropSupported()},t.prototype.crop=function(e,r,n,o){var i=this.binarizer.getLuminanceSource().crop(e,r,n,o);return new t(this.binarizer.createBinarizer(i))},t.prototype.isRotateSupported=function(){return this.binarizer.getLuminanceSource().isRotateSupported()},t.prototype.rotateCounterClockwise=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new t(this.binarizer.createBinarizer(e))},t.prototype.rotateCounterClockwise45=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new t(this.binarizer.createBinarizer(e))},t.prototype.toString=function(){try{return this.getBlackMatrix().toString()}catch(t){return""}},t}()},17391:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(56595),o=r(79886),i=r(438);let a=function(){function t(e,r,n,o){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),null==r&&(r=t.INIT_SIZE),null==n&&(n=e.getWidth()/2|0),null==o&&(o=e.getHeight()/2|0);var a=r/2|0;if(this.leftInit=n-a,this.rightInit=n+a,this.upInit=o-a,this.downInit=o+a,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new i.A}return t.prototype.detect=function(){for(var t=this.leftInit,e=this.rightInit,r=this.upInit,n=this.downInit,o=!1,a=!0,s=!1,u=!1,c=!1,f=!1,h=!1,l=this.width,d=this.height;a;){a=!1;for(var p=!0;(p||!u)&&e<l;)(p=this.containsBlackPoint(r,n,e,!1))?(e++,a=!0,u=!0):!u&&e++;if(e>=l){o=!0;break}for(var A=!0;(A||!c)&&n<d;)(A=this.containsBlackPoint(t,e,n,!0))?(n++,a=!0,c=!0):!c&&n++;if(n>=d){o=!0;break}for(var g=!0;(g||!f)&&t>=0;)(g=this.containsBlackPoint(r,n,t,!1))?(t--,a=!0,f=!0):!f&&t--;if(t<0){o=!0;break}for(var y=!0;(y||!h)&&r>=0;)(y=this.containsBlackPoint(t,e,r,!0))?(r--,a=!0,h=!0):!h&&r--;if(r<0){o=!0;break}a&&(s=!0)}if(!o&&s){for(var w=e-t,v=null,_=1;null===v&&_<w;_++)v=this.getBlackPointOnSegment(t,n-_,t+_,n);if(null==v)throw new i.A;for(var C=null,_=1;null===C&&_<w;_++)C=this.getBlackPointOnSegment(t,r+_,t+_,r);if(null==C)throw new i.A;for(var m=null,_=1;null===m&&_<w;_++)m=this.getBlackPointOnSegment(e,r+_,e-_,r);if(null==m)throw new i.A;for(var E=null,_=1;null===E&&_<w;_++)E=this.getBlackPointOnSegment(e,n-_,e-_,n);if(null==E)throw new i.A;return this.centerEdges(E,v,m,C)}throw new i.A},t.prototype.getBlackPointOnSegment=function(t,e,r,i){for(var a=o.A.round(o.A.distance(t,e,r,i)),s=(r-t)/a,u=(i-e)/a,c=this.image,f=0;f<a;f++){var h=o.A.round(t+f*s),l=o.A.round(e+f*u);if(c.get(h,l))return new n.A(h,l)}return null},t.prototype.centerEdges=function(e,r,o,i){var a=e.getX(),s=e.getY(),u=r.getX(),c=r.getY(),f=o.getX(),h=o.getY(),l=i.getX(),d=i.getY(),p=t.CORR;return a<this.width/2?[new n.A(l-p,d+p),new n.A(u+p,c+p),new n.A(f-p,h-p),new n.A(a+p,s-p)]:[new n.A(l+p,d+p),new n.A(u+p,c-p),new n.A(f-p,h+p),new n.A(a-p,s-p)]},t.prototype.containsBlackPoint=function(t,e,r,n){var o=this.image;if(n){for(var i=t;i<=e;i++)if(o.get(i,r))return!0}else for(var a=t;a<=e;a++)if(o.get(r,a))return!0;return!1},t.INIT_SIZE=10,t.CORR=1,t}()},17878:(t,e,r)=>{"use strict";r.d(e,{s:()=>a});var n=r(11623),o=r(92251),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(e,r){void 0===e&&(e=null),void 0===r&&(r=500);var n=this,i=new o.A;return i.setHints(e),t.call(this,i,r)||this}return i(e,t),e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e}(n.J)},19106:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="ArgumentException",e}(n.A)},19116:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(t){this.source=t}return t.prototype.getLuminanceSource=function(){return this.source},t.prototype.getWidth=function(){return this.source.getWidth()},t.prototype.getHeight=function(){return this.source.getHeight()},t}()},20367:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(92727);let o=function(){function t(){}return t.setGridSampler=function(e){t.gridSampler=e},t.getInstance=function(){return t.gridSampler},t.gridSampler=new n.A,t}()},20738:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="ReedSolomonException",e}(n.A)},21876:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var n=r(10782),o=r(78903),i=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=function(){function t(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];this.ecCodewordsPerBlock=t,this.ecBlocks=e}return t.prototype.getECCodewordsPerBlock=function(){return this.ecCodewordsPerBlock},t.prototype.getNumBlocks=function(){var t,e,r=0,n=this.ecBlocks;try{for(var o=i(n),a=o.next();!a.done;a=o.next()){var s=a.value;r+=s.getCount()}}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r},t.prototype.getTotalECCodewords=function(){return this.ecCodewordsPerBlock*this.getNumBlocks()},t.prototype.getECBlocks=function(){return this.ecBlocks},t}(),s=function(){function t(t,e){this.count=t,this.dataCodewords=e}return t.prototype.getCount=function(){return this.count},t.prototype.getDataCodewords=function(){return this.dataCodewords},t}(),u=r(71534),c=r(38988),f=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let h=function(){function t(t,e){for(var r,n,o=[],i=2;i<arguments.length;i++)o[i-2]=arguments[i];this.versionNumber=t,this.alignmentPatternCenters=e,this.ecBlocks=o;var a=0,s=o[0].getECCodewordsPerBlock(),u=o[0].getECBlocks();try{for(var c=f(u),h=c.next();!h.done;h=c.next()){var l=h.value;a+=l.getCount()*(l.getDataCodewords()+s)}}catch(t){r={error:t}}finally{try{h&&!h.done&&(n=c.return)&&n.call(c)}finally{if(r)throw r.error}}this.totalCodewords=a}return t.prototype.getVersionNumber=function(){return this.versionNumber},t.prototype.getAlignmentPatternCenters=function(){return this.alignmentPatternCenters},t.prototype.getTotalCodewords=function(){return this.totalCodewords},t.prototype.getDimensionForVersion=function(){return 17+4*this.versionNumber},t.prototype.getECBlocksForLevel=function(t){return this.ecBlocks[t.getValue()]},t.getProvisionalVersionForDimension=function(t){if(t%4!=1)throw new u.A;try{return this.getVersionForNumber((t-17)/4)}catch(t){throw new u.A}},t.getVersionForNumber=function(e){if(e<1||e>40)throw new c.A;return t.VERSIONS[e-1]},t.decodeVersionInformation=function(e){for(var r=Number.MAX_SAFE_INTEGER,n=0,i=0;i<t.VERSION_DECODE_INFO.length;i++){var a=t.VERSION_DECODE_INFO[i];if(a===e)return t.getVersionForNumber(i+7);var s=o.A.numBitsDiffering(e,a);s<r&&(n=i+7,r=s)}return r<=3?t.getVersionForNumber(n):null},t.prototype.buildFunctionPattern=function(){var t=this.getDimensionForVersion(),e=new n.A(t);e.setRegion(0,0,9,9),e.setRegion(t-8,0,8,9),e.setRegion(0,t-8,9,8);for(var r=this.alignmentPatternCenters.length,o=0;o<r;o++)for(var i=this.alignmentPatternCenters[o]-2,a=0;a<r;a++)(0!==o||0!==a&&a!==r-1)&&(o!==r-1||0!==a)&&e.setRegion(this.alignmentPatternCenters[a]-2,i,5,5);return e.setRegion(6,9,1,t-17),e.setRegion(9,6,t-17,1),this.versionNumber>6&&(e.setRegion(t-11,0,3,6),e.setRegion(0,t-11,6,3)),e},t.prototype.toString=function(){return""+this.versionNumber},t.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),t.VERSIONS=[new t(1,new Int32Array(0),new a(7,new s(1,19)),new a(10,new s(1,16)),new a(13,new s(1,13)),new a(17,new s(1,9))),new t(2,Int32Array.from([6,18]),new a(10,new s(1,34)),new a(16,new s(1,28)),new a(22,new s(1,22)),new a(28,new s(1,16))),new t(3,Int32Array.from([6,22]),new a(15,new s(1,55)),new a(26,new s(1,44)),new a(18,new s(2,17)),new a(22,new s(2,13))),new t(4,Int32Array.from([6,26]),new a(20,new s(1,80)),new a(18,new s(2,32)),new a(26,new s(2,24)),new a(16,new s(4,9))),new t(5,Int32Array.from([6,30]),new a(26,new s(1,108)),new a(24,new s(2,43)),new a(18,new s(2,15),new s(2,16)),new a(22,new s(2,11),new s(2,12))),new t(6,Int32Array.from([6,34]),new a(18,new s(2,68)),new a(16,new s(4,27)),new a(24,new s(4,19)),new a(28,new s(4,15))),new t(7,Int32Array.from([6,22,38]),new a(20,new s(2,78)),new a(18,new s(4,31)),new a(18,new s(2,14),new s(4,15)),new a(26,new s(4,13),new s(1,14))),new t(8,Int32Array.from([6,24,42]),new a(24,new s(2,97)),new a(22,new s(2,38),new s(2,39)),new a(22,new s(4,18),new s(2,19)),new a(26,new s(4,14),new s(2,15))),new t(9,Int32Array.from([6,26,46]),new a(30,new s(2,116)),new a(22,new s(3,36),new s(2,37)),new a(20,new s(4,16),new s(4,17)),new a(24,new s(4,12),new s(4,13))),new t(10,Int32Array.from([6,28,50]),new a(18,new s(2,68),new s(2,69)),new a(26,new s(4,43),new s(1,44)),new a(24,new s(6,19),new s(2,20)),new a(28,new s(6,15),new s(2,16))),new t(11,Int32Array.from([6,30,54]),new a(20,new s(4,81)),new a(30,new s(1,50),new s(4,51)),new a(28,new s(4,22),new s(4,23)),new a(24,new s(3,12),new s(8,13))),new t(12,Int32Array.from([6,32,58]),new a(24,new s(2,92),new s(2,93)),new a(22,new s(6,36),new s(2,37)),new a(26,new s(4,20),new s(6,21)),new a(28,new s(7,14),new s(4,15))),new t(13,Int32Array.from([6,34,62]),new a(26,new s(4,107)),new a(22,new s(8,37),new s(1,38)),new a(24,new s(8,20),new s(4,21)),new a(22,new s(12,11),new s(4,12))),new t(14,Int32Array.from([6,26,46,66]),new a(30,new s(3,115),new s(1,116)),new a(24,new s(4,40),new s(5,41)),new a(20,new s(11,16),new s(5,17)),new a(24,new s(11,12),new s(5,13))),new t(15,Int32Array.from([6,26,48,70]),new a(22,new s(5,87),new s(1,88)),new a(24,new s(5,41),new s(5,42)),new a(30,new s(5,24),new s(7,25)),new a(24,new s(11,12),new s(7,13))),new t(16,Int32Array.from([6,26,50,74]),new a(24,new s(5,98),new s(1,99)),new a(28,new s(7,45),new s(3,46)),new a(24,new s(15,19),new s(2,20)),new a(30,new s(3,15),new s(13,16))),new t(17,Int32Array.from([6,30,54,78]),new a(28,new s(1,107),new s(5,108)),new a(28,new s(10,46),new s(1,47)),new a(28,new s(1,22),new s(15,23)),new a(28,new s(2,14),new s(17,15))),new t(18,Int32Array.from([6,30,56,82]),new a(30,new s(5,120),new s(1,121)),new a(26,new s(9,43),new s(4,44)),new a(28,new s(17,22),new s(1,23)),new a(28,new s(2,14),new s(19,15))),new t(19,Int32Array.from([6,30,58,86]),new a(28,new s(3,113),new s(4,114)),new a(26,new s(3,44),new s(11,45)),new a(26,new s(17,21),new s(4,22)),new a(26,new s(9,13),new s(16,14))),new t(20,Int32Array.from([6,34,62,90]),new a(28,new s(3,107),new s(5,108)),new a(26,new s(3,41),new s(13,42)),new a(30,new s(15,24),new s(5,25)),new a(28,new s(15,15),new s(10,16))),new t(21,Int32Array.from([6,28,50,72,94]),new a(28,new s(4,116),new s(4,117)),new a(26,new s(17,42)),new a(28,new s(17,22),new s(6,23)),new a(30,new s(19,16),new s(6,17))),new t(22,Int32Array.from([6,26,50,74,98]),new a(28,new s(2,111),new s(7,112)),new a(28,new s(17,46)),new a(30,new s(7,24),new s(16,25)),new a(24,new s(34,13))),new t(23,Int32Array.from([6,30,54,78,102]),new a(30,new s(4,121),new s(5,122)),new a(28,new s(4,47),new s(14,48)),new a(30,new s(11,24),new s(14,25)),new a(30,new s(16,15),new s(14,16))),new t(24,Int32Array.from([6,28,54,80,106]),new a(30,new s(6,117),new s(4,118)),new a(28,new s(6,45),new s(14,46)),new a(30,new s(11,24),new s(16,25)),new a(30,new s(30,16),new s(2,17))),new t(25,Int32Array.from([6,32,58,84,110]),new a(26,new s(8,106),new s(4,107)),new a(28,new s(8,47),new s(13,48)),new a(30,new s(7,24),new s(22,25)),new a(30,new s(22,15),new s(13,16))),new t(26,Int32Array.from([6,30,58,86,114]),new a(28,new s(10,114),new s(2,115)),new a(28,new s(19,46),new s(4,47)),new a(28,new s(28,22),new s(6,23)),new a(30,new s(33,16),new s(4,17))),new t(27,Int32Array.from([6,34,62,90,118]),new a(30,new s(8,122),new s(4,123)),new a(28,new s(22,45),new s(3,46)),new a(30,new s(8,23),new s(26,24)),new a(30,new s(12,15),new s(28,16))),new t(28,Int32Array.from([6,26,50,74,98,122]),new a(30,new s(3,117),new s(10,118)),new a(28,new s(3,45),new s(23,46)),new a(30,new s(4,24),new s(31,25)),new a(30,new s(11,15),new s(31,16))),new t(29,Int32Array.from([6,30,54,78,102,126]),new a(30,new s(7,116),new s(7,117)),new a(28,new s(21,45),new s(7,46)),new a(30,new s(1,23),new s(37,24)),new a(30,new s(19,15),new s(26,16))),new t(30,Int32Array.from([6,26,52,78,104,130]),new a(30,new s(5,115),new s(10,116)),new a(28,new s(19,47),new s(10,48)),new a(30,new s(15,24),new s(25,25)),new a(30,new s(23,15),new s(25,16))),new t(31,Int32Array.from([6,30,56,82,108,134]),new a(30,new s(13,115),new s(3,116)),new a(28,new s(2,46),new s(29,47)),new a(30,new s(42,24),new s(1,25)),new a(30,new s(23,15),new s(28,16))),new t(32,Int32Array.from([6,34,60,86,112,138]),new a(30,new s(17,115)),new a(28,new s(10,46),new s(23,47)),new a(30,new s(10,24),new s(35,25)),new a(30,new s(19,15),new s(35,16))),new t(33,Int32Array.from([6,30,58,86,114,142]),new a(30,new s(17,115),new s(1,116)),new a(28,new s(14,46),new s(21,47)),new a(30,new s(29,24),new s(19,25)),new a(30,new s(11,15),new s(46,16))),new t(34,Int32Array.from([6,34,62,90,118,146]),new a(30,new s(13,115),new s(6,116)),new a(28,new s(14,46),new s(23,47)),new a(30,new s(44,24),new s(7,25)),new a(30,new s(59,16),new s(1,17))),new t(35,Int32Array.from([6,30,54,78,102,126,150]),new a(30,new s(12,121),new s(7,122)),new a(28,new s(12,47),new s(26,48)),new a(30,new s(39,24),new s(14,25)),new a(30,new s(22,15),new s(41,16))),new t(36,Int32Array.from([6,24,50,76,102,128,154]),new a(30,new s(6,121),new s(14,122)),new a(28,new s(6,47),new s(34,48)),new a(30,new s(46,24),new s(10,25)),new a(30,new s(2,15),new s(64,16))),new t(37,Int32Array.from([6,28,54,80,106,132,158]),new a(30,new s(17,122),new s(4,123)),new a(28,new s(29,46),new s(14,47)),new a(30,new s(49,24),new s(10,25)),new a(30,new s(24,15),new s(46,16))),new t(38,Int32Array.from([6,32,58,84,110,136,162]),new a(30,new s(4,122),new s(18,123)),new a(28,new s(13,46),new s(32,47)),new a(30,new s(48,24),new s(14,25)),new a(30,new s(42,15),new s(32,16))),new t(39,Int32Array.from([6,26,54,82,110,138,166]),new a(30,new s(20,117),new s(4,118)),new a(28,new s(40,47),new s(7,48)),new a(30,new s(43,24),new s(22,25)),new a(30,new s(10,15),new s(67,16))),new t(40,Int32Array.from([6,30,58,86,114,142,170]),new a(30,new s(19,118),new s(6,119)),new a(28,new s(18,47),new s(31,48)),new a(30,new s(34,24),new s(34,25)),new a(30,new s(20,15),new s(61,16)))],t}()},22152:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(22868),o=r(79417),i=r(43358),a=r(56595),s=r(438);let u=function(){function t(){}return t.prototype.decode=function(t,e){try{return this.doDecode(t,e)}catch(d){if(e&&!0===e.get(o.A.TRY_HARDER)&&t.isRotateSupported()){var r=t.rotateCounterClockwise(),n=this.doDecode(r,e),u=n.getResultMetadata(),c=270;null!==u&&!0===u.get(i.A.ORIENTATION)&&(c+=u.get(i.A.ORIENTATION)%360),n.putMetadata(i.A.ORIENTATION,c);var f=n.getResultPoints();if(null!==f)for(var h=r.getHeight(),l=0;l<f.length;l++)f[l]=new a.A(h-f[l].getY()-1,f[l].getX());return n}throw new s.A}},t.prototype.reset=function(){},t.prototype.doDecode=function(t,e){var r,u=t.getWidth(),c=t.getHeight(),f=new n.A(u),h=e&&!0===e.get(o.A.TRY_HARDER),l=Math.max(1,c>>(h?8:5));r=h?c:15;for(var d=Math.trunc(c/2),p=0;p<r;p++){var A=Math.trunc((p+1)/2),g=d+l*((1&p)==0?A:-A);if(g<0||g>=c)break;try{f=t.getBlackRow(g,f)}catch(t){continue}for(var y=this,w=0;w<2;w++){var v=function(t){if(1===t&&(f.reverse(),e&&!0===e.get(o.A.NEED_RESULT_POINT_CALLBACK))){var r=new Map;e.forEach(function(t,e){return r.set(e,t)}),r.delete(o.A.NEED_RESULT_POINT_CALLBACK),e=r}try{var n=y.decodeRow(g,f,e);if(1===t){n.putMetadata(i.A.ORIENTATION,180);var s=n.getResultPoints();null!==s&&(s[0]=new a.A(u-s[0].getX()-1,s[0].getY()),s[1]=new a.A(u-s[1].getX()-1,s[1].getY()))}return{value:n}}catch(t){}}(w);if("object"==typeof v)return v.value}}throw new s.A},t.recordPattern=function(t,e,r){for(var n=r.length,o=0;o<n;o++)r[o]=0;var i=t.getSize();if(e>=i)throw new s.A;for(var a=!t.get(e),u=0,c=e;c<i;){if(t.get(c)!==a)r[u]++;else if(++u===n)break;else r[u]=1,a=!a;c++}if(u!==n&&(u!==n-1||c!==i))throw new s.A},t.recordPatternInReverse=function(e,r,n){for(var o=n.length,i=e.get(r);r>0&&o>=0;)e.get(--r)!==i&&(o--,i=!i);if(o>=0)throw new s.A;t.recordPattern(e,r+1,n)},t.patternMatchVariance=function(t,e,r){for(var n=t.length,o=0,i=0,a=0;a<n;a++)o+=t[a],i+=e[a];if(o<i)return Number.POSITIVE_INFINITY;var s=o/i;r*=s;for(var u=0,c=0;c<n;c++){var f=t[c],h=e[c]*s,l=f>h?f-h:h-f;if(l>r)return Number.POSITIVE_INFINITY;u+=l}return u/o},t}()},22868:(t,e,r)=>{"use strict";r.d(e,{A:()=>s});var n=r(38988),o=r(6727),i=r(63479),a=r(322);let s=function(){function t(e,r){void 0===e?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,null==r?this.bits=t.makeArray(e):this.bits=r)}return t.prototype.getSize=function(){return this.size},t.prototype.getSizeInBytes=function(){return Math.floor((this.size+7)/8)},t.prototype.ensureCapacity=function(e){if(e>32*this.bits.length){var r=t.makeArray(e);a.A.arraycopy(this.bits,0,r,0,this.bits.length),this.bits=r}},t.prototype.get=function(t){return(this.bits[Math.floor(t/32)]&1<<(31&t))!=0},t.prototype.set=function(t){this.bits[Math.floor(t/32)]|=1<<(31&t)},t.prototype.flip=function(t){this.bits[Math.floor(t/32)]^=1<<(31&t)},t.prototype.getNextSet=function(t){var e=this.size;if(t>=e)return e;var r=this.bits,n=Math.floor(t/32),o=r[n];o&=~((1<<(31&t))-1);for(var a=r.length;0===o;){if(++n===a)return e;o=r[n]}var s=32*n+i.A.numberOfTrailingZeros(o);return s>e?e:s},t.prototype.getNextUnset=function(t){var e=this.size;if(t>=e)return e;var r=this.bits,n=Math.floor(t/32),o=~r[n];o&=~((1<<(31&t))-1);for(var a=r.length;0===o;){if(++n===a)return e;o=~r[n]}var s=32*n+i.A.numberOfTrailingZeros(o);return s>e?e:s},t.prototype.setBulk=function(t,e){this.bits[Math.floor(t/32)]=e},t.prototype.setRange=function(t,e){if(e<t||t<0||e>this.size)throw new n.A;if(e!==t)for(var r=Math.floor(t/32),o=Math.floor(--e/32),i=this.bits,a=r;a<=o;a++){var s=a>r?0:31&t,u=(2<<(a<o?31:31&e))-(1<<s);i[a]|=u}},t.prototype.clear=function(){for(var t=this.bits.length,e=this.bits,r=0;r<t;r++)e[r]=0},t.prototype.isRange=function(t,e,r){if(e<t||t<0||e>this.size)throw new n.A;if(e===t)return!0;for(var o=Math.floor(t/32),i=Math.floor(--e/32),a=this.bits,s=o;s<=i;s++){var u=s>o?0:31&t,c=(2<<(s<i?31:31&e))-(1<<u)|0;if((a[s]&c)!==(r?c:0))return!1}return!0},t.prototype.appendBit=function(t){this.ensureCapacity(this.size+1),t&&(this.bits[Math.floor(this.size/32)]|=1<<(31&this.size)),this.size++},t.prototype.appendBits=function(t,e){if(e<0||e>32)throw new n.A("Num bits must be between 0 and 32");this.ensureCapacity(this.size+e);for(var r=e;r>0;r--)this.appendBit((t>>r-1&1)==1)},t.prototype.appendBitArray=function(t){var e=t.size;this.ensureCapacity(this.size+e);for(var r=0;r<e;r++)this.appendBit(t.get(r))},t.prototype.xor=function(t){if(this.size!==t.size)throw new n.A("Sizes don't match");for(var e=this.bits,r=0,o=e.length;r<o;r++)e[r]^=t.bits[r]},t.prototype.toBytes=function(t,e,r,n){for(var o=0;o<n;o++){for(var i=0,a=0;a<8;a++)this.get(t)&&(i|=1<<7-a),t++;e[r+o]=i}},t.prototype.getBitArray=function(){return this.bits},t.prototype.reverse=function(){for(var t=new Int32Array(this.bits.length),e=Math.floor((this.size-1)/32),r=e+1,n=this.bits,o=0;o<r;o++){var i=n[o];i=(i=(i=(i=(i=i>>1&0x55555555|(0x55555555&i)<<1)>>2&0x33333333|(0x33333333&i)<<2)>>4&0xf0f0f0f|(0xf0f0f0f&i)<<4)>>8&0xff00ff|(0xff00ff&i)<<8)>>16&65535|(65535&i)<<16,t[e-o]=i}if(this.size!==32*r){for(var a=32*r-this.size,s=t[0]>>>a,o=1;o<r;o++){var u=t[o];s|=u<<32-a,t[o-1]=s,s=u>>>a}t[r-1]=s}this.bits=t},t.makeArray=function(t){return new Int32Array(Math.floor((t+31)/32))},t.prototype.equals=function(e){return e instanceof t&&this.size===e.size&&o.A.equals(this.bits,e.bits)},t.prototype.hashCode=function(){return 31*this.size+o.A.hashCode(this.bits)},t.prototype.toString=function(){for(var t="",e=0,r=this.size;e<r;e++)(7&e)==0&&(t+=" "),t+=this.get(e)?"X":".";return t},t.prototype.clone=function(){return new t(this.size,this.bits.slice())},t.prototype.toArray=function(){for(var t=[],e=0,r=this.size;e<r;e++)t.push(this.get(e));return t},t}()},23510:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(79417),o=r(59612),i=r(63623);let a=function(){function t(){}return t.castAsNonUtf8Char=function(t,e){void 0===e&&(e=null);var r=e?e.getName():this.ISO88591;return i.A.decode(new Uint8Array([t]),r)},t.guessEncoding=function(e,r){if(null!=r&&void 0!==r.get(n.A.CHARACTER_SET))return r.get(n.A.CHARACTER_SET).toString();for(var o=e.length,i=!0,a=!0,s=!0,u=0,c=0,f=0,h=0,l=0,d=0,p=0,A=0,g=0,y=0,w=0,v=e.length>3&&239===e[0]&&187===e[1]&&191===e[2],_=0;_<o&&(i||a||s);_++){var C=255&e[_];s&&(u>0?(128&C)==0?s=!1:u--:(128&C)!=0&&((64&C)==0?s=!1:(u++,(32&C)==0?c++:(u++,(16&C)==0?f++:(u++,(8&C)==0?h++:s=!1))))),i&&(C>127&&C<160?i=!1:C>159&&(C<192||215===C||247===C)&&w++),a&&(l>0?C<64||127===C||C>252?a=!1:l--:128===C||160===C||C>239?a=!1:C>160&&C<224?(d++,A=0,++p>g&&(g=p)):C>127?(l++,p=0,++A>y&&(y=A)):(p=0,A=0))}return(s&&u>0&&(s=!1),a&&l>0&&(a=!1),s&&(v||c+f+h>0))?t.UTF8:a&&(t.ASSUME_SHIFT_JIS||g>=3||y>=3)?t.SHIFT_JIS:i&&a?2===g&&2===d||10*w>=o?t.SHIFT_JIS:t.ISO88591:i?t.ISO88591:a?t.SHIFT_JIS:s?t.UTF8:t.PLATFORM_DEFAULT_ENCODING},t.format=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=-1;return t.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function(t,r,o,i,a,s){if("%%"===t)return"%";if(void 0!==e[++n]){t=i?parseInt(i.substr(1)):void 0;var u,c=a?parseInt(a.substr(1)):void 0;switch(s){case"s":u=e[n];break;case"c":u=e[n][0];break;case"f":u=parseFloat(e[n]).toFixed(t);break;case"p":u=parseFloat(e[n]).toPrecision(t);break;case"e":u=parseFloat(e[n]).toExponential(t);break;case"x":u=parseInt(e[n]).toString(c||16);break;case"d":u=parseFloat(parseInt(e[n],c||10).toPrecision(t)).toFixed(0)}u="object"==typeof u?JSON.stringify(u):(+u).toString(c);for(var f=parseInt(o),h=o&&o[0]+""=="0"?"0":" ";u.length<f;)u=void 0!==r?u+h:h+u;return u}})},t.getBytes=function(t,e){return i.A.encode(t,e)},t.getCharCode=function(t,e){return void 0===e&&(e=0),t.charCodeAt(e)},t.getCharAt=function(t){return String.fromCharCode(t)},t.SHIFT_JIS=o.A.SJIS.getName(),t.GB2312="GB2312",t.ISO88591=o.A.ISO8859_1.getName(),t.EUC_JP="EUC_JP",t.UTF8=o.A.UTF8.getName(),t.PLATFORM_DEFAULT_ENCODING=t.UTF8,t.ASSUME_SHIFT_JIS=!1,t}()},23583:(t,e,r)=>{"use strict";r.d(e,{A:()=>d});var n=r(22868),o=r(38988),i=r(23510),a=r(10782),s=r(61767),u=r(29477),c=r(55192),f=r(50942),h=r(63479),l=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let d=function(){function t(){}return t.encodeBytes=function(e){return t.encode(e,t.DEFAULT_EC_PERCENT,t.DEFAULT_AZTEC_LAYERS)},t.encode=function(e,r,n){var u,c,l,d,p,A,g=new f.A(e).encode(),y=h.A.truncDivision(g.getSize()*r,100)+11,w=g.getSize()+y;if(n!==t.DEFAULT_AZTEC_LAYERS){if(u=n<0,(c=Math.abs(n))>(u?t.MAX_NB_BITS_COMPACT:t.MAX_NB_BITS))throw new o.A(i.A.format("Illegal value %s for layers",n));var v=(l=t.totalBitsInLayer(c,u))-l%(d=t.WORD_SIZE[c]);if((p=t.stuffBits(g,d)).getSize()+y>v||u&&p.getSize()>64*d)throw new o.A("Data to large for user specified layer")}else{d=0,p=null;for(var _=0;;_++){if(_>t.MAX_NB_BITS)throw new o.A("Data too large for an Aztec code");if(c=(u=_<=3)?_+1:_,!(w>(l=t.totalBitsInLayer(c,u)))){(null==p||d!==t.WORD_SIZE[c])&&(d=t.WORD_SIZE[c],p=t.stuffBits(g,d));var v=l-l%d;if(!(u&&p.getSize()>64*d)&&p.getSize()+y<=v)break}}}var C=t.generateCheckWords(p,l,d),m=p.getSize()/d,E=t.generateModeMessage(u,c,m),I=(u?11:14)+4*c,S=new Int32Array(I);if(u){A=I;for(var _=0;_<S.length;_++)S[_]=_}else{A=I+1+2*h.A.truncDivision(h.A.truncDivision(I,2)-1,15);for(var T=h.A.truncDivision(I,2),O=h.A.truncDivision(A,2),_=0;_<T;_++){var R=_+h.A.truncDivision(_,15);S[T-_-1]=O-R-1,S[T+_]=O+R+1}}for(var b=new a.A(A),_=0,N=0;_<c;_++){for(var D=(c-_)*4+(u?9:12),M=0;M<D;M++)for(var P=2*M,B=0;B<2;B++)C.get(N+P+B)&&b.set(S[2*_+B],S[2*_+M]),C.get(N+2*D+P+B)&&b.set(S[2*_+M],S[I-1-2*_-B]),C.get(N+4*D+P+B)&&b.set(S[I-1-2*_-B],S[I-1-2*_-M]),C.get(N+6*D+P+B)&&b.set(S[I-1-2*_-M],S[2*_+B]);N+=8*D}if(t.drawModeMessage(b,u,A,E),u)t.drawBullsEye(b,h.A.truncDivision(A,2),5);else{t.drawBullsEye(b,h.A.truncDivision(A,2),7);for(var _=0,M=0;_<h.A.truncDivision(I,2)-1;_+=15,M+=16)for(var B=1&h.A.truncDivision(A,2);B<A;B+=2)b.set(h.A.truncDivision(A,2)-M,B),b.set(h.A.truncDivision(A,2)+M,B),b.set(B,h.A.truncDivision(A,2)-M),b.set(B,h.A.truncDivision(A,2)+M)}var L=new s.A;return L.setCompact(u),L.setSize(A),L.setLayers(c),L.setCodeWords(m),L.setMatrix(b),L},t.drawBullsEye=function(t,e,r){for(var n=0;n<r;n+=2)for(var o=e-n;o<=e+n;o++)t.set(o,e-n),t.set(o,e+n),t.set(e-n,o),t.set(e+n,o);t.set(e-r,e-r),t.set(e-r+1,e-r),t.set(e-r,e-r+1),t.set(e+r,e-r),t.set(e+r,e-r+1),t.set(e+r,e+r-1)},t.generateModeMessage=function(e,r,o){var i=new n.A;return e?(i.appendBits(r-1,2),i.appendBits(o-1,6),i=t.generateCheckWords(i,28,4)):(i.appendBits(r-1,5),i.appendBits(o-1,11),i=t.generateCheckWords(i,40,4)),i},t.drawModeMessage=function(t,e,r,n){var o=h.A.truncDivision(r,2);if(e)for(var i=0;i<7;i++){var a=o-3+i;n.get(i)&&t.set(a,o-5),n.get(i+7)&&t.set(o+5,a),n.get(20-i)&&t.set(a,o+5),n.get(27-i)&&t.set(o-5,a)}else for(var i=0;i<10;i++){var a=o-5+i+h.A.truncDivision(i,5);n.get(i)&&t.set(a,o-7),n.get(i+10)&&t.set(o+7,a),n.get(29-i)&&t.set(a,o+7),n.get(39-i)&&t.set(o-7,a)}},t.generateCheckWords=function(e,r,o){var i,a,s=e.getSize()/o,c=new u.A(t.getGF(o)),f=h.A.truncDivision(r,o),d=t.bitsToWords(e,o,f);c.encode(d,f-s);var p=r%o,A=new n.A;A.appendBits(0,p);try{for(var g=l(Array.from(d)),y=g.next();!y.done;y=g.next()){var w=y.value;A.appendBits(w,o)}}catch(t){i={error:t}}finally{try{y&&!y.done&&(a=g.return)&&a.call(g)}finally{if(i)throw i.error}}return A},t.bitsToWords=function(t,e,r){var n,o,i=new Int32Array(r);for(n=0,o=t.getSize()/e;n<o;n++){for(var a=0,s=0;s<e;s++)a|=t.get(n*e+s)?1<<e-s-1:0;i[n]=a}return i},t.getGF=function(t){switch(t){case 4:return c.A.AZTEC_PARAM;case 6:return c.A.AZTEC_DATA_6;case 8:return c.A.AZTEC_DATA_8;case 10:return c.A.AZTEC_DATA_10;case 12:return c.A.AZTEC_DATA_12;default:throw new o.A("Unsupported word size "+t)}},t.stuffBits=function(t,e){for(var r=new n.A,o=t.getSize(),i=(1<<e)-2,a=0;a<o;a+=e){for(var s=0,u=0;u<e;u++)(a+u>=o||t.get(a+u))&&(s|=1<<e-1-u);(s&i)===i?(r.appendBits(s&i,e),a--):(s&i)==0?(r.appendBits(1|s,e),a--):r.appendBits(s,e)}return r},t.totalBitsInLayer=function(t,e){return((e?88:112)+16*t)*t},t.DEFAULT_EC_PERCENT=33,t.DEFAULT_AZTEC_LAYERS=0,t.MAX_NB_BITS=32,t.MAX_NB_BITS_COMPACT=4,t.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12]),t}()},25879:(t,e,r)=>{"use strict";r.d(e,{b:()=>s});var n=r(23510),o=r(1933),i=r(93782),a=r(7779),s=function(){function t(){}return t.prototype.getEncodingMode=function(){return i.uf},t.prototype.encode=function(t){for(var e=new o.A;t.hasMoreCharacters();){var r=t.getCurrentChar();if(this.encodeChar(r,e),t.pos++,e.length()>=4){t.writeCodewords(this.encodeToCodewords(e.toString()));var s=e.toString().substring(4);if(e.setLengthToZero(),e.append(s),a.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(i.d2);break}}}e.append(n.A.getCharAt(31)),this.handleEOD(t,e)},t.prototype.handleEOD=function(t,e){try{var r=e.length();if(0===r)return;if(1===r){t.updateSymbolInfo();var n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),o=t.getRemainingCharacters();if(o>n&&(t.updateSymbolInfo(t.getCodewordCount()+1),n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount()),o<=n&&n<=2)return}if(r>4)throw Error("Count must not exceed 4");var a=r-1,s=this.encodeToCodewords(e.toString()),u=!t.hasMoreCharacters()&&a<=2;if(a<=2){t.updateSymbolInfo(t.getCodewordCount()+a);var n=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount();n>=3&&(u=!1,t.updateSymbolInfo(t.getCodewordCount()+s.length))}u?(t.resetSymbolInfo(),t.pos-=a):t.writeCodewords(s)}finally{t.signalEncoderChange(i.d2)}},t.prototype.encodeChar=function(t,e){t>=32&&t<=63?e.append(t):t>=64&&t<=94?e.append(n.A.getCharAt(t-64)):a.A.illegalCharacter(n.A.getCharAt(t))},t.prototype.encodeToCodewords=function(t){var e=t.length;if(0===e)throw Error("StringBuilder must not be empty");var r=(t.charAt(0).charCodeAt(0)<<18)+((e>=2?t.charAt(1).charCodeAt(0):0)<<12)+((e>=3?t.charAt(2).charCodeAt(0):0)<<6)+(e>=4?t.charAt(3).charCodeAt(0):0),n=new o.A;return n.append(r>>16&255),e>=2&&n.append(r>>8&255),e>=3&&n.append(255&r),n.toString()},t}()},25969:(t,e,r)=>{"use strict";var n;r.d(e,{A:()=>o}),function(t){t[t.AZTEC=0]="AZTEC",t[t.CODABAR=1]="CODABAR",t[t.CODE_39=2]="CODE_39",t[t.CODE_93=3]="CODE_93",t[t.CODE_128=4]="CODE_128",t[t.DATA_MATRIX=5]="DATA_MATRIX",t[t.EAN_8=6]="EAN_8",t[t.EAN_13=7]="EAN_13",t[t.ITF=8]="ITF",t[t.MAXICODE=9]="MAXICODE",t[t.PDF_417=10]="PDF_417",t[t.QR_CODE=11]="QR_CODE",t[t.RSS_14=12]="RSS_14",t[t.RSS_EXPANDED=13]="RSS_EXPANDED",t[t.UPC_A=14]="UPC_A",t[t.UPC_E=15]="UPC_E",t[t.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"}(n||(n={}));let o=n},26143:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n,o=r(38988);!function(t){t[t.TERMINATOR=0]="TERMINATOR",t[t.NUMERIC=1]="NUMERIC",t[t.ALPHANUMERIC=2]="ALPHANUMERIC",t[t.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",t[t.BYTE=4]="BYTE",t[t.ECI=5]="ECI",t[t.KANJI=6]="KANJI",t[t.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",t[t.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",t[t.HANZI=9]="HANZI"}(n||(n={}));let i=function(){function t(e,r,n,o){this.value=e,this.stringValue=r,this.characterCountBitsForVersions=n,this.bits=o,t.FOR_BITS.set(o,this),t.FOR_VALUE.set(e,this)}return t.forBits=function(e){var r=t.FOR_BITS.get(e);if(void 0===r)throw new o.A;return r},t.prototype.getCharacterCountBits=function(t){var e,r=t.getVersionNumber();return e=r<=9?0:r<=26?1:2,this.characterCountBitsForVersions[e]},t.prototype.getValue=function(){return this.value},t.prototype.getBits=function(){return this.bits},t.prototype.equals=function(e){return e instanceof t&&this.value===e.value},t.prototype.toString=function(){return this.stringValue},t.FOR_BITS=new Map,t.FOR_VALUE=new Map,t.TERMINATOR=new t(n.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),t.NUMERIC=new t(n.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),t.ALPHANUMERIC=new t(n.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),t.STRUCTURED_APPEND=new t(n.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),t.BYTE=new t(n.BYTE,"BYTE",Int32Array.from([8,16,16]),4),t.ECI=new t(n.ECI,"ECI",Int32Array.from([0,0,0]),7),t.KANJI=new t(n.KANJI,"KANJI",Int32Array.from([8,10,12]),8),t.FNC1_FIRST_POSITION=new t(n.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),t.FNC1_SECOND_POSITION=new t(n.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),t.HANZI=new t(n.HANZI,"HANZI",Int32Array.from([8,10,12]),13),t}()},26530:(t,e,r)=>{"use strict";r.r(e),r.d(e,{BrowserAztecCodeReader:()=>n.u,BrowserBarcodeReader:()=>o.c,BrowserCodeReader:()=>i.J,BrowserDatamatrixCodeReader:()=>a.w,BrowserMultiFormatReader:()=>s.s,BrowserPDF417Reader:()=>u.W,BrowserQRCodeReader:()=>c.w,BrowserQRCodeSvgWriter:()=>f.Q,HTMLCanvasElementLuminanceSource:()=>d.L,VideoInputDevice:()=>A.K});var n=r(8588),o=r(79540),i=r(11623),a=r(5106),s=r(17878),u=r(52428),c=r(36436),f=r(95108),h=r(71358),l={};for(let t in h)0>["default","BrowserAztecCodeReader","BrowserBarcodeReader","BrowserCodeReader","BrowserDatamatrixCodeReader","BrowserMultiFormatReader","BrowserPDF417Reader","BrowserQRCodeReader","BrowserQRCodeSvgWriter"].indexOf(t)&&(l[t]=()=>h[t]);r.d(e,l);var d=r(26745),p=r(88690),l={};for(let t in p)0>["default","BrowserAztecCodeReader","BrowserBarcodeReader","BrowserCodeReader","BrowserDatamatrixCodeReader","BrowserMultiFormatReader","BrowserPDF417Reader","BrowserQRCodeReader","BrowserQRCodeSvgWriter","HTMLCanvasElementLuminanceSource"].indexOf(t)&&(l[t]=()=>p[t]);r.d(e,l);var A=r(96768)},26745:(t,e,r)=>{"use strict";r.d(e,{L:()=>s});var n=r(64510),o=r(10997),i=r(38988),a=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),s=function(t){function e(r,n){void 0===n&&(n=!1);var o=t.call(this,r.width,r.height)||this;return o.canvas=r,o.tempCanvasElement=null,o.buffer=e.makeBufferFromCanvasImageData(r,n),o}return a(e,t),e.makeBufferFromCanvasImageData=function(t,r){void 0===r&&(r=!1);var n=t.getContext("2d").getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(n.data,t.width,t.height,r)},e.toGrayscaleBuffer=function(t,r,n,o){void 0===o&&(o=!1);var i=new Uint8ClampedArray(r*n);if(e.FRAME_INDEX=!e.FRAME_INDEX,e.FRAME_INDEX||!o)for(var a=0,s=0,u=t.length;a<u;a+=4,s++){var c=void 0,f=t[a+3];if(0===f)c=255;else{var h=t[a],l=t[a+1],d=t[a+2];c=306*h+601*l+117*d+512>>10}i[s]=c}else for(var a=0,s=0,p=t.length;a<p;a+=4,s++){var c=void 0,f=t[a+3];if(0===f)c=255;else{var h=t[a],l=t[a+1],d=t[a+2];c=306*h+601*l+117*d+512>>10}i[s]=255-c}return i},e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new i.A("Requested row is outside the image: "+t);var r=this.getWidth(),n=t*r;return null===e?e=this.buffer.slice(n,n+r):(e.length<r&&(e=new Uint8ClampedArray(r)),e.set(this.buffer.slice(n,n+r))),e},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(e,r,n,o){return t.prototype.crop.call(this,e,r,n,o),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.getTempCanvasElement=function(){if(null===this.tempCanvasElement){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var r=this.getTempCanvasElement(),n=r.getContext("2d"),o=t*e.DEGREE_TO_RADIANS,i=this.canvas.width,a=this.canvas.height,s=Math.ceil(Math.abs(Math.cos(o))*i+Math.abs(Math.sin(o))*a),u=Math.ceil(Math.abs(Math.sin(o))*i+Math.abs(Math.cos(o))*a);return r.width=s,r.height=u,n.translate(s/2,u/2),n.rotate(o),n.drawImage(this.canvas,-(i/2),-(a/2)),this.buffer=e.makeBufferFromCanvasImageData(r),this},e.prototype.invert=function(){return new n.A(this)},e.DEGREE_TO_RADIANS=Math.PI/180,e.FRAME_INDEX=!0,e}(o.A)},27051:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}return t.prototype.getSegmentIndex=function(){return this.segmentIndex},t.prototype.setSegmentIndex=function(t){this.segmentIndex=t},t.prototype.getFileId=function(){return this.fileId},t.prototype.setFileId=function(t){this.fileId=t},t.prototype.getOptionalData=function(){return this.optionalData},t.prototype.setOptionalData=function(t){this.optionalData=t},t.prototype.isLastSegment=function(){return this.lastSegment},t.prototype.setLastSegment=function(t){this.lastSegment=t},t.prototype.getSegmentCount=function(){return this.segmentCount},t.prototype.setSegmentCount=function(t){this.segmentCount=t},t.prototype.getSender=function(){return this.sender||null},t.prototype.setSender=function(t){this.sender=t},t.prototype.getAddressee=function(){return this.addressee||null},t.prototype.setAddressee=function(t){this.addressee=t},t.prototype.getFileName=function(){return this.fileName},t.prototype.setFileName=function(t){this.fileName=t},t.prototype.getFileSize=function(){return this.fileSize},t.prototype.setFileSize=function(t){this.fileSize=t},t.prototype.getChecksum=function(){return this.checksum},t.prototype.setChecksum=function(t){this.checksum=t},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.setTimestamp=function(t){this.timestamp=t},t}()},27217:(t,e,r)=>{"use strict";var n;r.d(e,{A:()=>o}),function(t){t[t.ERROR_CORRECTION=0]="ERROR_CORRECTION",t[t.CHARACTER_SET=1]="CHARACTER_SET",t[t.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",t[t.DATA_MATRIX_COMPACT=3]="DATA_MATRIX_COMPACT",t[t.MIN_SIZE=4]="MIN_SIZE",t[t.MAX_SIZE=5]="MAX_SIZE",t[t.MARGIN=6]="MARGIN",t[t.PDF417_COMPACT=7]="PDF417_COMPACT",t[t.PDF417_COMPACTION=8]="PDF417_COMPACTION",t[t.PDF417_DIMENSIONS=9]="PDF417_DIMENSIONS",t[t.AZTEC_LAYERS=10]="AZTEC_LAYERS",t[t.QR_VERSION=11]="QR_VERSION",t[t.GS1_FORMAT=12]="GS1_FORMAT",t[t.FORCE_C40=13]="FORCE_C40"}(n||(n={}));let o=n},27323:(t,e,r)=>{"use strict";r.d(e,{B:()=>s});var n=r(23510),o=r(1933),i=r(7779),a=r(93782),s=function(){function t(){}return t.prototype.getEncodingMode=function(){return a.mt},t.prototype.encode=function(t){var e=new o.A;for(e.append(0);t.hasMoreCharacters();){var r=t.getCurrentChar();if(e.append(r),t.pos++,i.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(a.d2);break}}var s=e.length()-1,u=t.getCodewordCount()+s+1;t.updateSymbolInfo(u);var c=t.getSymbolInfo().getDataCapacity()-u>0;if(t.hasMoreCharacters()||c)if(s<=249)e.setCharAt(0,n.A.getCharAt(s));else if(s<=1555)e.setCharAt(0,n.A.getCharAt(Math.floor(s/250)+249)),e.insert(1,n.A.getCharAt(s%250));else throw Error("Message length not in valid ranges: "+s);for(var f=0,r=e.length();f<r;f++)t.writeCodeword(this.randomize255State(e.charAt(f).charCodeAt(0),t.getCodewordCount()+1))},t.prototype.randomize255State=function(t,e){var r=t+(149*e%255+1);return r<=255?r:r-256},t}()},28822:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(25969),o=r(85917),i=r(438),a=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),s=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let u=function(t){function e(){var e=t.call(this)||this;return e.decodeMiddleCounters=Int32Array.from([0,0,0,0]),e}return a(e,t),e.prototype.decodeMiddle=function(t,r,n){var i,a,u,c,f=this.decodeMiddleCounters;f[0]=0,f[1]=0,f[2]=0,f[3]=0;for(var h=t.getSize(),l=r[1],d=0,p=0;p<6&&l<h;p++){var A=o.A.decodeDigit(t,f,l,o.A.L_AND_G_PATTERNS);n+=String.fromCharCode(48+A%10);try{for(var g=(i=void 0,s(f)),y=g.next();!y.done;y=g.next()){var w=y.value;l+=w}}catch(t){i={error:t}}finally{try{y&&!y.done&&(a=g.return)&&a.call(g)}finally{if(i)throw i.error}}A>=10&&(d|=1<<5-p)}n=e.determineFirstDigit(n,d),l=o.A.findGuardPattern(t,l,!0,o.A.MIDDLE_PATTERN,new Int32Array(o.A.MIDDLE_PATTERN.length).fill(0))[1];for(var p=0;p<6&&l<h;p++){var A=o.A.decodeDigit(t,f,l,o.A.L_PATTERNS);n+=String.fromCharCode(48+A);try{for(var v=(u=void 0,s(f)),_=v.next();!_.done;_=v.next()){var w=_.value;l+=w}}catch(t){u={error:t}}finally{try{_&&!_.done&&(c=v.return)&&c.call(v)}finally{if(u)throw u.error}}}return{rowOffset:l,resultString:n}},e.prototype.getBarcodeFormat=function(){return n.A.EAN_13},e.determineFirstDigit=function(t,e){for(var r=0;r<10;r++)if(e===this.FIRST_DIGIT_ENCODINGS[r])return t=String.fromCharCode(48+r)+t;throw new i.A},e.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26],e}(o.A)},29477:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(34382),o=r(322),i=r(38988);let a=function(){function t(t){this.field=t,this.cachedGenerators=[],this.cachedGenerators.push(new n.A(t,Int32Array.from([1])))}return t.prototype.buildGenerator=function(t){var e=this.cachedGenerators;if(t>=e.length)for(var r=e[e.length-1],o=this.field,i=e.length;i<=t;i++){var a=r.multiply(new n.A(o,Int32Array.from([1,o.exp(i-1+o.getGeneratorBase())])));e.push(a),r=a}return e[t]},t.prototype.encode=function(t,e){if(0===e)throw new i.A("No error correction bytes");var r=t.length-e;if(r<=0)throw new i.A("No data bytes provided");var a=this.buildGenerator(e),s=new Int32Array(r);o.A.arraycopy(t,0,s,0,r);for(var u=new n.A(this.field,s),c=(u=u.multiplyByMonomial(e,1)).divide(a)[1].getCoefficients(),f=e-c.length,h=0;h<f;h++)t[r+h]=0;o.A.arraycopy(c,0,t,r+f,c.length)},t}()},33638:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(38988);let o=function(){function t(){}return t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new n.A;return this.logTable[t]},t.addOrSubtract=function(t,e){return t^e},t}()},34382:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(33638),o=r(322),i=r(38988);let a=function(){function t(t,e){if(0===e.length)throw new i.A;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var n=1;n<r&&0===e[n];)n++;n===r?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(r-n),o.A.arraycopy(e,n,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){if(0===t)return this.getCoefficient(0);var e,r=this.coefficients;if(1===t){e=0;for(var o=0,i=r.length;o!==i;o++){var a=r[o];e=n.A.addOrSubtract(e,a)}return e}e=r[0];for(var s=r.length,u=this.field,o=1;o<s;o++)e=n.A.addOrSubtract(u.multiply(t,e),r[o]);return e},t.prototype.addOrSubtract=function(e){if(!this.field.equals(e.field))throw new i.A("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,a=e.coefficients;if(r.length>a.length){var s=r;r=a,a=s}var u=new Int32Array(a.length),c=a.length-r.length;o.A.arraycopy(a,0,u,0,c);for(var f=c;f<a.length;f++)u[f]=n.A.addOrSubtract(r[f-c],a[f]);return new t(this.field,u)},t.prototype.multiply=function(e){if(!this.field.equals(e.field))throw new i.A("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();for(var r=this.coefficients,o=r.length,a=e.coefficients,s=a.length,u=new Int32Array(o+s-1),c=this.field,f=0;f<o;f++)for(var h=r[f],l=0;l<s;l++)u[f+l]=n.A.addOrSubtract(u[f+l],c.multiply(h,a[l]));return new t(c,u)},t.prototype.multiplyScalar=function(e){if(0===e)return this.field.getZero();if(1===e)return this;for(var r=this.coefficients.length,n=this.field,o=new Int32Array(r),i=this.coefficients,a=0;a<r;a++)o[a]=n.multiply(i[a],e);return new t(n,o)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new i.A;if(0===r)return this.field.getZero();for(var n=this.coefficients,o=n.length,a=new Int32Array(o+e),s=this.field,u=0;u<o;u++)a[u]=s.multiply(n[u],r);return new t(s,a)},t.prototype.divide=function(t){if(!this.field.equals(t.field))throw new i.A("GenericGFPolys do not have same GenericGF field");if(t.isZero())throw new i.A("Divide by 0");for(var e=this.field,r=e.getZero(),n=this,o=t.getCoefficient(t.getDegree()),a=e.inverse(o);n.getDegree()>=t.getDegree()&&!n.isZero();){var s=n.getDegree()-t.getDegree(),u=e.multiply(n.getCoefficient(n.getDegree()),a),c=t.multiplyByMonomial(s,u),f=e.buildMonomial(s,u);r=r.addOrSubtract(f),n=n.addOrSubtract(c)}return[r,n]},t.prototype.toString=function(){for(var t="",e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);if(0!==r){if(r<0?(t+=" - ",r=-r):t.length>0&&(t+=" + "),0===e||1!==r){var n=this.field.log(r);0===n?t+="1":1===n?t+="a":(t+="a^",t+=n)}0!==e&&(1===e?t+="x":(t+="x^",t+=e))}}return t},t}()},35168:(t,e,r)=>{"use strict";r.d(e,{A:()=>w});var n=r(27217),o=r(22868),i=r(59612),a=r(55192),s=r(29477),u=r(26143),c=r(21876),f=r(85808),h=r(52771),l=r(6974),d=r(6537),p=r(63623),A=function(){function t(t,e){this.dataBytes=t,this.errorCorrectionBytes=e}return t.prototype.getDataBytes=function(){return this.dataBytes},t.prototype.getErrorCorrectionBytes=function(){return this.errorCorrectionBytes},t}(),g=r(93682),y=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let w=function(){function t(){}return t.calculateMaskPenalty=function(t){return f.A.applyMaskPenaltyRule1(t)+f.A.applyMaskPenaltyRule2(t)+f.A.applyMaskPenaltyRule3(t)+f.A.applyMaskPenaltyRule4(t)},t.encode=function(e,r,a){void 0===a&&(a=null);var s,f=t.DEFAULT_BYTE_MODE_ENCODING,p=null!==a&&void 0!==a.get(n.A.CHARACTER_SET);p&&(f=a.get(n.A.CHARACTER_SET).toString());var A=this.chooseMode(e,f),y=new o.A;if(A===u.A.BYTE&&(p||t.DEFAULT_BYTE_MODE_ENCODING!==f)){var w=i.A.getCharacterSetECIByName(f);void 0!==w&&this.appendECI(w,y)}this.appendModeInfo(A,y);var v=new o.A;if(this.appendBytes(e,A,v,f),null!==a&&void 0!==a.get(n.A.QR_VERSION)){var _=Number.parseInt(a.get(n.A.QR_VERSION).toString(),10);s=c.A.getVersionForNumber(_);var C=this.calculateBitsNeeded(A,y,v,s);if(!this.willFit(C,s,r))throw new g.A("Data too big for requested version")}else s=this.recommendVersion(r,A,y,v);var m=new o.A;m.appendBitArray(y);var E=A===u.A.BYTE?v.getSizeInBytes():e.length;this.appendLengthInfo(E,s,A,m),m.appendBitArray(v);var I=s.getECBlocksForLevel(r),S=s.getTotalCodewords()-I.getTotalECCodewords();this.terminateBits(S,m);var T=this.interleaveWithECBytes(m,s.getTotalCodewords(),S,I.getNumBlocks()),O=new l.A;O.setECLevel(r),O.setMode(A),O.setVersion(s);var R=s.getDimensionForVersion(),b=new h.A(R,R),N=this.chooseMaskPattern(T,r,s,b);return O.setMaskPattern(N),d.A.buildMatrix(T,r,s,N,b),O.setMatrix(b),O},t.recommendVersion=function(t,e,r,n){var o=this.calculateBitsNeeded(e,r,n,c.A.getVersionForNumber(1)),i=this.chooseVersion(o,t),a=this.calculateBitsNeeded(e,r,n,i);return this.chooseVersion(a,t)},t.calculateBitsNeeded=function(t,e,r,n){return e.getSize()+t.getCharacterCountBits(n)+r.getSize()},t.getAlphanumericCode=function(e){return e<t.ALPHANUMERIC_TABLE.length?t.ALPHANUMERIC_TABLE[e]:-1},t.chooseMode=function(e,r){if(void 0===r&&(r=null),i.A.SJIS.getName()===r&&this.isOnlyDoubleByteKanji(e))return u.A.KANJI;for(var n=!1,o=!1,a=0,s=e.length;a<s;++a){var c=e.charAt(a);if(t.isDigit(c))n=!0;else{if(-1===this.getAlphanumericCode(c.charCodeAt(0)))return u.A.BYTE;o=!0}}return o?u.A.ALPHANUMERIC:n?u.A.NUMERIC:u.A.BYTE},t.isOnlyDoubleByteKanji=function(t){try{e=p.A.encode(t,i.A.SJIS)}catch(t){return!1}var e,r=e.length;if(r%2!=0)return!1;for(var n=0;n<r;n+=2){var o=255&e[n];if((o<129||o>159)&&(o<224||o>235))return!1}return!0},t.chooseMaskPattern=function(t,e,r,n){for(var o=Number.MAX_SAFE_INTEGER,i=-1,a=0;a<l.A.NUM_MASK_PATTERNS;a++){d.A.buildMatrix(t,e,r,a,n);var s=this.calculateMaskPenalty(n);s<o&&(o=s,i=a)}return i},t.chooseVersion=function(e,r){for(var n=1;n<=40;n++){var o=c.A.getVersionForNumber(n);if(t.willFit(e,o,r))return o}throw new g.A("Data too big")},t.willFit=function(t,e,r){return e.getTotalCodewords()-e.getECBlocksForLevel(r).getTotalECCodewords()>=(t+7)/8},t.terminateBits=function(t,e){var r=8*t;if(e.getSize()>r)throw new g.A("data bits cannot fit in the QR Code"+e.getSize()+" > "+r);for(var n=0;n<4&&e.getSize()<r;++n)e.appendBit(!1);var o=7&e.getSize();if(o>0)for(var n=o;n<8;n++)e.appendBit(!1);for(var i=t-e.getSizeInBytes(),n=0;n<i;++n)e.appendBits((1&n)==0?236:17,8);if(e.getSize()!==r)throw new g.A("Bits size does not equal capacity")},t.getNumDataBytesAndNumECBytesForBlockID=function(t,e,r,n,o,i){if(n>=r)throw new g.A("Block ID too large");var a=t%r,s=r-a,u=Math.floor(t/r),c=Math.floor(e/r),f=c+1,h=u-c,l=u+1-f;if(h!==l)throw new g.A("EC bytes mismatch");if(r!==s+a)throw new g.A("RS blocks mismatch");if(t!==(c+h)*s+(f+l)*a)throw new g.A("Total bytes mismatch");n<s?(o[0]=c,i[0]=h):(o[0]=f,i[0]=l)},t.interleaveWithECBytes=function(e,r,n,i){if(e.getSizeInBytes()!==n)throw new g.A("Number of bits and data bytes does not match");for(var a,s,u,c,f=0,h=0,l=0,d=[],p=0;p<i;++p){var w=new Int32Array(1),v=new Int32Array(1);t.getNumDataBytesAndNumECBytesForBlockID(r,n,i,p,w,v);var _=w[0],C=new Uint8Array(_);e.toBytes(8*f,C,0,_);var m=t.generateECBytes(C,v[0]);d.push(new A(C,m)),h=Math.max(h,_),l=Math.max(l,m.length),f+=w[0]}if(n!==f)throw new g.A("Data bytes does not match offset");for(var E=new o.A,p=0;p<h;++p)try{for(var I=(a=void 0,y(d)),S=I.next();!S.done;S=I.next()){var T=S.value,C=T.getDataBytes();p<C.length&&E.appendBits(C[p],8)}}catch(t){a={error:t}}finally{try{S&&!S.done&&(s=I.return)&&s.call(I)}finally{if(a)throw a.error}}for(var p=0;p<l;++p)try{for(var O=(u=void 0,y(d)),R=O.next();!R.done;R=O.next()){var T=R.value,m=T.getErrorCorrectionBytes();p<m.length&&E.appendBits(m[p],8)}}catch(t){u={error:t}}finally{try{R&&!R.done&&(c=O.return)&&c.call(O)}finally{if(u)throw u.error}}if(r!==E.getSizeInBytes())throw new g.A("Interleaving error: "+r+" and "+E.getSizeInBytes()+" differ.");return E},t.generateECBytes=function(t,e){for(var r=t.length,n=new Int32Array(r+e),o=0;o<r;o++)n[o]=255&t[o];new s.A(a.A.QR_CODE_FIELD_256).encode(n,e);for(var i=new Uint8Array(e),o=0;o<e;o++)i[o]=n[r+o];return i},t.appendModeInfo=function(t,e){e.appendBits(t.getBits(),4)},t.appendLengthInfo=function(t,e,r,n){var o=r.getCharacterCountBits(e);if(t>=1<<o)throw new g.A(t+" is bigger than "+((1<<o)-1));n.appendBits(t,o)},t.appendBytes=function(e,r,n,o){switch(r){case u.A.NUMERIC:t.appendNumericBytes(e,n);break;case u.A.ALPHANUMERIC:t.appendAlphanumericBytes(e,n);break;case u.A.BYTE:t.append8BitBytes(e,n,o);break;case u.A.KANJI:t.appendKanjiBytes(e,n);break;default:throw new g.A("Invalid mode: "+r)}},t.getDigit=function(t){return t.charCodeAt(0)-48},t.isDigit=function(e){var r=t.getDigit(e);return r>=0&&r<=9},t.appendNumericBytes=function(e,r){for(var n=e.length,o=0;o<n;){var i=t.getDigit(e.charAt(o));if(o+2<n){var a=t.getDigit(e.charAt(o+1)),s=t.getDigit(e.charAt(o+2));r.appendBits(100*i+10*a+s,10),o+=3}else if(o+1<n){var a=t.getDigit(e.charAt(o+1));r.appendBits(10*i+a,7),o+=2}else r.appendBits(i,4),o++}},t.appendAlphanumericBytes=function(e,r){for(var n=e.length,o=0;o<n;){var i=t.getAlphanumericCode(e.charCodeAt(o));if(-1===i)throw new g.A;if(o+1<n){var a=t.getAlphanumericCode(e.charCodeAt(o+1));if(-1===a)throw new g.A;r.appendBits(45*i+a,11),o+=2}else r.appendBits(i,6),o++}},t.append8BitBytes=function(t,e,r){var n;try{n=p.A.encode(t,r)}catch(t){throw new g.A(t)}for(var o=0,i=n.length;o!==i;o++){var a=n[o];e.appendBits(a,8)}},t.appendKanjiBytes=function(t,e){try{r=p.A.encode(t,i.A.SJIS)}catch(t){throw new g.A(t)}for(var r,n=r.length,o=0;o<n;o+=2){var a=(255&r[o])<<8|255&r[o+1],s=-1;if(a>=33088&&a<=40956?s=a-33088:a>=57408&&a<=60351&&(s=a-49472),-1===s)throw new g.A("Invalid byte sequence");var u=(s>>8)*192+(255&s);e.appendBits(u,13)}},t.appendECI=function(t,e){e.appendBits(u.A.ECI.getBits(),4),e.appendBits(t.getValue(),8)},t.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),t.DEFAULT_BYTE_MODE_ENCODING=i.A.UTF8.getName(),t}()},35509:(t,e,r)=>{"use strict";r.d(e,{A:()=>P});var n,o=r(25969),i=r(10782),a=r(27217),s=r(52771),u=r(2257);r(58892),r(27323),r(39894);var c=r(73693);r(25879),r(49202);var f=r(55277),h=r(7779),l=r(93782),d=r(91375),p=r(63623),A=r(23510),g=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},y=function(){function t(t){this.charset=t,this.name=t.name}return t.prototype.canEncode=function(t){try{return null!=p.A.encode(t,this.charset)}catch(t){return!1}},t}(),w=function(){function t(t,e,r){this.ENCODERS=["IBM437","ISO-8859-2","ISO-8859-3","ISO-8859-4","ISO-8859-5","ISO-8859-6","ISO-8859-7","ISO-8859-8","ISO-8859-9","ISO-8859-10","ISO-8859-11","ISO-8859-13","ISO-8859-14","ISO-8859-15","ISO-8859-16","windows-1250","windows-1251","windows-1252","windows-1256","Shift_JIS"].map(function(t){return new y(u.A.forName(t))}),this.encoders=[];var n,o,i,a,s,c,f=[];f.push(new y(d.A.ISO_8859_1));for(var h=null!=e&&e.name.startsWith("UTF"),l=0;l<t.length;l++){var p=!1;try{for(var A=(n=void 0,g(f)),w=A.next();!w.done;w=A.next()){var v=w.value,_=t.charAt(l);if(_.charCodeAt(0)===r||v.canEncode(_)){p=!0;break}}}catch(t){n={error:t}}finally{try{w&&!w.done&&(o=A.return)&&o.call(A)}finally{if(n)throw n.error}}if(!p)try{for(var C=(i=void 0,g(this.ENCODERS)),m=C.next();!m.done;m=C.next()){var v=m.value;if(v.canEncode(t.charAt(l))){f.push(v),p=!0;break}}}catch(t){i={error:t}}finally{try{m&&!m.done&&(a=C.return)&&a.call(C)}finally{if(i)throw i.error}}p||(h=!0)}if(1!==f.length||h){this.encoders=[];var E=0;try{for(var I=g(f),S=I.next();!S.done;S=I.next()){var v=S.value;this.encoders[E++]=v}}catch(t){s={error:t}}finally{try{S&&!S.done&&(c=I.return)&&c.call(I)}finally{if(s)throw s.error}}}else this.encoders=[f[0]];var T=-1;if(null!=e){for(var l=0;l<this.encoders.length;l++)if(null!=this.encoders[l]&&e.name===this.encoders[l].name){T=l;break}}this.priorityEncoderIndex=T}return t.prototype.length=function(){return this.encoders.length},t.prototype.getCharsetName=function(t){if(!(t<this.length()))throw Error("index must be less than length");return this.encoders[t].name},t.prototype.getCharset=function(t){if(!(t<this.length()))throw Error("index must be less than length");return this.encoders[t].charset},t.prototype.getECIValue=function(t){return this.encoders[t].charset.getValueIdentifier()},t.prototype.getPriorityEncoderIndex=function(){return this.priorityEncoderIndex},t.prototype.canEncode=function(t,e){if(!(e<this.length()))throw Error("index must be less than length");return!0},t.prototype.encode=function(t,e){if(!(e<this.length()))throw Error("index must be less than length");return p.A.encode(A.A.getCharAt(t),this.encoders[e].name)},t}(),v=r(63479),_=r(1933),C=function(){function t(t,e,r){this.fnc1=r;var n=new w(t,e,r);if(1===n.length())for(var o=0;o<this.bytes.length;o++){var i=t.charAt(o).charCodeAt(0);this.bytes[o]=i===r?1e3:i}else this.bytes=this.encodeMinimally(t,n,r)}return t.prototype.getFNC1Character=function(){return this.fnc1},t.prototype.length=function(){return this.bytes.length},t.prototype.haveNCharacters=function(t,e){if(t+e-1>=this.bytes.length)return!1;for(var r=0;r<e;r++)if(this.isECI(t+r))return!1;return!0},t.prototype.charAt=function(t){if(t<0||t>=this.length())throw Error(""+t);if(this.isECI(t))throw Error("value at "+t+" is not a character but an ECI");return this.isFNC1(t)?this.fnc1:this.bytes[t]},t.prototype.subSequence=function(t,e){if(t<0||t>e||e>this.length())throw Error(""+t);for(var r=new _.A,n=t;n<e;n++){if(this.isECI(n))throw Error("value at "+n+" is not a character but an ECI");r.append(this.charAt(n))}return r.toString()},t.prototype.isECI=function(t){if(t<0||t>=this.length())throw Error(""+t);return this.bytes[t]>255&&this.bytes[t]<=999},t.prototype.isFNC1=function(t){if(t<0||t>=this.length())throw Error(""+t);return 1e3===this.bytes[t]},t.prototype.getECIValue=function(t){if(t<0||t>=this.length())throw Error(""+t);if(!this.isECI(t))throw Error("value at "+t+" is not an ECI but a character");return this.bytes[t]-256},t.prototype.addEdge=function(t,e,r){(null==t[e][r.encoderIndex]||t[e][r.encoderIndex].cachedTotalSize>r.cachedTotalSize)&&(t[e][r.encoderIndex]=r)},t.prototype.addEdges=function(t,e,r,n,o,i){var a=t.charAt(n).charCodeAt(0),s=0,u=e.length();e.getPriorityEncoderIndex()>=0&&(a===i||e.canEncode(a,e.getPriorityEncoderIndex()))&&(u=(s=e.getPriorityEncoderIndex())+1);for(var c=s;c<u;c++)(a===i||e.canEncode(a,c))&&this.addEdge(r,n+1,new m(a,e,c,o,i))},t.prototype.encodeMinimally=function(t,e,r){var n=t.length,o=new m[n+1][e.length()];this.addEdges(t,e,o,0,null,r);for(var i=1;i<=n;i++){for(var a=0;a<e.length();a++)null!=o[i][a]&&i<n&&this.addEdges(t,e,o,i,o[i][a],r);for(var a=0;a<e.length();a++)o[i-1][a]=null}for(var s=-1,u=v.A.MAX_VALUE,a=0;a<e.length();a++)if(null!=o[n][a]){var c=o[n][a];c.cachedTotalSize<u&&(u=c.cachedTotalSize,s=a)}if(s<0)throw Error('Failed to encode "'+t+'"');for(var f=[],h=o[n][s];null!=h;){if(h.isFNC1())f.unshift(1e3);else for(var l=e.encode(h.c,h.encoderIndex),i=l.length-1;i>=0;i--)f.unshift(255&l[i]);(null===h.previous?0:h.previous.encoderIndex)!==h.encoderIndex&&f.unshift(256+e.getECIValue(h.encoderIndex)),h=h.previous}for(var d=[],i=0;i<d.length;i++)d[i]=f[i];return d},t}(),m=function(){function t(t,e,r,n,o){this.c=t,this.encoderSet=e,this.encoderIndex=r,this.previous=n,this.fnc1=o,this.c=t===o?1e3:t;var i=this.isFNC1()?1:e.encode(t,r).length;(null===n?0:n.encoderIndex)!==r&&(i+=3),null!=n&&(i+=n.cachedTotalSize),this.cachedTotalSize=i}return t.prototype.isFNC1=function(){return 1e3===this.c},t}(),E=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),I=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},S=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},T=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(S(arguments[e]));return t};!function(t){t[t.ASCII=0]="ASCII",t[t.C40=1]="C40",t[t.TEXT=2]="TEXT",t[t.X12=3]="X12",t[t.EDF=4]="EDF",t[t.B256=5]="B256"}(n||(n={}));var O=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],R=function(){function t(){}return t.isExtendedASCII=function(t,e){return t!==e&&t>=128&&t<=255},t.isInC40Shift1Set=function(t){return t<=31},t.isInC40Shift2Set=function(t,e){var r,n;try{for(var o=I(O),i=o.next();!i.done;i=o.next())if(i.value.charCodeAt(0)===t)return!0}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return t===e},t.isInTextShift1Set=function(t){return this.isInC40Shift1Set(t)},t.isInTextShift2Set=function(t,e){return this.isInC40Shift2Set(t,e)},t.encodeHighLevel=function(t,e,r,n){void 0===e&&(e=null),void 0===r&&(r=-1),void 0===n&&(n=0);var o=0;return t.startsWith(l.h_)&&t.endsWith(l.TG)?(o=5,t=t.substring(l.h_.length,t.length-2)):t.startsWith(l.eB)&&t.endsWith(l.TG)&&(o=6,t=t.substring(l.eB.length,t.length-2)),decodeURIComponent(escape(String.fromCharCode.apply(String,T(this.encode(t,e,r,n,o)))))},t.encode=function(t,e,r,n,o){return this.encodeMinimally(new D(t,e,r,n,o)).getBytes()},t.addEdge=function(t,e){var r=e.fromPosition+e.characterLength;(null===t[r][e.getEndMode()]||t[r][e.getEndMode()].cachedTotalSize>e.cachedTotalSize)&&(t[r][e.getEndMode()]=e)},t.getNumberOfC40Words=function(e,r,n,o){for(var i=0,a=r;a<e.length()&&!e.isECI(a);a++){;var s=e.charAt(a);if(n&&h.A.isNativeC40(s)||!n&&h.A.isNativeText(s))i++;else if(t.isExtendedASCII(s,e.getFNC1Character())){var u=255&s;u>=128&&(n&&h.A.isNativeC40(u-128)||!n&&h.A.isNativeText(u-128))?i+=3:i+=4}else i+=2;if(i%3==0||(i-2)%3==0&&a+1===e.length())return o[0]=a-r+1,Math.ceil(i/3)}return o[0]=0,0},t.addEdges=function(e,r,o,i){if(e.isECI(o))return void this.addEdge(r,new N(e,n.ASCII,o,1,i));var a,s,u,c=e.charAt(o);if(null===i||i.getEndMode()!==n.EDF){h.A.isDigit(c)&&e.haveNCharacters(o,2)&&h.A.isDigit(e.charAt(o+1))?this.addEdge(r,new N(e,n.ASCII,o,2,i)):this.addEdge(r,new N(e,n.ASCII,o,1,i));var f=[n.C40,n.TEXT];try{for(var l=I(f),d=l.next();!d.done;d=l.next()){var p=d.value,A=[];t.getNumberOfC40Words(e,o,p===n.C40,A)>0&&this.addEdge(r,new N(e,p,o,A[0],i))}}catch(t){a={error:t}}finally{try{d&&!d.done&&(s=l.return)&&s.call(l)}finally{if(a)throw a.error}}e.haveNCharacters(o,3)&&h.A.isNativeX12(e.charAt(o))&&h.A.isNativeX12(e.charAt(o+1))&&h.A.isNativeX12(e.charAt(o+2))&&this.addEdge(r,new N(e,n.X12,o,3,i)),this.addEdge(r,new N(e,n.B256,o,1,i))}for(u=0;u<3;u++){var g=o+u;if(e.haveNCharacters(g,1)&&h.A.isNativeEDIFACT(e.charAt(g)))this.addEdge(r,new N(e,n.EDF,o,u+1,i));else break}3===u&&e.haveNCharacters(o,4)&&h.A.isNativeEDIFACT(e.charAt(o+3))&&this.addEdge(r,new N(e,n.EDF,o,4,i))},t.encodeMinimally=function(t){var e=t.length(),r=Array(e+1).fill(null).map(function(){return Array(6).fill(0)});this.addEdges(t,r,0,null);for(var n=1;n<=e;n++){for(var o=0;o<6;o++)null!==r[n][o]&&n<e&&this.addEdges(t,r,n,r[n][o]);for(var o=0;o<6;o++)r[n-1][o]=null}for(var i=-1,a=v.A.MAX_VALUE,o=0;o<6;o++)if(null!==r[e][o]){var s=r[e][o],u=o>=1&&o<=3?s.cachedTotalSize+1:s.cachedTotalSize;u<a&&(a=u,i=o)}if(i<0)throw Error('Failed to encode "'+t+'"');return new b(r[e][i])},t}(),b=function(){function t(t){var e=t.input,r=0,o=[],i=[],a=[];(t.mode===n.C40||t.mode===n.TEXT||t.mode===n.X12)&&t.getEndMode()!==n.ASCII&&(r+=this.prepend(N.getBytes(254),o));for(var s=t;null!==s;)r+=this.prepend(s.getDataBytes(),o),(null===s.previous||s.getPreviousStartMode()!==s.getMode())&&(s.getMode()===n.B256&&(r<=249?(o.unshift(r),r++):(o.unshift(r%250),o.unshift(r/250+249),r+=2),i.push(o.length),a.push(r)),this.prepend(s.getLatchBytes(),o),r=0),s=s.previous;5===e.getMacroId()?r+=this.prepend(N.getBytes(236),o):6===e.getMacroId()&&(r+=this.prepend(N.getBytes(237),o)),e.getFNC1Character()>0&&(r+=this.prepend(N.getBytes(232),o));for(var u=0;u<i.length;u++)this.applyRandomPattern(o,o.length-i[u],a[u]);var c=t.getMinSymbolSize(o.length);for(o.length<c&&o.push(129);o.length<c;)o.push(this.randomize253State(o.length+1));this.bytes=new Uint8Array(o.length);for(var u=0;u<this.bytes.length;u++)this.bytes[u]=o[u]}return t.prototype.prepend=function(t,e){for(var r=t.length-1;r>=0;r--)e.unshift(t[r]);return t.length},t.prototype.randomize253State=function(t){var e=129+(149*t%253+1);return e<=254?e:e-254},t.prototype.applyRandomPattern=function(t,e,r){for(var n=0;n<r;n++){var o=e+n,i=(255&t[o])+(149*(o+1)%255+1);t[o]=i<=255?i:i-256}},t.prototype.getBytes=function(){return this.bytes},t}(),N=function(){function t(t,e,r,o,i){if(this.input=t,this.mode=e,this.fromPosition=r,this.characterLength=o,this.previous=i,this.allCodewordCapacities=[3,5,8,10,12,16,18,22,30,32,36,44,49,62,86,114,144,174,204,280,368,456,576,696,816,1050,1304,1558],this.squareCodewordCapacities=[3,5,8,12,18,22,30,36,44,62,86,114,144,174,204,280,368,456,576,696,816,1050,1304,1558],this.rectangularCodewordCapacities=[5,10,16,33,32,49],!(r+o<=t.length()))throw Error("Invalid edge");var a=null!==i?i.cachedTotalSize:0,s=this.getPreviousMode();switch(e){case n.ASCII:a++,(t.isECI(r)||R.isExtendedASCII(t.charAt(r),t.getFNC1Character()))&&a++,(s===n.C40||s===n.TEXT||s===n.X12)&&a++;break;case n.B256:a++,s!==n.B256?a++:250===this.getB256Size()&&a++,s===n.ASCII?a++:(s===n.C40||s===n.TEXT||s===n.X12)&&(a+=2);break;case n.C40:case n.TEXT:case n.X12:e===n.X12?a+=2:a+=2*R.getNumberOfC40Words(t,r,e===n.C40,[]),s===n.ASCII||s===n.B256?a++:s!==e&&(s===n.C40||s===n.TEXT||s===n.X12)&&(a+=2);break;case n.EDF:a+=3,s===n.ASCII||s===n.B256?a++:(s===n.C40||s===n.TEXT||s===n.X12)&&(a+=2)}this.cachedTotalSize=a}return t.prototype.getB256Size=function(){for(var t=0,e=this;null!==e&&e.mode===n.B256&&t<=250;)t++,e=e.previous;return t},t.prototype.getPreviousStartMode=function(){return null===this.previous?n.ASCII:this.previous.mode},t.prototype.getPreviousMode=function(){return null===this.previous?n.ASCII:this.previous.getEndMode()},t.prototype.getEndMode=function(){if(this.mode===n.EDF){if(this.characterLength<4)return n.ASCII;var t=this.getLastASCII();if(t>0&&this.getCodewordsRemaining(this.cachedTotalSize+t)<=2-t)return n.ASCII}if(this.mode===n.C40||this.mode===n.TEXT||this.mode===n.X12){if(this.fromPosition+this.characterLength>=this.input.length()&&0===this.getCodewordsRemaining(this.cachedTotalSize))return n.ASCII;var t=this.getLastASCII();if(1===t&&0===this.getCodewordsRemaining(this.cachedTotalSize+1))return n.ASCII}return this.mode},t.prototype.getMode=function(){return this.mode},t.prototype.getLastASCII=function(){var t=this.input.length(),e=this.fromPosition+this.characterLength;return t-e>4||e>=t?0:t-e==1?+!R.isExtendedASCII(this.input.charAt(e),this.input.getFNC1Character()):t-e==2?R.isExtendedASCII(this.input.charAt(e),this.input.getFNC1Character())||R.isExtendedASCII(this.input.charAt(e+1),this.input.getFNC1Character())?0:h.A.isDigit(this.input.charAt(e))&&h.A.isDigit(this.input.charAt(e+1))?1:2:t-e==3?h.A.isDigit(this.input.charAt(e))&&h.A.isDigit(this.input.charAt(e+1))&&!R.isExtendedASCII(this.input.charAt(e+2),this.input.getFNC1Character())||h.A.isDigit(this.input.charAt(e+1))&&h.A.isDigit(this.input.charAt(e+2))&&!R.isExtendedASCII(this.input.charAt(e),this.input.getFNC1Character())?2:0:h.A.isDigit(this.input.charAt(e))&&h.A.isDigit(this.input.charAt(e+1))&&h.A.isDigit(this.input.charAt(e+2))&&h.A.isDigit(this.input.charAt(e+3))?2:0},t.prototype.getMinSymbolSize=function(t){var e,r,n,o,i,a;switch(this.input.getShapeHint()){case 1:try{for(var s=I(this.squareCodewordCapacities),u=s.next();!u.done;u=s.next()){var c=u.value;if(c>=t)return c}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}break;case 2:try{for(var f=I(this.rectangularCodewordCapacities),h=f.next();!h.done;h=f.next()){var c=h.value;if(c>=t)return c}}catch(t){n={error:t}}finally{try{h&&!h.done&&(o=f.return)&&o.call(f)}finally{if(n)throw n.error}}}try{for(var l=I(this.allCodewordCapacities),d=l.next();!d.done;d=l.next()){var c=d.value;if(c>=t)return c}}catch(t){i={error:t}}finally{try{d&&!d.done&&(a=l.return)&&a.call(l)}finally{if(i)throw i.error}}return this.allCodewordCapacities[this.allCodewordCapacities.length-1]},t.prototype.getCodewordsRemaining=function(t){return this.getMinSymbolSize(t)-t},t.getBytes=function(t,e){var r=new Uint8Array(e?2:1);return r[0]=t,e&&(r[1]=e),r},t.prototype.setC40Word=function(t,e,r,n,o){var i=1600*(255&r)+40*(255&n)+(255&o)+1;t[e]=i/256,t[e+1]=i%256},t.prototype.getX12Value=function(t){return 13===t?0:42===t?1:62===t?2:32===t?3:t>=48&&t<=57?t-44:t>=65&&t<=90?t-51:t},t.prototype.getX12Words=function(){if(this.characterLength%3!=0)throw Error("X12 words must be a multiple of 3");for(var t=new Uint8Array(this.characterLength/3*2),e=0;e<t.length;e+=2)this.setC40Word(t,e,this.getX12Value(this.input.charAt(this.fromPosition+e/2*3)),this.getX12Value(this.input.charAt(this.fromPosition+e/2*3+1)),this.getX12Value(this.input.charAt(this.fromPosition+e/2*3+2)));return t},t.prototype.getShiftValue=function(t,e,r){return e&&R.isInC40Shift1Set(t)||!e&&R.isInTextShift1Set(t)?0:e&&R.isInC40Shift2Set(t,r)||!e&&R.isInTextShift2Set(t,r)?1:2},t.prototype.getC40Value=function(t,e,r,n){if(r===n){if(2!==e)throw Error("FNC1 cannot be used in C40 shift 2");return 27}return t?r<=31?r:32===r?3:r<=47?r-33:r<=57?r-44:r<=64?r-43:r<=90?r-51:r<=95?r-69:r<=127?r-96:r:0===r?0:0===e&&r<=3?r-1:1===e&&r<=31?r:32===r?3:r>=33&&r<=47?r-33:r>=48&&r<=57?r-44:r>=58&&r<=64?r-43:r>=65&&r<=90?r-64:r>=91&&r<=95?r-69:96===r?0:r>=97&&r<=122?r-83:r>=123&&r<=127?r-96:r},t.prototype.getC40Words=function(t,e){for(var r=[],n=0;n<this.characterLength;n++){var o=this.input.charAt(this.fromPosition+n);if(t&&h.A.isNativeC40(o)||!t&&h.A.isNativeText(o))r.push(this.getC40Value(t,0,o,e));else if(R.isExtendedASCII(o,e)){var i=(255&o)-128;if(t&&h.A.isNativeC40(i)||!t&&h.A.isNativeText(i))r.push(1),r.push(30),r.push(this.getC40Value(t,0,i,e));else{r.push(1),r.push(30);var a=this.getShiftValue(i,t,e);r.push(a),r.push(this.getC40Value(t,a,i,e))}}else{var a=this.getShiftValue(o,t,e);r.push(a),r.push(this.getC40Value(t,a,o,e))}}if(r.length%3!=0){if((r.length-2)%3!=0||this.fromPosition+this.characterLength!==this.input.length())throw Error("C40 words must be a multiple of 3");r.push(0)}for(var s=new Uint8Array(r.length/3*2),u=0,n=0;n<r.length;n+=3)this.setC40Word(s,u,255&r[n],255&r[n+1],255&r[n+2]),u+=2;return s},t.prototype.getEDFBytes=function(){for(var t=Math.ceil(this.characterLength/4),e=new Uint8Array(3*t),r=this.fromPosition,n=Math.min(this.fromPosition+this.characterLength-1,this.input.length()-1),o=0;o<t;o+=3){for(var i=[],a=0;a<4;a++)r<=n?i[a]=63&this.input.charAt(r++):i[a]=31*(r===n+1);var s=i[0]<<18;s|=i[1]<<12,s|=i[2]<<6,s|=i[3],e[o]=s>>16&255,e[o+1]=s>>8&255,e[o+2]=255&s}return e},t.prototype.getLatchBytes=function(){switch(this.getPreviousMode()){case n.ASCII:case n.B256:switch(this.mode){case n.B256:return t.getBytes(231);case n.C40:return t.getBytes(230);case n.TEXT:return t.getBytes(239);case n.X12:return t.getBytes(238);case n.EDF:return t.getBytes(240)}break;case n.C40:case n.TEXT:case n.X12:if(this.mode!==this.getPreviousMode())switch(this.mode){case n.ASCII:return t.getBytes(254);case n.B256:return t.getBytes(254,231);case n.C40:return t.getBytes(254,230);case n.TEXT:return t.getBytes(254,239);case n.X12:return t.getBytes(254,238);case n.EDF:return t.getBytes(254,240)}break;case n.EDF:if(this.mode!==n.EDF)throw Error("Cannot switch from EDF to "+this.mode)}return new Uint8Array(0)},t.prototype.getDataBytes=function(){switch(this.mode){case n.ASCII:if(this.input.isECI(this.fromPosition))return t.getBytes(241,this.input.getECIValue(this.fromPosition)+1);if(R.isExtendedASCII(this.input.charAt(this.fromPosition),this.input.getFNC1Character()))return t.getBytes(235,this.input.charAt(this.fromPosition)-127);if(2===this.characterLength)return t.getBytes(10*this.input.charAt(this.fromPosition)+this.input.charAt(this.fromPosition+1)+130);else if(this.input.isFNC1(this.fromPosition))return t.getBytes(232);else return t.getBytes(this.input.charAt(this.fromPosition)+1);case n.B256:return t.getBytes(this.input.charAt(this.fromPosition));case n.C40:return this.getC40Words(!0,this.input.getFNC1Character());case n.TEXT:return this.getC40Words(!1,this.input.getFNC1Character());case n.X12:return this.getX12Words();case n.EDF:return this.getEDFBytes()}},t}(),D=function(t){function e(e,r,n,o,i){var a=t.call(this,e,r,n)||this;return a.shape=o,a.macroId=i,a}return E(e,t),e.prototype.getMacroId=function(){return this.macroId},e.prototype.getShapeHint=function(){return this.shape},e}(C),M=r(72169);r(48798),r(9568);let P=function(){function t(){}return t.prototype.encode=function(t,e,r,n,i){if(void 0===i&&(i=null),""===t.trim())throw Error("Found empty contents");if(e!==o.A.DATA_MATRIX)throw Error("Can only encode DATA_MATRIX, but got "+e);if(r<0||n<0)throw Error("Requested dimensions can't be negative: "+r+"x"+n);var s,l=0,d=null,p=null;if(null!=i){var A=i.get(a.A.DATA_MATRIX_SHAPE);null!=A&&(l=A);var g=i.get(a.A.MIN_SIZE);null!=g&&(d=g);var y=i.get(a.A.MAX_SIZE);null!=y&&(p=y)}if(null!=i&&i.has(a.A.DATA_MATRIX_COMPACT)&&i.get(a.A.DATA_MATRIX_COMPACT).toString()){var w=i.has(a.A.GS1_FORMAT)&&!!i.get(a.A.GS1_FORMAT).toString(),v=null;i.has(a.A.CHARACTER_SET)&&(v=u.A.forName(i.get(a.A.CHARACTER_SET).toString())),s=R.encodeHighLevel(t,v,w?29:-1,l)}else{var _=null!=i&&i.has(a.A.FORCE_C40)&&!!i.get(a.A.FORCE_C40).toString();s=h.A.encodeHighLevel(t,l,d,p,_)}var C=M.A.lookup(s.length,l,d,p,!0),m=f.A.encodeECC200(s,C),E=new c.A(m,C.getSymbolDataWidth(),C.getSymbolDataHeight());return E.place(),this.encodeLowLevel(E,C,r,n)},t.prototype.encodeLowLevel=function(t,e,r,n){for(var o=e.getSymbolDataWidth(),i=e.getSymbolDataHeight(),a=new s.A(e.getSymbolWidth(),e.getSymbolHeight()),u=0,c=0;c<i;c++){var f=void 0;if(c%e.matrixHeight==0){f=0;for(var h=0;h<e.getSymbolWidth();h++)a.setBoolean(f,u,h%2==0),f++;u++}f=0;for(var h=0;h<o;h++)h%e.matrixWidth==0&&(a.setBoolean(f,u,!0),f++),a.setBoolean(f,u,t.getBit(h,c)),f++,h%e.matrixWidth==e.matrixWidth-1&&(a.setBoolean(f,u,c%2==0),f++);if(u++,c%e.matrixHeight==e.matrixHeight-1){f=0;for(var h=0;h<e.getSymbolWidth();h++)a.setBoolean(f,u,!0),f++;u++}}return this.convertByteMatrixToBitMatrix(a,r,n)},t.prototype.convertByteMatrixToBitMatrix=function(t,e,r){var n,o=t.getWidth(),a=t.getHeight(),s=Math.max(e,o),u=Math.max(r,a),c=Math.min(s/o,u/a),f=(s-o*c)/2,h=(u-a*c)/2;r<a||e<o?(f=0,h=0,n=new i.A(o,a)):n=new i.A(e,r),n.clear();for(var l=0,d=h;l<a;l++,d+=c)for(var p=0,A=f;p<o;p++,A+=c)1===t.get(p,l)&&n.setRegion(A,d,c,c);return n},t}()},36436:(t,e,r)=>{"use strict";r.d(e,{w:()=>a});var n=r(11623),o=r(85469),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new o.A,e)||this}return i(e,t),e}(n.J)},37391:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(79886),o=r(438),i=r(22152),a=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),s=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let u=function(t){function e(){var e=t.call(this)||this;return e.decodeFinderCounters=new Int32Array(4),e.dataCharacterCounters=new Int32Array(8),e.oddRoundingErrors=[,,,,],e.evenRoundingErrors=[,,,,],e.oddCounts=Array(e.dataCharacterCounters.length/2),e.evenCounts=Array(e.dataCharacterCounters.length/2),e}return a(e,t),e.prototype.getDecodeFinderCounters=function(){return this.decodeFinderCounters},e.prototype.getDataCharacterCounters=function(){return this.dataCharacterCounters},e.prototype.getOddRoundingErrors=function(){return this.oddRoundingErrors},e.prototype.getEvenRoundingErrors=function(){return this.evenRoundingErrors},e.prototype.getOddCounts=function(){return this.oddCounts},e.prototype.getEvenCounts=function(){return this.evenCounts},e.prototype.parseFinderValue=function(t,r){for(var n=0;n<r.length;n++)if(i.A.patternMatchVariance(t,r[n],e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return n;throw new o.A},e.count=function(t){return n.A.sum(new Int32Array(t))},e.increment=function(t,e){for(var r=0,n=e[0],o=1;o<t.length;o++)e[o]>n&&(n=e[o],r=o);t[r]++},e.decrement=function(t,e){for(var r=0,n=e[0],o=1;o<t.length;o++)e[o]<n&&(n=e[o],r=o);t[r]--},e.isFinderPattern=function(t){var r,n,o=t[0]+t[1],i=o+t[2]+t[3],a=o/i;if(a>=e.MIN_FINDER_PATTERN_RATIO&&a<=e.MAX_FINDER_PATTERN_RATIO){var u=Number.MAX_SAFE_INTEGER,c=Number.MIN_SAFE_INTEGER;try{for(var f=s(t),h=f.next();!h.done;h=f.next()){var l=h.value;l>c&&(c=l),l<u&&(u=l)}}catch(t){r={error:t}}finally{try{h&&!h.done&&(n=f.return)&&n.call(f)}finally{if(r)throw r.error}}return c<10*u}return!1},e.MAX_AVG_VARIANCE=.2,e.MAX_INDIVIDUAL_VARIANCE=.45,e.MIN_FINDER_PATTERN_RATIO=9.5/12,e.MAX_FINDER_PATTERN_RATIO=12.5/14,e}(i.A)},38972:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(55695),o=r(10782),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let a=function(t){function e(e){var r=t.call(this,e)||this;return r.matrix=null,r}return i(e,t),e.prototype.getBlackMatrix=function(){if(null!==this.matrix)return this.matrix;var r=this.getLuminanceSource(),n=r.getWidth(),i=r.getHeight();if(n>=e.MINIMUM_DIMENSION&&i>=e.MINIMUM_DIMENSION){var a=r.getMatrix(),s=n>>e.BLOCK_SIZE_POWER;(n&e.BLOCK_SIZE_MASK)!=0&&s++;var u=i>>e.BLOCK_SIZE_POWER;(i&e.BLOCK_SIZE_MASK)!=0&&u++;var c=e.calculateBlackPoints(a,s,u,n,i),f=new o.A(n,i);e.calculateThresholdForBlock(a,s,u,n,i,c,f),this.matrix=f}else this.matrix=t.prototype.getBlackMatrix.call(this);return this.matrix},e.prototype.createBinarizer=function(t){return new e(t)},e.calculateThresholdForBlock=function(t,r,n,o,i,a,s){for(var u=i-e.BLOCK_SIZE,c=o-e.BLOCK_SIZE,f=0;f<n;f++){var h=f<<e.BLOCK_SIZE_POWER;h>u&&(h=u);for(var l=e.cap(f,2,n-3),d=0;d<r;d++){var p=d<<e.BLOCK_SIZE_POWER;p>c&&(p=c);for(var A=e.cap(d,2,r-3),g=0,y=-2;y<=2;y++){var w=a[l+y];g+=w[A-2]+w[A-1]+w[A]+w[A+1]+w[A+2]}var v=g/25;e.thresholdBlock(t,p,h,v,o,s)}}},e.cap=function(t,e,r){return t<e?e:t>r?r:t},e.thresholdBlock=function(t,r,n,o,i,a){for(var s=0,u=n*i+r;s<e.BLOCK_SIZE;s++,u+=i)for(var c=0;c<e.BLOCK_SIZE;c++)(255&t[u+c])<=o&&a.set(r+c,n+s)},e.calculateBlackPoints=function(t,r,n,o,i){for(var a=i-e.BLOCK_SIZE,s=o-e.BLOCK_SIZE,u=Array(n),c=0;c<n;c++){u[c]=new Int32Array(r);var f=c<<e.BLOCK_SIZE_POWER;f>a&&(f=a);for(var h=0;h<r;h++){var l=h<<e.BLOCK_SIZE_POWER;l>s&&(l=s);for(var d=0,p=255,A=0,g=0,y=f*o+l;g<e.BLOCK_SIZE;g++,y+=o){for(var w=0;w<e.BLOCK_SIZE;w++){var v=255&t[y+w];d+=v,v<p&&(p=v),v>A&&(A=v)}if(A-p>e.MIN_DYNAMIC_RANGE)for(g++,y+=o;g<e.BLOCK_SIZE;g++,y+=o)for(var w=0;w<e.BLOCK_SIZE;w++)d+=255&t[y+w]}var _=d>>2*e.BLOCK_SIZE_POWER;if(A-p<=e.MIN_DYNAMIC_RANGE&&(_=p/2,c>0&&h>0)){var C=(u[c-1][h]+2*u[c][h-1]+u[c-1][h-1])/4;p<C&&(_=C)}u[c][h]=_}}return u},e.BLOCK_SIZE_POWER=3,e.BLOCK_SIZE=1<<e.BLOCK_SIZE_POWER,e.BLOCK_SIZE_MASK=e.BLOCK_SIZE-1,e.MINIMUM_DIMENSION=5*e.BLOCK_SIZE,e.MIN_DYNAMIC_RANGE=24,e}(n.A)},38988:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="IllegalArgumentException",e}(n.A)},39778:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="IllegalStateException",e}(n.A)},39798:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(t,e,r,n,o,i,a,s,u){this.a11=t,this.a21=e,this.a31=r,this.a12=n,this.a22=o,this.a32=i,this.a13=a,this.a23=s,this.a33=u}return t.quadrilateralToQuadrilateral=function(e,r,n,o,i,a,s,u,c,f,h,l,d,p,A,g){var y=t.quadrilateralToSquare(e,r,n,o,i,a,s,u);return t.squareToQuadrilateral(c,f,h,l,d,p,A,g).times(y)},t.prototype.transformPoints=function(t){for(var e=t.length,r=this.a11,n=this.a12,o=this.a13,i=this.a21,a=this.a22,s=this.a23,u=this.a31,c=this.a32,f=this.a33,h=0;h<e;h+=2){var l=t[h],d=t[h+1],p=o*l+s*d+f;t[h]=(r*l+i*d+u)/p,t[h+1]=(n*l+a*d+c)/p}},t.prototype.transformPointsWithValues=function(t,e){for(var r=this.a11,n=this.a12,o=this.a13,i=this.a21,a=this.a22,s=this.a23,u=this.a31,c=this.a32,f=this.a33,h=t.length,l=0;l<h;l++){var d=t[l],p=e[l],A=o*d+s*p+f;t[l]=(r*d+i*p+u)/A,e[l]=(n*d+a*p+c)/A}},t.squareToQuadrilateral=function(e,r,n,o,i,a,s,u){var c=e-n+i-s,f=r-o+a-u;if(0===c&&0===f)return new t(n-e,i-n,e,o-r,a-o,r,0,0,1);var h=n-i,l=s-i,d=o-a,p=u-a,A=h*p-l*d,g=(c*p-l*f)/A,y=(h*f-c*d)/A;return new t(n-e+g*n,s-e+y*s,e,o-r+g*o,u-r+y*u,r,g,y,1)},t.quadrilateralToSquare=function(e,r,n,o,i,a,s,u){return t.squareToQuadrilateral(e,r,n,o,i,a,s,u).buildAdjoint()},t.prototype.buildAdjoint=function(){return new t(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},t.prototype.times=function(e){return new t(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)},t}()},39894:(t,e,r)=>{"use strict";r.d(e,{S:()=>a});var n=r(1933),o=r(7779),i=r(93782),a=function(){function t(){}return t.prototype.getEncodingMode=function(){return i.fG},t.prototype.encodeMaximal=function(t){for(var e=new n.A,r=0,o=t.pos,a=0;t.hasMoreCharacters();){var s=t.getCurrentChar();t.pos++,r=this.encodeChar(s,e),e.length()%3==0&&(o=t.pos,a=e.length())}if(a!==e.length()){var u=Math.floor(e.length()/3*2),c=Math.floor(t.getCodewordCount()+u+1);t.updateSymbolInfo(c);var f=t.getSymbolInfo().getDataCapacity()-c,h=Math.floor(e.length()%3);(2===h&&2!==f||1===h&&(r>3||1!==f))&&(t.pos=o)}e.length()>0&&t.writeCodeword(i.X7),this.handleEOD(t,e)},t.prototype.encode=function(t){for(var e=new n.A;t.hasMoreCharacters();){var r=t.getCurrentChar();t.pos++;var a=this.encodeChar(r,e),s=2*Math.floor(e.length()/3),u=t.getCodewordCount()+s;t.updateSymbolInfo(u);var c=t.getSymbolInfo().getDataCapacity()-u;if(!t.hasMoreCharacters()){var f=new n.A;for(e.length()%3==2&&2!==c&&(a=this.backtrackOneCharacter(t,e,f,a));e.length()%3==1&&(a>3||1!==c);)a=this.backtrackOneCharacter(t,e,f,a);break}if(e.length()%3==0&&o.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode())!==this.getEncodingMode()){t.signalEncoderChange(i.d2);break}}this.handleEOD(t,e)},t.prototype.backtrackOneCharacter=function(t,e,r,n){var o=e.length(),i=e.toString().substring(0,o-n);e.setLengthToZero(),e.append(i),t.pos--;var a=t.getCurrentChar();return n=this.encodeChar(a,r),t.resetSymbolInfo(),n},t.prototype.writeNextTriplet=function(t,e){t.writeCodewords(this.encodeToCodewords(e.toString()));var r=e.toString().substring(3);e.setLengthToZero(),e.append(r)},t.prototype.handleEOD=function(t,e){var r=Math.floor(e.length()/3*2),n=e.length()%3,o=t.getCodewordCount()+r;t.updateSymbolInfo(o);var a=t.getSymbolInfo().getDataCapacity()-o;if(2===n){for(e.append("\0");e.length()>=3;)this.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(i.eb)}else if(1===a&&1===n){for(;e.length()>=3;)this.writeNextTriplet(t,e);t.hasMoreCharacters()&&t.writeCodeword(i.eb),t.pos--}else if(0===n){for(;e.length()>=3;)this.writeNextTriplet(t,e);(a>0||t.hasMoreCharacters())&&t.writeCodeword(i.eb)}else throw Error("Unexpected case. Please report!");t.signalEncoderChange(i.d2)},t.prototype.encodeChar=function(t,e){var r;return 32===t?(e.append(3),1):t>=48&&t<=57?(e.append(t-48+4),1):t>=65&&t<=90?(e.append(t-65+14),1):t<32?(e.append(0),e.append(t),2):t<=47?(e.append(1),e.append(t-33),2):t<=64?(e.append(1),e.append(t-58+15),2):t<=95?(e.append(1),e.append(t-91+22),2):t<=127?(e.append(2),e.append(t-96),2):(e.append("1\x1e"),2+this.encodeChar(t-128,e))},t.prototype.encodeToCodewords=function(t){var e=1600*t.charCodeAt(0)+40*t.charCodeAt(1)+t.charCodeAt(2)+1,r=new n.A;return r.append(e/256),r.append(e%256),r.toString()},t}()},41094:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var n=r(85770),o=r(59612),i=r(55701),a=r(23510),s=r(71534),u=r(1933),c=r(63623),f=r(26143);let h=function(){function t(){}return t.decode=function(e,r,a,c){var h=new n.A(e),l=new u.A,d=[],p=-1,A=-1;try{var g=null,y=!1,w=void 0;do{if(4>h.available())w=f.A.TERMINATOR;else{var v=h.readBits(4);w=f.A.forBits(v)}switch(w){case f.A.TERMINATOR:break;case f.A.FNC1_FIRST_POSITION:case f.A.FNC1_SECOND_POSITION:y=!0;break;case f.A.STRUCTURED_APPEND:if(16>h.available())throw new s.A;p=h.readBits(8),A=h.readBits(8);break;case f.A.ECI:var _=t.parseECIValue(h);if(g=o.A.getCharacterSetECIByValue(_),null===g)throw new s.A;break;case f.A.HANZI:var C=h.readBits(4),m=h.readBits(w.getCharacterCountBits(r));C===t.GB2312_SUBSET&&t.decodeHanziSegment(h,l,m);break;default:var E=h.readBits(w.getCharacterCountBits(r));switch(w){case f.A.NUMERIC:t.decodeNumericSegment(h,l,E);break;case f.A.ALPHANUMERIC:t.decodeAlphanumericSegment(h,l,E,y);break;case f.A.BYTE:t.decodeByteSegment(h,l,E,g,d,c);break;case f.A.KANJI:t.decodeKanjiSegment(h,l,E);break;default:throw new s.A}}}while(w!==f.A.TERMINATOR)}catch(t){throw new s.A}return new i.A(e,l.toString(),0===d.length?null:d,null===a?null:a.toString(),p,A)},t.decodeHanziSegment=function(t,e,r){if(13*r>t.available())throw new s.A;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=t.readBits(13),u=i/96<<8|i%96;u<959?u+=41377:u+=42657,n[o]=u>>8&255,n[o+1]=255&u,o+=2,r--}try{e.append(c.A.decode(n,a.A.GB2312))}catch(t){throw new s.A(t)}},t.decodeKanjiSegment=function(t,e,r){if(13*r>t.available())throw new s.A;for(var n=new Uint8Array(2*r),o=0;r>0;){var i=t.readBits(13),u=i/192<<8|i%192;u<7936?u+=33088:u+=49472,n[o]=u>>8,n[o+1]=u,o+=2,r--}try{e.append(c.A.decode(n,a.A.SHIFT_JIS))}catch(t){throw new s.A(t)}},t.decodeByteSegment=function(t,e,r,n,o,i){if(8*r>t.available())throw new s.A;for(var u,f=new Uint8Array(r),h=0;h<r;h++)f[h]=t.readBits(8);u=null===n?a.A.guessEncoding(f,i):n.getName();try{e.append(c.A.decode(f,u))}catch(t){throw new s.A(t)}o.push(f)},t.toAlphaNumericChar=function(e){if(e>=t.ALPHANUMERIC_CHARS.length)throw new s.A;return t.ALPHANUMERIC_CHARS[e]},t.decodeAlphanumericSegment=function(e,r,n,o){for(var i=r.length();n>1;){if(11>e.available())throw new s.A;var a=e.readBits(11);r.append(t.toAlphaNumericChar(Math.floor(a/45))),r.append(t.toAlphaNumericChar(a%45)),n-=2}if(1===n){if(6>e.available())throw new s.A;r.append(t.toAlphaNumericChar(e.readBits(6)))}if(o)for(var u=i;u<r.length();u++)"%"===r.charAt(u)&&(u<r.length()-1&&"%"===r.charAt(u+1)?r.deleteCharAt(u+1):r.setCharAt(u,"\x1d"))},t.decodeNumericSegment=function(e,r,n){for(;n>=3;){if(10>e.available())throw new s.A;var o=e.readBits(10);if(o>=1e3)throw new s.A;r.append(t.toAlphaNumericChar(Math.floor(o/100))),r.append(t.toAlphaNumericChar(Math.floor(o/10)%10)),r.append(t.toAlphaNumericChar(o%10)),n-=3}if(2===n){if(7>e.available())throw new s.A;var i=e.readBits(7);if(i>=100)throw new s.A;r.append(t.toAlphaNumericChar(Math.floor(i/10))),r.append(t.toAlphaNumericChar(i%10))}else if(1===n){if(4>e.available())throw new s.A;var a=e.readBits(4);if(a>=10)throw new s.A;r.append(t.toAlphaNumericChar(a))}},t.parseECIValue=function(t){var e=t.readBits(8);if((128&e)==0)return 127&e;if((192&e)==128)return(63&e)<<8|t.readBits(8);if((224&e)==192)return(31&e)<<16|t.readBits(16);throw new s.A},t.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",t.GB2312_SUBSET=1,t}()},41205:(t,e,r)=>{"use strict";r.d(e,{A:()=>N});var n=r(25969),o=r(10782),i=r(79417),a=r(438),s=r(69071),u=r(43358),c=r(322),f=r(66950),h=r(55192),l=r(60109),d=r(71534),p=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},A=function(){function t(t,e,r){this.ecCodewords=t,this.ecBlocks=[e],r&&this.ecBlocks.push(r)}return t.prototype.getECCodewords=function(){return this.ecCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t}(),g=function(){function t(t,e){this.count=t,this.dataCodewords=e}return t.prototype.getCount=function(){return this.count},t.prototype.getDataCodewords=function(){return this.dataCodewords},t}(),y=function(){function t(t,e,r,n,o,i){this.versionNumber=t,this.symbolSizeRows=e,this.symbolSizeColumns=r,this.dataRegionSizeRows=n,this.dataRegionSizeColumns=o,this.ecBlocks=i;var a,s,u=0,c=i.getECCodewords(),f=i.getECBlocks();try{for(var h=p(f),l=h.next();!l.done;l=h.next()){var d=l.value;u+=d.getCount()*(d.getDataCodewords()+c)}}catch(t){a={error:t}}finally{try{l&&!l.done&&(s=h.return)&&s.call(h)}finally{if(a)throw a.error}}this.totalCodewords=u}return t.prototype.getVersionNumber=function(){return this.versionNumber},t.prototype.getSymbolSizeRows=function(){return this.symbolSizeRows},t.prototype.getSymbolSizeColumns=function(){return this.symbolSizeColumns},t.prototype.getDataRegionSizeRows=function(){return this.dataRegionSizeRows},t.prototype.getDataRegionSizeColumns=function(){return this.dataRegionSizeColumns},t.prototype.getTotalCodewords=function(){return this.totalCodewords},t.prototype.getECBlocks=function(){return this.ecBlocks},t.getVersionForDimensions=function(e,r){var n,o;if((1&e)!=0||(1&r)!=0)throw new d.A;try{for(var i=p(t.VERSIONS),a=i.next();!a.done;a=i.next()){var s=a.value;if(s.symbolSizeRows===e&&s.symbolSizeColumns===r)return s}}catch(t){n={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}throw new d.A},t.prototype.toString=function(){return""+this.versionNumber},t.buildVersions=function(){return[new t(1,10,10,8,8,new A(5,new g(1,3))),new t(2,12,12,10,10,new A(7,new g(1,5))),new t(3,14,14,12,12,new A(10,new g(1,8))),new t(4,16,16,14,14,new A(12,new g(1,12))),new t(5,18,18,16,16,new A(14,new g(1,18))),new t(6,20,20,18,18,new A(18,new g(1,22))),new t(7,22,22,20,20,new A(20,new g(1,30))),new t(8,24,24,22,22,new A(24,new g(1,36))),new t(9,26,26,24,24,new A(28,new g(1,44))),new t(10,32,32,14,14,new A(36,new g(1,62))),new t(11,36,36,16,16,new A(42,new g(1,86))),new t(12,40,40,18,18,new A(48,new g(1,114))),new t(13,44,44,20,20,new A(56,new g(1,144))),new t(14,48,48,22,22,new A(68,new g(1,174))),new t(15,52,52,24,24,new A(42,new g(2,102))),new t(16,64,64,14,14,new A(56,new g(2,140))),new t(17,72,72,16,16,new A(36,new g(4,92))),new t(18,80,80,18,18,new A(48,new g(4,114))),new t(19,88,88,20,20,new A(56,new g(4,144))),new t(20,96,96,22,22,new A(68,new g(4,174))),new t(21,104,104,24,24,new A(56,new g(6,136))),new t(22,120,120,18,18,new A(68,new g(6,175))),new t(23,132,132,20,20,new A(62,new g(8,163))),new t(24,144,144,22,22,new A(62,new g(8,156),new g(2,155))),new t(25,8,18,6,16,new A(7,new g(1,5))),new t(26,8,32,6,14,new A(11,new g(1,10))),new t(27,12,26,10,24,new A(14,new g(1,16))),new t(28,12,36,10,16,new A(18,new g(1,22))),new t(29,16,36,14,16,new A(24,new g(1,32))),new t(30,16,48,14,22,new A(28,new g(1,49)))]},t.VERSIONS=t.buildVersions(),t}(),w=r(38988),v=function(){function t(e){var r=e.getHeight();if(r<8||r>144||(1&r)!=0)throw new d.A;this.version=t.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new o.A(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}return t.prototype.getVersion=function(){return this.version},t.readVersion=function(t){var e=t.getHeight(),r=t.getWidth();return y.getVersionForDimensions(e,r)},t.prototype.readCodewords=function(){var t=new Int8Array(this.version.getTotalCodewords()),e=0,r=4,n=0,o=this.mappingBitMatrix.getHeight(),i=this.mappingBitMatrix.getWidth(),a=!1,s=!1,u=!1,c=!1;do if(r!==o||0!==n||a)if(r!==o-2||0!==n||(3&i)==0||s)if(r!==o+4||2!==n||(7&i)!=0||u)if(r!==o-2||0!==n||(7&i)!=4||c){do r<o&&n>=0&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,i)),r-=2,n+=2;while(r>=0&&n<i);r+=1,n+=3;do r>=0&&n<i&&!this.readMappingMatrix.get(n,r)&&(t[e++]=255&this.readUtah(r,n,o,i)),r+=2,n-=2;while(r<o&&n>=0);r+=3,n+=1}else t[e++]=255&this.readCorner4(o,i),r-=2,n+=2,c=!0;else t[e++]=255&this.readCorner3(o,i),r-=2,n+=2,u=!0;else t[e++]=255&this.readCorner2(o,i),r-=2,n+=2,s=!0;else t[e++]=255&this.readCorner1(o,i),r-=2,n+=2,a=!0;while(r<o||n<i);if(e!==this.version.getTotalCodewords())throw new d.A;return t},t.prototype.readModule=function(t,e,r,n){return t<0&&(t+=r,e+=4-(r+4&7)),e<0&&(e+=n,t+=4-(n+4&7)),this.readMappingMatrix.set(e,t),this.mappingBitMatrix.get(e,t)},t.prototype.readUtah=function(t,e,r,n){var o=0;return this.readModule(t-2,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-2,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t-1,e,r,n)&&(o|=1),o<<=1,this.readModule(t,e-2,r,n)&&(o|=1),o<<=1,this.readModule(t,e-1,r,n)&&(o|=1),o<<=1,this.readModule(t,e,r,n)&&(o|=1),o},t.prototype.readCorner1=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,1,t,e)&&(r|=1),r<<=1,this.readModule(t-1,2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.readCorner2=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-4,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner3=function(t,e){var r=0;return this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(0,e-3,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-3,t,e)&&(r|=1),r<<=1,this.readModule(1,e-2,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r},t.prototype.readCorner4=function(t,e){var r=0;return this.readModule(t-3,0,t,e)&&(r|=1),r<<=1,this.readModule(t-2,0,t,e)&&(r|=1),r<<=1,this.readModule(t-1,0,t,e)&&(r|=1),r<<=1,this.readModule(0,e-2,t,e)&&(r|=1),r<<=1,this.readModule(0,e-1,t,e)&&(r|=1),r<<=1,this.readModule(1,e-1,t,e)&&(r|=1),r<<=1,this.readModule(2,e-1,t,e)&&(r|=1),r<<=1,this.readModule(3,e-1,t,e)&&(r|=1),r},t.prototype.extractDataRegion=function(t){var e=this.version.getSymbolSizeRows(),r=this.version.getSymbolSizeColumns();if(t.getHeight()!==e)throw new w.A("Dimension of bitMatrix must match the version size");for(var n=this.version.getDataRegionSizeRows(),i=this.version.getDataRegionSizeColumns(),a=e/n|0,s=r/i|0,u=new o.A(s*i,a*n),c=0;c<a;++c)for(var f=c*n,h=0;h<s;++h)for(var l=h*i,d=0;d<n;++d)for(var p=c*(n+2)+1+d,A=f+d,g=0;g<i;++g){var y=h*(i+2)+1+g;if(t.get(y,p)){var v=l+g;u.set(v,A)}}return u},t}(),_=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},C=function(){function t(t,e){this.numDataCodewords=t,this.codewords=e}return t.getDataBlocks=function(e,r){var n,o,i,a,s=r.getECBlocks(),u=0,c=s.getECBlocks();try{for(var f=_(c),h=f.next();!h.done;h=f.next()){var l=h.value;u+=l.getCount()}}catch(t){n={error:t}}finally{try{h&&!h.done&&(o=f.return)&&o.call(f)}finally{if(n)throw n.error}}var d=Array(u),p=0;try{for(var A=_(c),g=A.next();!g.done;g=A.next())for(var l=g.value,y=0;y<l.getCount();y++){var v=l.getDataCodewords(),C=s.getECCodewords()+v;d[p++]=new t(v,new Uint8Array(C))}}catch(t){i={error:t}}finally{try{g&&!g.done&&(a=A.return)&&a.call(A)}finally{if(i)throw i.error}}for(var m=d[0].codewords.length-s.getECCodewords(),E=m-1,I=0,y=0;y<E;y++)for(var S=0;S<p;S++)d[S].codewords[y]=e[I++];for(var T=24===r.getVersionNumber(),O=T?8:p,S=0;S<O;S++)d[S].codewords[m-1]=e[I++];for(var R=d[0].codewords.length,y=m;y<R;y++)for(var S=0;S<p;S++){var b=T?(S+8)%p:S,N=T&&b>7?y-1:y;d[b].codewords[N]=e[I++]}if(I!==e.length)throw new w.A;return d},t.prototype.getNumDataCodewords=function(){return this.numDataCodewords},t.prototype.getCodewords=function(){return this.codewords},t}(),m=r(71411),E=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},I=function(){function t(){this.rsDecoder=new l.A(h.A.DATA_MATRIX_FIELD_256)}return t.prototype.decode=function(t){var e,r,n=new v(t),o=n.getVersion(),i=n.readCodewords(),a=C.getDataBlocks(i,o),s=0;try{for(var u=E(a),c=u.next();!c.done;c=u.next()){var f=c.value;s+=f.getNumDataCodewords()}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}for(var h=new Uint8Array(s),l=a.length,d=0;d<l;d++){var p=a[d],A=p.getCodewords(),g=p.getNumDataCodewords();this.correctErrors(A,g);for(var y=0;y<g;y++)h[y*l+d]=A[y]}return m.A.decode(h)},t.prototype.correctErrors=function(t,e){var r=new Int32Array(t);try{this.rsDecoder.decode(r,t.length-e)}catch(t){throw new f.A}for(var n=0;n<e;n++)t[n]=r[n]},t}(),S=r(17391),T=r(56451),O=r(20367),R=r(56595),b=function(){function t(t){this.image=t,this.rectangleDetector=new S.A(this.image)}return t.prototype.detect=function(){var e=this.rectangleDetector.detect(),r=this.detectSolid1(e);if((r=this.detectSolid2(r))[3]=this.correctTopRight(r),!r[3])throw new a.A;var n=(r=this.shiftToModuleCenter(r))[0],o=r[1],i=r[2],s=r[3],u=this.transitionsBetween(n,s)+1,c=this.transitionsBetween(i,s)+1;(1&u)==1&&(u+=1),(1&c)==1&&(c+=1),4*u<7*c&&4*c<7*u&&(u=c=Math.max(u,c));var f=t.sampleGrid(this.image,n,o,i,s,u,c);return new T.A(f,[n,o,i,s])},t.shiftPoint=function(t,e,r){var n=(e.getX()-t.getX())/(r+1),o=(e.getY()-t.getY())/(r+1);return new R.A(t.getX()+n,t.getY()+o)},t.moveAway=function(t,e,r){var n=t.getX(),o=t.getY();return n<e?n-=1:n+=1,o<r?o-=1:o+=1,new R.A(n,o)},t.prototype.detectSolid1=function(t){var e=t[0],r=t[1],n=t[3],o=t[2],i=this.transitionsBetween(e,r),a=this.transitionsBetween(r,n),s=this.transitionsBetween(n,o),u=this.transitionsBetween(o,e),c=i,f=[o,e,r,n];return c>a&&(c=a,f[0]=e,f[1]=r,f[2]=n,f[3]=o),c>s&&(c=s,f[0]=r,f[1]=n,f[2]=o,f[3]=e),c>u&&(f[0]=n,f[1]=o,f[2]=e,f[3]=r),f},t.prototype.detectSolid2=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i),s=t.shiftPoint(n,o,(a+1)*4),u=t.shiftPoint(o,n,(a+1)*4);return this.transitionsBetween(s,r)<this.transitionsBetween(u,i)?(e[0]=r,e[1]=n,e[2]=o,e[3]=i):(e[0]=n,e[1]=o,e[2]=i,e[3]=r),e},t.prototype.correctTopRight=function(e){var r=e[0],n=e[1],o=e[2],i=e[3],a=this.transitionsBetween(r,i),s=this.transitionsBetween(n,i),u=t.shiftPoint(r,n,(s+1)*4),c=t.shiftPoint(o,n,(a+1)*4);a=this.transitionsBetween(u,i),s=this.transitionsBetween(c,i);var f=new R.A(i.getX()+(o.getX()-n.getX())/(a+1),i.getY()+(o.getY()-n.getY())/(a+1)),h=new R.A(i.getX()+(r.getX()-n.getX())/(s+1),i.getY()+(r.getY()-n.getY())/(s+1));return this.isValid(f)?this.isValid(h)?this.transitionsBetween(u,f)+this.transitionsBetween(c,f)>this.transitionsBetween(u,h)+this.transitionsBetween(c,h)?f:h:f:this.isValid(h)?h:null},t.prototype.shiftToModuleCenter=function(e){var r,n,o=e[0],i=e[1],a=e[2],s=e[3],u=this.transitionsBetween(o,s)+1,c=this.transitionsBetween(a,s)+1,f=t.shiftPoint(o,i,4*c),h=t.shiftPoint(a,i,4*u);u=this.transitionsBetween(f,s)+1,c=this.transitionsBetween(h,s)+1,(1&u)==1&&(u+=1),(1&c)==1&&(c+=1);var l=(o.getX()+i.getX()+a.getX()+s.getX())/4,d=(o.getY()+i.getY()+a.getY()+s.getY())/4;return o=t.moveAway(o,l,d),i=t.moveAway(i,l,d),a=t.moveAway(a,l,d),s=t.moveAway(s,l,d),f=t.shiftPoint(o,i,4*c),f=t.shiftPoint(f,s,4*u),r=t.shiftPoint(i,o,4*c),r=t.shiftPoint(r,a,4*u),h=t.shiftPoint(a,s,4*c),h=t.shiftPoint(h,i,4*u),n=t.shiftPoint(s,a,4*c),[f,r,h,n=t.shiftPoint(n,o,4*u)]},t.prototype.isValid=function(t){return t.getX()>=0&&t.getX()<this.image.getWidth()&&t.getY()>0&&t.getY()<this.image.getHeight()},t.sampleGrid=function(t,e,r,n,o,i,a){return O.A.getInstance().sampleGrid(t,i,a,.5,.5,i-.5,.5,i-.5,a-.5,.5,a-.5,e.getX(),e.getY(),o.getX(),o.getY(),n.getX(),n.getY(),r.getX(),r.getY())},t.prototype.transitionsBetween=function(t,e){var r=Math.trunc(t.getX()),n=Math.trunc(t.getY()),o=Math.trunc(e.getX()),i=Math.trunc(e.getY()),a=Math.abs(i-n)>Math.abs(o-r);if(a){var s=r;r=n,n=s,s=o,o=i,i=s}for(var u=Math.abs(o-r),c=Math.abs(i-n),f=-u/2,h=n<i?1:-1,l=r<o?1:-1,d=0,p=this.image.get(a?n:r,a?r:n),A=r,g=n;A!==o;A+=l){var y=this.image.get(a?g:A,a?A:g);if(y!==p&&(d++,p=y),(f+=c)>0){if(g===i)break;g+=h,f-=u}}return d},t}();let N=function(){function t(){this.decoder=new I}return t.prototype.decode=function(e,r){if(void 0===r&&(r=null),null!=r&&r.has(i.A.PURE_BARCODE)){var o,a,f=t.extractPureBits(e.getBlackMatrix());o=this.decoder.decode(f),a=t.NO_POINTS}else{var h=new b(e.getBlackMatrix()).detect();o=this.decoder.decode(h.getBits()),a=h.getPoints()}var l=o.getRawBytes(),d=new s.A(o.getText(),l,8*l.length,a,n.A.DATA_MATRIX,c.A.currentTimeMillis()),p=o.getByteSegments();null!=p&&d.putMetadata(u.A.BYTE_SEGMENTS,p);var A=o.getECLevel();return null!=A&&d.putMetadata(u.A.ERROR_CORRECTION_LEVEL,A),d},t.prototype.reset=function(){},t.extractPureBits=function(t){var e=t.getTopLeftOnBit(),r=t.getBottomRightOnBit();if(null==e||null==r)throw new a.A;var n=this.moduleSize(e,t),i=e[1],s=r[1],u=e[0],c=(r[0]-u+1)/n,f=(s-i+1)/n;if(c<=0||f<=0)throw new a.A;var h=n/2;i+=h,u+=h;for(var l=new o.A(c,f),d=0;d<f;d++)for(var p=i+d*n,A=0;A<c;A++)t.get(u+A*n,p)&&l.set(A,d);return l},t.moduleSize=function(t,e){for(var r=e.getWidth(),n=t[0],o=t[1];n<r&&e.get(n,o);)n++;if(n===r)throw new a.A;var i=n-t[0];if(0===i)throw new a.A;return i},t.NO_POINTS=[],t}()},42563:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(68883);let o=function(){function t(t){this.information=t,this.generalDecoder=new n.A(t)}return t.prototype.getInformation=function(){return this.information},t.prototype.getGeneralDecoder=function(){return this.generalDecoder},t}()},43197:(t,e,r)=>{"use strict";r.d(e,{A:()=>d,b:()=>l});var n=r(56595),o=r(71614),i=r(79886),a=r(17391),s=r(55192),u=r(60109),c=r(438),f=r(20367),h=r(63479),l=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.toResultPoint=function(){return new n.A(this.getX(),this.getY())},t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t}();let d=function(){function t(t){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=t}return t.prototype.detect=function(){return this.detectMirror(!1)},t.prototype.detectMirror=function(t){var e=this.getMatrixCenter(),r=this.getBullsEyeCorners(e);if(t){var n=r[0];r[0]=r[2],r[2]=n}this.extractParameters(r);var i=this.sampleGrid(this.image,r[this.shift%4],r[(this.shift+1)%4],r[(this.shift+2)%4],r[(this.shift+3)%4]),a=this.getMatrixCornerPoints(r);return new o.A(i,a,this.compact,this.nbDataBlocks,this.nbLayers)},t.prototype.extractParameters=function(t){if(!this.isValidPoint(t[0])||!this.isValidPoint(t[1])||!this.isValidPoint(t[2])||!this.isValidPoint(t[3]))throw new c.A;var e=2*this.nbCenterLayers,r=new Int32Array([this.sampleLine(t[0],t[1],e),this.sampleLine(t[1],t[2],e),this.sampleLine(t[2],t[3],e),this.sampleLine(t[3],t[0],e)]);this.shift=this.getRotation(r,e);for(var n=0,o=0;o<4;o++){var i=r[(this.shift+o)%4];this.compact?(n<<=7,n+=i>>1&127):(n<<=10,n+=(i>>2&992)+(i>>1&31))}var a=this.getCorrectedParameterData(n,this.compact);this.compact?(this.nbLayers=(a>>6)+1,this.nbDataBlocks=(63&a)+1):(this.nbLayers=(a>>11)+1,this.nbDataBlocks=(2047&a)+1)},t.prototype.getRotation=function(t,e){var r=0;t.forEach(function(t,n,o){r=(r<<3)+((t>>e-2<<1)+(1&t))}),r=((1&r)<<11)+(r>>1);for(var n=0;n<4;n++)if(2>=h.A.bitCount(r^this.EXPECTED_CORNER_BITS[n]))return n;throw new c.A},t.prototype.getCorrectedParameterData=function(t,e){e?(r=7,n=2):(r=10,n=4);for(var r,n,o=r-n,i=new Int32Array(r),a=r-1;a>=0;--a)i[a]=15&t,t>>=4;try{new u.A(s.A.AZTEC_PARAM).decode(i,o)}catch(t){throw new c.A}for(var f=0,a=0;a<n;a++)f=(f<<4)+i[a];return f},t.prototype.getBullsEyeCorners=function(t){var e=t,r=t,o=t,i=t,a=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){var s=this.getFirstDifferent(e,a,1,-1),u=this.getFirstDifferent(r,a,1,1),f=this.getFirstDifferent(o,a,-1,1),h=this.getFirstDifferent(i,a,-1,-1);if(this.nbCenterLayers>2){var l=this.distancePoint(h,s)*this.nbCenterLayers/(this.distancePoint(i,e)*(this.nbCenterLayers+2));if(l<.75||l>1.25||!this.isWhiteOrBlackRectangle(s,u,f,h))break}e=s,r=u,o=f,i=h,a=!a}if(5!==this.nbCenterLayers&&7!==this.nbCenterLayers)throw new c.A;this.compact=5===this.nbCenterLayers;var d=new n.A(e.getX()+.5,e.getY()-.5),p=new n.A(r.getX()+.5,r.getY()+.5),A=new n.A(o.getX()-.5,o.getY()+.5),g=new n.A(i.getX()-.5,i.getY()-.5);return this.expandSquare([d,p,A,g],2*this.nbCenterLayers-3,2*this.nbCenterLayers)},t.prototype.getMatrixCenter=function(){try{var t,e,r,n,o=new a.A(this.image).detect();t=o[0],e=o[1],r=o[2],n=o[3]}catch(o){var s=this.image.getWidth()/2,u=this.image.getHeight()/2;t=this.getFirstDifferent(new l(s+7,u-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new l(s+7,u+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new l(s-7,u+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new l(s-7,u-7),!1,-1,-1).toResultPoint()}var c=i.A.round((t.getX()+n.getX()+e.getX()+r.getX())/4),f=i.A.round((t.getY()+n.getY()+e.getY()+r.getY())/4);try{var o=new a.A(this.image,15,c,f).detect();t=o[0],e=o[1],r=o[2],n=o[3]}catch(o){t=this.getFirstDifferent(new l(c+7,f-7),!1,1,-1).toResultPoint(),e=this.getFirstDifferent(new l(c+7,f+7),!1,1,1).toResultPoint(),r=this.getFirstDifferent(new l(c-7,f+7),!1,-1,1).toResultPoint(),n=this.getFirstDifferent(new l(c-7,f-7),!1,-1,-1).toResultPoint()}return new l(c=i.A.round((t.getX()+n.getX()+e.getX()+r.getX())/4),f=i.A.round((t.getY()+n.getY()+e.getY()+r.getY())/4))},t.prototype.getMatrixCornerPoints=function(t){return this.expandSquare(t,2*this.nbCenterLayers,this.getDimension())},t.prototype.sampleGrid=function(t,e,r,n,o){var i=f.A.getInstance(),a=this.getDimension(),s=a/2-this.nbCenterLayers,u=a/2+this.nbCenterLayers;return i.sampleGrid(t,a,a,s,s,u,s,u,u,s,u,e.getX(),e.getY(),r.getX(),r.getY(),n.getX(),n.getY(),o.getX(),o.getY())},t.prototype.sampleLine=function(t,e,r){for(var n=0,o=this.distanceResultPoint(t,e),a=o/r,s=t.getX(),u=t.getY(),c=a*(e.getX()-t.getX())/o,f=a*(e.getY()-t.getY())/o,h=0;h<r;h++)this.image.get(i.A.round(s+h*c),i.A.round(u+h*f))&&(n|=1<<r-h-1);return n},t.prototype.isWhiteOrBlackRectangle=function(t,e,r,n){t=new l(t.getX()-3,t.getY()+3),e=new l(e.getX()-3,e.getY()-3),r=new l(r.getX()+3,r.getY()-3),n=new l(n.getX()+3,n.getY()+3);var o=this.getColor(n,t);if(0===o)return!1;var i=this.getColor(t,e);return i===o&&(i=this.getColor(e,r))===o&&(i=this.getColor(r,n))===o},t.prototype.getColor=function(t,e){for(var r=this.distancePoint(t,e),n=(e.getX()-t.getX())/r,o=(e.getY()-t.getY())/r,a=0,s=t.getX(),u=t.getY(),c=this.image.get(t.getX(),t.getY()),f=Math.ceil(r),h=0;h<f;h++)s+=n,u+=o,this.image.get(i.A.round(s),i.A.round(u))!==c&&a++;var l=a/r;return l>.1&&l<.9?0:l<=.1===c?1:-1},t.prototype.getFirstDifferent=function(t,e,r,n){for(var o=t.getX()+r,i=t.getY()+n;this.isValid(o,i)&&this.image.get(o,i)===e;)o+=r,i+=n;for(o-=r,i-=n;this.isValid(o,i)&&this.image.get(o,i)===e;)o+=r;for(o-=r;this.isValid(o,i)&&this.image.get(o,i)===e;)i+=n;return new l(o,i-=n)},t.prototype.expandSquare=function(t,e,r){var o=r/(2*e),i=t[0].getX()-t[2].getX(),a=t[0].getY()-t[2].getY(),s=(t[0].getX()+t[2].getX())/2,u=(t[0].getY()+t[2].getY())/2,c=new n.A(s+o*i,u+o*a),f=new n.A(s-o*i,u-o*a);return i=t[1].getX()-t[3].getX(),a=t[1].getY()-t[3].getY(),s=(t[1].getX()+t[3].getX())/2,u=(t[1].getY()+t[3].getY())/2,[c,new n.A(s+o*i,u+o*a),f,new n.A(s-o*i,u-o*a)]},t.prototype.isValid=function(t,e){return t>=0&&t<this.image.getWidth()&&e>0&&e<this.image.getHeight()},t.prototype.isValidPoint=function(t){var e=i.A.round(t.getX()),r=i.A.round(t.getY());return this.isValid(e,r)},t.prototype.distancePoint=function(t,e){return i.A.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.distanceResultPoint=function(t,e){return i.A.distance(t.getX(),t.getY(),e.getX(),e.getY())},t.prototype.getDimension=function(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(h.A.truncDivision(this.nbLayers-4,8)+1)+15},t}()},43358:(t,e,r)=>{"use strict";var n;r.d(e,{A:()=>o}),function(t){t[t.OTHER=0]="OTHER",t[t.ORIENTATION=1]="ORIENTATION",t[t.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",t[t.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",t[t.ISSUE_NUMBER=4]="ISSUE_NUMBER",t[t.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",t[t.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",t[t.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",t[t.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",t[t.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",t[t.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"}(n||(n={}));let o=n},45332:(t,e,r)=>{"use strict";r.d(e,{A:()=>l});var n=r(25969),o=r(66950),i=r(79417),a=r(71534),s=r(438),u=r(69071),c=r(56595),f=r(22152),h=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return h(e,t),e.findStartPattern=function(t){for(var r=t.getSize(),n=t.getNextSet(0),o=0,i=Int32Array.from([0,0,0,0,0,0]),a=n,u=!1,c=n;c<r;c++)if(t.get(c)!==u)i[o]++;else{if(5===o){for(var h=e.MAX_AVG_VARIANCE,l=-1,d=e.CODE_START_A;d<=e.CODE_START_C;d++){var p=f.A.patternMatchVariance(i,e.CODE_PATTERNS[d],e.MAX_INDIVIDUAL_VARIANCE);p<h&&(h=p,l=d)}if(l>=0&&t.isRange(Math.max(0,a-(c-a)/2),a,!1))return Int32Array.from([a,c,l]);a+=i[0]+i[1],(i=i.slice(2,i.length))[o-1]=0,i[o]=0,o--}else o++;i[o]=1,u=!u}throw new s.A},e.decodeCode=function(t,r,n){f.A.recordPattern(t,n,r);for(var o=e.MAX_AVG_VARIANCE,i=-1,a=0;a<e.CODE_PATTERNS.length;a++){var u=e.CODE_PATTERNS[a],c=this.patternMatchVariance(r,u,e.MAX_INDIVIDUAL_VARIANCE);c<o&&(o=c,i=a)}if(i>=0)return i;throw new s.A},e.prototype.decodeRow=function(t,r,f){var h,l=f&&!0===f.get(i.A.ASSUME_GS1),d=e.findStartPattern(r),p=d[2],A=0,g=new Uint8Array(20);switch(g[A++]=p,p){case e.CODE_START_A:h=e.CODE_CODE_A;break;case e.CODE_START_B:h=e.CODE_CODE_B;break;case e.CODE_START_C:h=e.CODE_CODE_C;break;default:throw new a.A}for(var y=!1,w=!1,v="",_=d[0],C=d[1],m=Int32Array.from([0,0,0,0,0,0]),E=0,I=0,S=p,T=0,O=!0,R=!1,b=!1;!y;){var N=w;switch(w=!1,E=I,I=e.decodeCode(r,m,C),g[A++]=I,I!==e.CODE_STOP&&(O=!0),I!==e.CODE_STOP&&(S+=++T*I),_=C,C+=m.reduce(function(t,e){return t+e},0),I){case e.CODE_START_A:case e.CODE_START_B:case e.CODE_START_C:throw new a.A}switch(h){case e.CODE_CODE_A:if(I<64)b===R?v+=String.fromCharCode(32+I):v+=String.fromCharCode(32+I+128),b=!1;else if(I<96)b===R?v+=String.fromCharCode(I-64):v+=String.fromCharCode(I+64),b=!1;else switch(I!==e.CODE_STOP&&(O=!1),I){case e.CODE_FNC_1:l&&(0===v.length?v+="]C1":v+="\x1d");break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_A:!R&&b?(R=!0,b=!1):R&&b?(R=!1,b=!1):b=!0;break;case e.CODE_SHIFT:w=!0,h=e.CODE_CODE_B;break;case e.CODE_CODE_B:h=e.CODE_CODE_B;break;case e.CODE_CODE_C:h=e.CODE_CODE_C;break;case e.CODE_STOP:y=!0}break;case e.CODE_CODE_B:if(I<96)b===R?v+=String.fromCharCode(32+I):v+=String.fromCharCode(32+I+128),b=!1;else switch(I!==e.CODE_STOP&&(O=!1),I){case e.CODE_FNC_1:l&&(0===v.length?v+="]C1":v+="\x1d");break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_B:!R&&b?(R=!0,b=!1):R&&b?(R=!1,b=!1):b=!0;break;case e.CODE_SHIFT:w=!0,h=e.CODE_CODE_A;break;case e.CODE_CODE_A:h=e.CODE_CODE_A;break;case e.CODE_CODE_C:h=e.CODE_CODE_C;break;case e.CODE_STOP:y=!0}break;case e.CODE_CODE_C:if(I<100)I<10&&(v+="0"),v+=I;else switch(I!==e.CODE_STOP&&(O=!1),I){case e.CODE_FNC_1:l&&(0===v.length?v+="]C1":v+="\x1d");break;case e.CODE_CODE_A:h=e.CODE_CODE_A;break;case e.CODE_CODE_B:h=e.CODE_CODE_B;break;case e.CODE_STOP:y=!0}}N&&(h=h===e.CODE_CODE_A?e.CODE_CODE_B:e.CODE_CODE_A)}var D=C-_;if(C=r.getNextUnset(C),!r.isRange(C,Math.min(r.getSize(),C+(C-_)/2),!1))throw new s.A;if((S-=T*E)%103!==E)throw new o.A;var M=v.length;if(0===M)throw new s.A;M>0&&O&&(v=h===e.CODE_CODE_C?v.substring(0,M-2):v.substring(0,M-1));for(var P=(d[1]+d[0])/2,B=_+D/2,L=g.length,x=new Uint8Array(L),F=0;F<L;F++)x[F]=g[F];var k=[new c.A(P,t),new c.A(B,t)];return new u.A(v,x,0,k,n.A.CODE_128,new Date().getTime())},e.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],e.MAX_AVG_VARIANCE=.25,e.MAX_INDIVIDUAL_VARIANCE=.7,e.CODE_SHIFT=98,e.CODE_CODE_C=99,e.CODE_CODE_B=100,e.CODE_CODE_A=101,e.CODE_FNC_1=102,e.CODE_FNC_2=97,e.CODE_FNC_3=96,e.CODE_FNC_4_A=101,e.CODE_FNC_4_B=100,e.CODE_START_A=103,e.CODE_START_B=104,e.CODE_START_C=105,e.CODE_STOP=106,e}(f.A)},46263:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(){}return t.floatToIntBits=function(t){return t},t.MAX_VALUE=Number.MAX_SAFE_INTEGER,t}()},47339:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(99496),o=r(25969),i=r(38988);let a=function(){function t(){}return t.prototype.encode=function(t,e,r,a,s){var u;if(e===o.A.QR_CODE)u=new n.A;else throw new i.A("No encoder available for format "+e);return u.encode(t,e,r,a,s)},t}()},48798:(t,e,r)=>{"use strict";r.d(e,{_:()=>a});var n=r(39894),o=r(93782),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.getEncodingMode=function(){return o.VL},e.prototype.encodeChar=function(t,e){var r;return 32===t?(e.append(3),1):t>=48&&t<=57?(e.append(t-48+4),1):t>=97&&t<=122?(e.append(t-97+14),1):t<32?(e.append(0),e.append(t),2):t<=47?(e.append(1),e.append(t-33),2):t<=64?(e.append(1),e.append(t-58+15),2):t>=91&&t<=95?(e.append(1),e.append(t-91+22),2):96===t?(e.append(2),e.append(0),2):t<=90?(e.append(2),e.append(t-65+1),2):t<=127?(e.append(2),e.append(t-123+27),2):(e.append("1\x1e"),2+this.encodeChar(t-128,e))},e}(n.S)},48852:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(438);let o=function(){function t(){}return t.checkAndNudgePoints=function(t,e){for(var r=t.getWidth(),o=t.getHeight(),i=!0,a=0;a<e.length&&i;a+=2){var s=Math.floor(e[a]),u=Math.floor(e[a+1]);if(s<-1||s>r||u<-1||u>o)throw new n.A;i=!1,-1===s?(e[a]=0,i=!0):s===r&&(e[a]=r-1,i=!0),-1===u?(e[a+1]=0,i=!0):u===o&&(e[a+1]=o-1,i=!0)}i=!0;for(var a=e.length-2;a>=0&&i;a-=2){var s=Math.floor(e[a]),u=Math.floor(e[a+1]);if(s<-1||s>r||u<-1||u>o)throw new n.A;i=!1,-1===s?(e[a]=0,i=!0):s===r&&(e[a]=r-1,i=!0),-1===u?(e[a+1]=0,i=!0):u===o&&(e[a+1]=o-1,i=!0)}},t}()},49202:(t,e,r)=>{"use strict";r.d(e,{Q:()=>i});var n=r(1933),o=r(72169),i=function(){function t(t){this.msg=t,this.pos=0,this.skipAtEnd=0;for(var e=t.split("").map(function(t){return t.charCodeAt(0)}),r=new n.A,o=0,i=e.length;o<i;o++){var a=String.fromCharCode(255&e[o]);if("?"===a&&"?"!==t.charAt(o))throw Error("Message contains characters outside ISO-8859-1 encoding.");r.append(a)}this.msg=r.toString(),this.shape=0,this.codewords=new n.A,this.newEncoding=-1}return t.prototype.setSymbolShape=function(t){this.shape=t},t.prototype.setSizeConstraints=function(t,e){this.minSize=t,this.maxSize=e},t.prototype.getMessage=function(){return this.msg},t.prototype.setSkipAtEnd=function(t){this.skipAtEnd=t},t.prototype.getCurrentChar=function(){return this.msg.charCodeAt(this.pos)},t.prototype.getCurrent=function(){return this.msg.charCodeAt(this.pos)},t.prototype.getCodewords=function(){return this.codewords},t.prototype.writeCodewords=function(t){this.codewords.append(t)},t.prototype.writeCodeword=function(t){this.codewords.append(t)},t.prototype.getCodewordCount=function(){return this.codewords.length()},t.prototype.getNewEncoding=function(){return this.newEncoding},t.prototype.signalEncoderChange=function(t){this.newEncoding=t},t.prototype.resetEncoderSignal=function(){this.newEncoding=-1},t.prototype.hasMoreCharacters=function(){return this.pos<this.getTotalMessageCharCount()},t.prototype.getTotalMessageCharCount=function(){return this.msg.length-this.skipAtEnd},t.prototype.getRemainingCharacters=function(){return this.getTotalMessageCharCount()-this.pos},t.prototype.getSymbolInfo=function(){return this.symbolInfo},t.prototype.updateSymbolInfo=function(t){void 0===t&&(t=this.getCodewordCount()),(null==this.symbolInfo||t>this.symbolInfo.getDataCapacity())&&(this.symbolInfo=o.A.lookup(t,this.shape,this.minSize,this.maxSize,!0))},t.prototype.resetSymbolInfo=function(){this.symbolInfo=null},t}()},50942:(t,e,r)=>{"use strict";r.d(e,{A:()=>E});var n=function(){function t(){}return t.singletonList=function(t){return[t]},t.min=function(t,e){return t.sort(e)[0]},t}(),o=r(22868),i=function(){function t(t){this.previous=t}return t.prototype.getPrevious=function(){return this.previous},t}(),a=r(63479),s=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),u=function(t){function e(e,r,n){var o=t.call(this,e)||this;return o.value=r,o.bitCount=n,o}return s(e,t),e.prototype.appendTo=function(t,e){t.appendBits(this.value,this.bitCount)},e.prototype.add=function(t,r){return new e(this,t,r)},e.prototype.addBinaryShift=function(t,r){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new e(this,t,r)},e.prototype.toString=function(){var t=this.value&(1<<this.bitCount)-1;return t|=1<<this.bitCount,"<"+a.A.toBinaryString(t|1<<this.bitCount).substring(1)+">"},e}(i),c=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),f=function(t){function e(e,r,n){var o=t.call(this,e,0,0)||this;return o.binaryShiftStart=r,o.binaryShiftByteCount=n,o}return c(e,t),e.prototype.appendTo=function(t,e){for(var r=0;r<this.binaryShiftByteCount;r++)(0===r||31===r&&this.binaryShiftByteCount<=62)&&(t.appendBits(31,5),this.binaryShiftByteCount>62?t.appendBits(this.binaryShiftByteCount-31,16):0===r?t.appendBits(Math.min(this.binaryShiftByteCount,31),5):t.appendBits(this.binaryShiftByteCount-31,5)),t.appendBits(e[this.binaryShiftStart+r],8)},e.prototype.addBinaryShift=function(t,r){return new e(this,t,r)},e.prototype.toString=function(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"},e}(u);function h(t,e,r){return new u(t,e,r)}var l=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],d=new u(null,0,0),p=[Int32Array.from([0,327708,327710,327709,656318]),Int32Array.from([590318,0,327710,327709,656318]),Int32Array.from([262158,590300,0,590301,932798]),Int32Array.from([327709,327708,656318,0,327710]),Int32Array.from([327711,656380,656382,656381,0])],A=r(6727),g=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},y=function(t){var e,r;try{for(var n=g(t),o=n.next();!o.done;o=n.next()){var i=o.value;A.A.fill(i,-1)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return t[0][4]=0,t[1][4]=0,t[1][0]=28,t[3][4]=0,t[2][4]=0,t[2][0]=15,t}(A.A.createInt32Array(6,6)),w=r(23510),v=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},_=function(){function t(t,e,r,n){this.token=t,this.mode=e,this.binaryShiftByteCount=r,this.bitCount=n}return t.prototype.getMode=function(){return this.mode},t.prototype.getToken=function(){return this.token},t.prototype.getBinaryShiftByteCount=function(){return this.binaryShiftByteCount},t.prototype.getBitCount=function(){return this.bitCount},t.prototype.latchAndAppend=function(e,r){var n=this.bitCount,o=this.token;if(e!==this.mode){var i=p[this.mode][e];o=h(o,65535&i,i>>16),n+=i>>16}var a=2===e?4:5;return new t(o=h(o,r,a),e,0,n+a)},t.prototype.shiftAndAppend=function(e,r){var n=this.token,o=2===this.mode?4:5;return n=h(n,y[this.mode][e],o),new t(n=h(n,r,5),this.mode,0,this.bitCount+o+5)},t.prototype.addBinaryShiftChar=function(e){var r=this.token,n=this.mode,o=this.bitCount;if(4===this.mode||2===this.mode){var i=p[n][0];r=h(r,65535&i,i>>16),o+=i>>16,n=0}var a=0===this.binaryShiftByteCount||31===this.binaryShiftByteCount?18:62===this.binaryShiftByteCount?9:8,s=new t(r,n,this.binaryShiftByteCount+1,o+a);return 2078===s.binaryShiftByteCount&&(s=s.endBinaryShift(e+1)),s},t.prototype.endBinaryShift=function(e){if(0===this.binaryShiftByteCount)return this;var r=this.token;return new t(r=new f(r,e-this.binaryShiftByteCount,this.binaryShiftByteCount),this.mode,0,this.bitCount)},t.prototype.isBetterThanOrEqualTo=function(e){var r=this.bitCount+(p[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?r+=t.calculateBinaryShiftCost(e)-t.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(r+=10),r<=e.bitCount},t.prototype.toBitArray=function(t){for(var e,r,n=[],i=this.endBinaryShift(t.length).token;null!==i;i=i.getPrevious())n.unshift(i);var a=new o.A;try{for(var s=v(n),u=s.next();!u.done;u=s.next())u.value.appendTo(a,t)}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return a},t.prototype.toString=function(){return w.A.format("%s bits=%d bytes=%d",l[this.mode],this.bitCount,this.binaryShiftByteCount)},t.calculateBinaryShiftCost=function(t){return t.binaryShiftByteCount>62?21:t.binaryShiftByteCount>31?20:10*(t.binaryShiftByteCount>0)},t.INITIAL_STATE=new t(d,0,0,0),t}(),C=function(t){var e=w.A.getCharCode(" "),r=w.A.getCharCode("."),n=w.A.getCharCode(",");t[0][e]=1;for(var o=w.A.getCharCode("Z"),i=w.A.getCharCode("A"),a=i;a<=o;a++)t[0][a]=a-i+2;t[1][e]=1;for(var s=w.A.getCharCode("z"),u=w.A.getCharCode("a"),a=u;a<=s;a++)t[1][a]=a-u+2;t[2][e]=1;for(var c=w.A.getCharCode("9"),f=w.A.getCharCode("0"),a=f;a<=c;a++)t[2][a]=a-f+2;t[2][n]=12,t[2][r]=13;for(var h=["\0"," ","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","	","\n","\v","\f","\r","\x1b","\x1c","\x1d","\x1e","\x1f","@","\\","^","_","`","|","~",""],l=0;l<h.length;l++)t[3][w.A.getCharCode(h[l])]=l;for(var d=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"],l=0;l<d.length;l++)w.A.getCharCode(d[l])>0&&(t[4][w.A.getCharCode(d[l])]=l);return t}(A.A.createInt32Array(5,256)),m=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let E=function(){function t(t){this.text=t}return t.prototype.encode=function(){for(var e=w.A.getCharCode(" "),r=w.A.getCharCode("\n"),o=n.singletonList(_.INITIAL_STATE),i=0;i<this.text.length;i++){var a=void 0,s=i+1<this.text.length?this.text[i+1]:0;switch(this.text[i]){case w.A.getCharCode("\r"):a=2*(s===r);break;case w.A.getCharCode("."):a=3*(s===e);break;case w.A.getCharCode(","):a=4*(s===e);break;case w.A.getCharCode(":"):a=5*(s===e);break;default:a=0}a>0?(o=t.updateStateListForPair(o,i,a),i++):o=this.updateStateListForChar(o,i)}return n.min(o,function(t,e){return t.getBitCount()-e.getBitCount()}).toBitArray(this.text)},t.prototype.updateStateListForChar=function(e,r){var n,o,i=[];try{for(var a=m(e),s=a.next();!s.done;s=a.next()){var u=s.value;this.updateStateForChar(u,r,i)}}catch(t){n={error:t}}finally{try{s&&!s.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}}return t.simplifyStates(i)},t.prototype.updateStateForChar=function(t,e,r){for(var n=255&this.text[e],o=C[t.getMode()][n]>0,i=null,a=0;a<=4;a++){var s=C[a][n];if(s>0){if(null==i&&(i=t.endBinaryShift(e)),!o||a===t.getMode()||2===a){var u=i.latchAndAppend(a,s);r.push(u)}if(!o&&y[t.getMode()][a]>=0){var c=i.shiftAndAppend(a,s);r.push(c)}}}if(t.getBinaryShiftByteCount()>0||0===C[t.getMode()][n]){var f=t.addBinaryShiftChar(e);r.push(f)}},t.updateStateListForPair=function(t,e,r){var n,o,i=[];try{for(var a=m(t),s=a.next();!s.done;s=a.next()){var u=s.value;this.updateStateForPair(u,e,r,i)}}catch(t){n={error:t}}finally{try{s&&!s.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}}return this.simplifyStates(i)},t.updateStateForPair=function(t,e,r,n){var o=t.endBinaryShift(e);if(n.push(o.latchAndAppend(4,r)),4!==t.getMode()&&n.push(o.shiftAndAppend(4,r)),3===r||4===r){var i=o.latchAndAppend(2,16-r).latchAndAppend(2,1);n.push(i)}if(t.getBinaryShiftByteCount()>0){var a=t.addBinaryShiftChar(e).addBinaryShiftChar(e+1);n.push(a)}},t.simplifyStates=function(t){var e,r,n,o,i=[];try{for(var a=m(t),s=a.next();!s.done;s=a.next()){var u=s.value,c=!0,f=function(t){if(t.isBetterThanOrEqualTo(u))return c=!1,"break";u.isBetterThanOrEqualTo(t)&&(i=i.filter(function(e){return e!==t}))};try{for(var h=(n=void 0,m(i)),l=h.next();!l.done;l=h.next()){var d=l.value,p=f(d);if("break"===p)break}}catch(t){n={error:t}}finally{try{l&&!l.done&&(o=h.return)&&o.call(h)}finally{if(n)throw n.error}}c&&i.push(u)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return i},t}()},52067:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="IndexOutOfBoundsException",e}(n.A)},52428:(t,e,r)=>{"use strict";r.d(e,{W:()=>a});var n=r(11623),o=r(81339),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(e){return void 0===e&&(e=500),t.call(this,new o.A,e)||this}return i(e,t),e}(n.J)},52771:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(6727),o=r(1933),i=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let a=function(){function t(t,e){this.width=t,this.height=e;for(var r=Array(e),n=0;n!==e;n++)r[n]=new Uint8Array(t);this.bytes=r}return t.prototype.getHeight=function(){return this.height},t.prototype.getWidth=function(){return this.width},t.prototype.get=function(t,e){return this.bytes[e][t]},t.prototype.getArray=function(){return this.bytes},t.prototype.setNumber=function(t,e,r){this.bytes[e][t]=r},t.prototype.setBoolean=function(t,e,r){this.bytes[e][t]=+!!r},t.prototype.clear=function(t){var e,r;try{for(var o=i(this.bytes),a=o.next();!a.done;a=o.next()){var s=a.value;n.A.fill(s,t)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},t.prototype.equals=function(e){if(!(e instanceof t)||this.width!==e.width||this.height!==e.height)return!1;for(var r=0,n=this.height;r<n;++r)for(var o=this.bytes[r],i=e.bytes[r],a=0,s=this.width;a<s;++a)if(o[a]!==i[a])return!1;return!0},t.prototype.toString=function(){for(var t=new o.A,e=0,r=this.height;e<r;++e){for(var n=this.bytes[e],i=0,a=this.width;i<a;++i)switch(n[i]){case 0:t.append(" 0");break;case 1:t.append(" 1");break;default:t.append("  ")}t.append("\n")}return t.toString()},t}()},55192:(t,e,r)=>{"use strict";r.d(e,{A:()=>c});var n=r(34382),o=r(33638),i=r(63479),a=r(38988),s=r(73179),u=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let c=function(t){function e(e,r,o){var i=t.call(this)||this;i.primitive=e,i.size=r,i.generatorBase=o;for(var a=new Int32Array(r),s=1,u=0;u<r;u++)a[u]=s,(s*=2)>=r&&(s^=e,s&=r-1);i.expTable=a;for(var c=new Int32Array(r),u=0;u<r-1;u++)c[a[u]]=u;return i.logTable=c,i.zero=new n.A(i,Int32Array.from([0])),i.one=new n.A(i,Int32Array.from([1])),i}return u(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new a.A;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new n.A(this,r)},e.prototype.inverse=function(t){if(0===t)throw new s.A;return this.expTable[this.size-this.logTable[t]-1]},e.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.size-1)]},e.prototype.getSize=function(){return this.size},e.prototype.getGeneratorBase=function(){return this.generatorBase},e.prototype.toString=function(){return"GF(0x"+i.A.toHexString(this.primitive)+","+this.size+")"},e.prototype.equals=function(t){return t===this},e.AZTEC_DATA_12=new e(4201,4096,1),e.AZTEC_DATA_10=new e(1033,1024,1),e.AZTEC_DATA_6=new e(67,64,1),e.AZTEC_PARAM=new e(19,16,1),e.QR_CODE_FIELD_256=new e(285,256,0),e.DATA_MATRIX_FIELD_256=new e(301,256,1),e.AZTEC_DATA_8=e.DATA_MATRIX_FIELD_256,e.MAXICODE_FIELD_64=e.AZTEC_DATA_6,e}(o.A)},55277:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(1933),o=r(93782);let i=function(){function t(){}return t.encodeECC200=function(t,e){if(t.length!==e.getDataCapacity())throw Error("The number of codewords does not match the selected symbol");var r=new n.A;r.append(t);var o=e.getInterleavedBlockCount();if(1===o){var i=this.createECCBlock(t,e.getErrorCodewords());r.append(i)}else{for(var a=[],s=[],u=0;u<o;u++)a[u]=e.getDataLengthForInterleavedBlock(u+1),s[u]=e.getErrorLengthForInterleavedBlock(u+1);for(var c=0;c<o;c++){for(var f=new n.A,h=c;h<e.getDataCapacity();h+=o)f.append(t.charAt(h));for(var i=this.createECCBlock(f.toString(),s[c]),l=0,d=c;d<s[c]*o;d+=o)r.setCharAt(e.getDataCapacity()+d,i.charAt(l++))}}return r.toString()},t.createECCBlock=function(t,e){for(var r=-1,n=0;n<o.gE.length;n++)if(o.gE[n]===e){r=n;break}if(r<0)throw Error("Illegal number of error correction codewords specified: "+e);for(var i=o.XQ[r],a=[],n=0;n<e;n++)a[n]=0;for(var n=0;n<t.length;n++){for(var s=a[e-1]^t.charAt(n).charCodeAt(0),u=e-1;u>0;u--)0!==s&&0!==i[u]?a[u]=a[u-1]^o.KX[(o.$9[s]+o.$9[i[u]])%255]:a[u]=a[u-1];0!==s&&0!==i[0]?a[0]=o.KX[(o.$9[s]+o.$9[i[0]])%255]:a[0]=0}for(var c=[],n=0;n<e;n++)c[n]=a[e-n-1];return c.map(function(t){return String.fromCharCode(t)}).join("")},t}()},55695:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(19116),o=r(22868),i=r(10782),a=r(438),s=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let u=function(t){function e(r){var n=t.call(this,r)||this;return n.luminances=e.EMPTY,n.buckets=new Int32Array(e.LUMINANCE_BUCKETS),n}return s(e,t),e.prototype.getBlackRow=function(t,r){var n=this.getLuminanceSource(),i=n.getWidth();null==r||r.getSize()<i?r=new o.A(i):r.clear(),this.initArrays(i);for(var a=n.getRow(t,this.luminances),s=this.buckets,u=0;u<i;u++)s[(255&a[u])>>e.LUMINANCE_SHIFT]++;var c=e.estimateBlackPoint(s);if(i<3)for(var u=0;u<i;u++)(255&a[u])<c&&r.set(u);else for(var f=255&a[0],h=255&a[1],u=1;u<i-1;u++){var l=255&a[u+1];(4*h-f-l)/2<c&&r.set(u),f=h,h=l}return r},e.prototype.getBlackMatrix=function(){var t=this.getLuminanceSource(),r=t.getWidth(),n=t.getHeight(),o=new i.A(r,n);this.initArrays(r);for(var a=this.buckets,s=1;s<5;s++)for(var u=Math.floor(n*s/5),c=t.getRow(u,this.luminances),f=Math.floor(4*r/5),h=Math.floor(r/5);h<f;h++){var l=255&c[h];a[l>>e.LUMINANCE_SHIFT]++}for(var d=e.estimateBlackPoint(a),p=t.getMatrix(),s=0;s<n;s++)for(var A=s*r,h=0;h<r;h++){var l=255&p[A+h];l<d&&o.set(h,s)}return o},e.prototype.createBinarizer=function(t){return new e(t)},e.prototype.initArrays=function(t){this.luminances.length<t&&(this.luminances=new Uint8ClampedArray(t));for(var r=this.buckets,n=0;n<e.LUMINANCE_BUCKETS;n++)r[n]=0},e.estimateBlackPoint=function(t){for(var r=t.length,n=0,o=0,i=0,s=0;s<r;s++)t[s]>i&&(o=s,i=t[s]),t[s]>n&&(n=t[s]);for(var u=0,c=0,s=0;s<r;s++){var f=s-o,h=t[s]*f*f;h>c&&(u=s,c=h)}if(o>u){var l=o;o=u,u=l}if(u-o<=r/16)throw new a.A;for(var d=u-1,p=-1,s=u-1;s>o;s--){var A=s-o,h=A*A*(u-s)*(n-t[s]);h>p&&(d=s,p=h)}return d<<e.LUMINANCE_SHIFT},e.LUMINANCE_BITS=5,e.LUMINANCE_SHIFT=8-e.LUMINANCE_BITS,e.LUMINANCE_BUCKETS=1<<e.LUMINANCE_BITS,e.EMPTY=Uint8ClampedArray.from([0]),e}(n.A)},55701:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(t,e,r,n,o,i){void 0===o&&(o=-1),void 0===i&&(i=-1),this.rawBytes=t,this.text=e,this.byteSegments=r,this.ecLevel=n,this.structuredAppendSequenceNumber=o,this.structuredAppendParity=i,this.numBits=null==t?0:8*t.length}return t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.setNumBits=function(t){this.numBits=t},t.prototype.getText=function(){return this.text},t.prototype.getByteSegments=function(){return this.byteSegments},t.prototype.getECLevel=function(){return this.ecLevel},t.prototype.getErrorsCorrected=function(){return this.errorsCorrected},t.prototype.setErrorsCorrected=function(t){this.errorsCorrected=t},t.prototype.getErasures=function(){return this.erasures},t.prototype.setErasures=function(t){this.erasures=t},t.prototype.getOther=function(){return this.other},t.prototype.setOther=function(t){this.other=t},t.prototype.hasStructuredAppend=function(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0},t.prototype.getStructuredAppendParity=function(){return this.structuredAppendParity},t.prototype.getStructuredAppendSequenceNumber=function(){return this.structuredAppendSequenceNumber},t}()},56451:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(t,e){this.bits=t,this.points=e}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}()},56595:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(79886),o=r(46263);let i=function(){function t(t,e){this.x=t,this.y=e}return t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t.prototype.equals=function(e){return e instanceof t&&this.x===e.x&&this.y===e.y},t.prototype.hashCode=function(){return 31*o.A.floatToIntBits(this.x)+o.A.floatToIntBits(this.y)},t.prototype.toString=function(){return"("+this.x+","+this.y+")"},t.orderBestPatterns=function(t){var e,r,n,o=this.distance(t[0],t[1]),i=this.distance(t[1],t[2]),a=this.distance(t[0],t[2]);if(i>=o&&i>=a?(r=t[0],e=t[1],n=t[2]):a>=i&&a>=o?(r=t[1],e=t[0],n=t[2]):(r=t[2],e=t[0],n=t[1]),0>this.crossProductZ(e,r,n)){var s=e;e=n,n=s}t[0]=e,t[1]=r,t[2]=n},t.distance=function(t,e){return n.A.distance(t.x,t.y,e.x,e.y)},t.crossProductZ=function(t,e,r){var n=e.x,o=e.y;return(r.x-n)*(t.y-o)-(r.y-o)*(t.x-n)},t}()},58892:(t,e,r)=>{"use strict";r.d(e,{a:()=>i});var n=r(93782),o=r(7779),i=function(){function t(){}return t.prototype.getEncodingMode=function(){return n.d2},t.prototype.encode=function(t){if(o.A.determineConsecutiveDigitCount(t.getMessage(),t.pos)>=2)t.writeCodeword(this.encodeASCIIDigits(t.getMessage().charCodeAt(t.pos),t.getMessage().charCodeAt(t.pos+1))),t.pos+=2;else{var e=t.getCurrentChar(),r=o.A.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode());if(r!==this.getEncodingMode())switch(r){case n.mt:t.writeCodeword(n.ah),t.signalEncoderChange(n.mt);return;case n.fG:t.writeCodeword(n.X7),t.signalEncoderChange(n.fG);return;case n.VK:t.writeCodeword(n.Qe),t.signalEncoderChange(n.VK);break;case n.VL:t.writeCodeword(n.dn),t.signalEncoderChange(n.VL);break;case n.uf:t.writeCodeword(n.ij),t.signalEncoderChange(n.uf);break;default:throw Error("Illegal mode: "+r)}else o.A.isExtendedASCII(e)?(t.writeCodeword(n.gn),t.writeCodeword(e-128+1)):t.writeCodeword(e+1),t.pos++}},t.prototype.encodeASCIIDigits=function(t,e){if(o.A.isDigit(t)&&o.A.isDigit(e))return(t-48)*10+(e-48)+130;throw Error("not digits: "+t+e)},t}()},59612:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n,o=r(71534),i=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};!function(t){t[t.Cp437=0]="Cp437",t[t.ISO8859_1=1]="ISO8859_1",t[t.ISO8859_2=2]="ISO8859_2",t[t.ISO8859_3=3]="ISO8859_3",t[t.ISO8859_4=4]="ISO8859_4",t[t.ISO8859_5=5]="ISO8859_5",t[t.ISO8859_6=6]="ISO8859_6",t[t.ISO8859_7=7]="ISO8859_7",t[t.ISO8859_8=8]="ISO8859_8",t[t.ISO8859_9=9]="ISO8859_9",t[t.ISO8859_10=10]="ISO8859_10",t[t.ISO8859_11=11]="ISO8859_11",t[t.ISO8859_13=12]="ISO8859_13",t[t.ISO8859_14=13]="ISO8859_14",t[t.ISO8859_15=14]="ISO8859_15",t[t.ISO8859_16=15]="ISO8859_16",t[t.SJIS=16]="SJIS",t[t.Cp1250=17]="Cp1250",t[t.Cp1251=18]="Cp1251",t[t.Cp1252=19]="Cp1252",t[t.Cp1256=20]="Cp1256",t[t.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",t[t.UTF8=22]="UTF8",t[t.ASCII=23]="ASCII",t[t.Big5=24]="Big5",t[t.GB18030=25]="GB18030",t[t.EUC_KR=26]="EUC_KR"}(n||(n={}));let a=function(){function t(e,r,n){for(var o,a,s=[],u=3;u<arguments.length;u++)s[u-3]=arguments[u];this.valueIdentifier=e,this.name=n,"number"==typeof r?this.values=Int32Array.from([r]):this.values=r,this.otherEncodingNames=s,t.VALUE_IDENTIFIER_TO_ECI.set(e,this),t.NAME_TO_ECI.set(n,this);for(var c=this.values,f=0,h=c.length;f!==h;f++){var l=c[f];t.VALUES_TO_ECI.set(l,this)}try{for(var d=i(s),p=d.next();!p.done;p=d.next()){var A=p.value;t.NAME_TO_ECI.set(A,this)}}catch(t){o={error:t}}finally{try{p&&!p.done&&(a=d.return)&&a.call(d)}finally{if(o)throw o.error}}}return t.prototype.getValueIdentifier=function(){return this.valueIdentifier},t.prototype.getName=function(){return this.name},t.prototype.getValue=function(){return this.values[0]},t.getCharacterSetECIByValue=function(e){if(e<0||e>=900)throw new o.A("incorect value");var r=t.VALUES_TO_ECI.get(e);if(void 0===r)throw new o.A("incorect value");return r},t.getCharacterSetECIByName=function(e){var r=t.NAME_TO_ECI.get(e);if(void 0===r)throw new o.A("incorect value");return r},t.prototype.equals=function(e){return e instanceof t&&this.getName()===e.getName()},t.VALUE_IDENTIFIER_TO_ECI=new Map,t.VALUES_TO_ECI=new Map,t.NAME_TO_ECI=new Map,t.Cp437=new t(n.Cp437,Int32Array.from([0,2]),"Cp437"),t.ISO8859_1=new t(n.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),t.ISO8859_2=new t(n.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),t.ISO8859_3=new t(n.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),t.ISO8859_4=new t(n.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),t.ISO8859_5=new t(n.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),t.ISO8859_6=new t(n.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),t.ISO8859_7=new t(n.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),t.ISO8859_8=new t(n.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),t.ISO8859_9=new t(n.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),t.ISO8859_10=new t(n.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),t.ISO8859_11=new t(n.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),t.ISO8859_13=new t(n.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),t.ISO8859_14=new t(n.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),t.ISO8859_15=new t(n.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),t.ISO8859_16=new t(n.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),t.SJIS=new t(n.SJIS,20,"SJIS","Shift_JIS"),t.Cp1250=new t(n.Cp1250,21,"Cp1250","windows-1250"),t.Cp1251=new t(n.Cp1251,22,"Cp1251","windows-1251"),t.Cp1252=new t(n.Cp1252,23,"Cp1252","windows-1252"),t.Cp1256=new t(n.Cp1256,24,"Cp1256","windows-1256"),t.UnicodeBigUnmarked=new t(n.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),t.UTF8=new t(n.UTF8,26,"UTF8","UTF-8"),t.ASCII=new t(n.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),t.Big5=new t(n.Big5,28,"Big5"),t.GB18030=new t(n.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),t.EUC_KR=new t(n.EUC_KR,30,"EUC_KR","EUC-KR"),t}()},60109:(t,e,r)=>{"use strict";r.d(e,{A:()=>s});var n=r(55192),o=r(34382),i=r(20738),a=r(39778);let s=function(){function t(t){this.field=t}return t.prototype.decode=function(t,e){for(var r=this.field,a=new o.A(r,t),s=new Int32Array(e),u=!0,c=0;c<e;c++){var f=a.evaluateAt(r.exp(c+r.getGeneratorBase()));s[s.length-1-c]=f,0!==f&&(u=!1)}if(!u)for(var h=new o.A(r,s),l=this.runEuclideanAlgorithm(r.buildMonomial(e,1),h,e),d=l[0],p=l[1],A=this.findErrorLocations(d),g=this.findErrorMagnitudes(p,A),c=0;c<A.length;c++){var y=t.length-1-r.log(A[c]);if(y<0)throw new i.A("Bad error location");t[y]=n.A.addOrSubtract(t[y],g[c])}},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var n=t;t=e,e=n}for(var o=this.field,s=t,u=e,c=o.getZero(),f=o.getOne();u.getDegree()>=(r/2|0);){var h=s,l=c;if(s=u,c=f,s.isZero())throw new i.A("r_{i-1} was zero");u=h;for(var d=o.getZero(),p=s.getCoefficient(s.getDegree()),A=o.inverse(p);u.getDegree()>=s.getDegree()&&!u.isZero();){var g=u.getDegree()-s.getDegree(),y=o.multiply(u.getCoefficient(u.getDegree()),A);d=d.addOrSubtract(o.buildMonomial(g,y)),u=u.addOrSubtract(s.multiplyByMonomial(g,y))}if(f=d.multiply(c).addOrSubtract(l),u.getDegree()>=s.getDegree())throw new a.A("Division algorithm failed to reduce polynomial?")}var w=f.getCoefficient(0);if(0===w)throw new i.A("sigmaTilde(0) was zero");var v=o.inverse(w);return[f.multiplyScalar(v),u.multiplyScalar(v)]},t.prototype.findErrorLocations=function(t){var e=t.getDegree();if(1===e)return Int32Array.from([t.getCoefficient(1)]);for(var r=new Int32Array(e),n=0,o=this.field,a=1;a<o.getSize()&&n<e;a++)0===t.evaluateAt(a)&&(r[n]=o.inverse(a),n++);if(n!==e)throw new i.A("Error locator degree does not match number of roots");return r},t.prototype.findErrorMagnitudes=function(t,e){for(var r=e.length,n=new Int32Array(r),o=this.field,i=0;i<r;i++){for(var a=o.inverse(e[i]),s=1,u=0;u<r;u++)if(i!==u){var c=o.multiply(e[u],a),f=(1&c)==0?1|c:-2&c;s=o.multiply(s,f)}n[i]=o.multiply(t.evaluateAt(a),o.inverse(s)),0!==o.getGeneratorBase()&&(n[i]=o.multiply(n[i],a))}return n},t}()},61536:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n,o=r(19106),i=r(38988);!function(t){t[t.L=0]="L",t[t.M=1]="M",t[t.Q=2]="Q",t[t.H=3]="H"}(n||(n={}));let a=function(){function t(e,r,n){this.value=e,this.stringValue=r,this.bits=n,t.FOR_BITS.set(n,this),t.FOR_VALUE.set(e,this)}return t.prototype.getValue=function(){return this.value},t.prototype.getBits=function(){return this.bits},t.fromString=function(e){switch(e){case"L":return t.L;case"M":return t.M;case"Q":return t.Q;case"H":return t.H;default:throw new o.A(e+"not available")}},t.prototype.toString=function(){return this.stringValue},t.prototype.equals=function(e){return e instanceof t&&this.value===e.value},t.forBits=function(e){if(e<0||e>=t.FOR_BITS.size)throw new i.A;return t.FOR_BITS.get(e)},t.FOR_BITS=new Map,t.FOR_VALUE=new Map,t.L=new t(n.L,"L",1),t.M=new t(n.M,"M",0),t.Q=new t(n.Q,"Q",3),t.H=new t(n.H,"H",2),t}()},61767:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(){}return t.prototype.isCompact=function(){return this.compact},t.prototype.setCompact=function(t){this.compact=t},t.prototype.getSize=function(){return this.size},t.prototype.setSize=function(t){this.size=t},t.prototype.getLayers=function(){return this.layers},t.prototype.setLayers=function(t){this.layers=t},t.prototype.getCodeWords=function(){return this.codeWords},t.prototype.setCodeWords=function(t){this.codeWords=t},t.prototype.getMatrix=function(){return this.matrix},t.prototype.setMatrix=function(t){this.matrix=t},t}()},63479:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(){}return t.numberOfTrailingZeros=function(t){if(0===t)return 32;var e,r=31;return 0!=(e=t<<16)&&(r-=16,t=e),0!=(e=t<<8)&&(r-=8,t=e),0!=(e=t<<4)&&(r-=4,t=e),0!=(e=t<<2)&&(r-=2,t=e),r-(t<<1>>>31)},t.numberOfLeadingZeros=function(t){if(0===t)return 32;var e=1;return t>>>16==0&&(e+=16,t<<=16),t>>>24==0&&(e+=8,t<<=8),t>>>28==0&&(e+=4,t<<=4),t>>>30==0&&(e+=2,t<<=2),e-=t>>>31},t.toHexString=function(t){return t.toString(16)},t.toBinaryString=function(t){return String(parseInt(String(t),2))},t.bitCount=function(t){return t-=t>>>1&0x55555555,t=(t=(0x33333333&t)+(t>>>2&0x33333333))+(t>>>4)&0xf0f0f0f,t+=t>>>8,63&(t+=t>>>16)},t.truncDivision=function(t,e){return Math.trunc(t/e)},t.parseInt=function(t,e){return void 0===e&&(e=void 0),parseInt(t,e)},t.MIN_VALUE_32_BITS=-0x80000000,t.MAX_VALUE=Number.MAX_SAFE_INTEGER,t}()},63623:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(3451),o=r(59612);let i=function(){function t(){}return t.decode=function(t,e){var r=this.encodingName(e);return this.customDecoder?this.customDecoder(t,r):"undefined"==typeof TextDecoder||this.shouldDecodeOnFallback(r)?this.decodeFallback(t,r):new TextDecoder(r).decode(t)},t.shouldDecodeOnFallback=function(e){return!t.isBrowser()&&"ISO-8859-1"===e},t.encode=function(t,e){var r=this.encodingName(e);return this.customEncoder?this.customEncoder(t,r):"undefined"==typeof TextEncoder?this.encodeFallback(t):new TextEncoder().encode(t)},t.isBrowser=function(){return"undefined"!=typeof window&&"[object Window]"===({}).toString.call(window)},t.encodingName=function(t){return"string"==typeof t?t:t.getName()},t.encodingCharacterSet=function(t){return t instanceof o.A?t:o.A.getCharacterSetECIByName(t)},t.decodeFallback=function(e,r){var i=this.encodingCharacterSet(r);if(t.isDecodeFallbackSupported(i)){for(var a="",s=0,u=e.length;s<u;s++){var c=e[s].toString(16);c.length<2&&(c="0"+c),a+="%"+c}return decodeURIComponent(a)}if(i.equals(o.A.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new n.A("Encoding "+this.encodingName(r)+" not supported by fallback.")},t.isDecodeFallbackSupported=function(t){return t.equals(o.A.UTF8)||t.equals(o.A.ISO8859_1)||t.equals(o.A.ASCII)},t.encodeFallback=function(t){for(var e=btoa(unescape(encodeURIComponent(t))).split(""),r=[],n=0;n<e.length;n++)r.push(e[n].charCodeAt(0));return new Uint8Array(r)},t}()},64191:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var n,o=r(55701),i=r(55192),a=r(60109),s=r(39778),u=r(71534),c=r(23510),f=r(63479);!function(t){t[t.UPPER=0]="UPPER",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.DIGIT=3]="DIGIT",t[t.PUNCT=4]="PUNCT",t[t.BINARY=5]="BINARY"}(n||(n={}));let h=function(){function t(){}return t.prototype.decode=function(e){this.ddata=e;var r=e.getBits(),n=this.extractBits(r),i=this.correctBits(n),a=t.convertBoolArrayToByteArray(i),s=t.getEncodedData(i),u=new o.A(a,s,null,null);return u.setNumBits(i.length),u},t.highLevelDecode=function(t){return this.getEncodedData(t)},t.getEncodedData=function(e){for(var r=e.length,o=n.UPPER,i=n.UPPER,a="",s=0;s<r;)if(i===n.BINARY){if(r-s<5)break;var u=t.readCode(e,s,5);if(s+=5,0===u){if(r-s<11)break;u=t.readCode(e,s,11)+31,s+=11}for(var f=0;f<u;f++){if(r-s<8){s=r;break}var h=t.readCode(e,s,8);a+=c.A.castAsNonUtf8Char(h),s+=8}i=o}else{var l=i===n.DIGIT?4:5;if(r-s<l)break;var h=t.readCode(e,s,l);s+=l;var d=t.getCharacter(i,h);d.startsWith("CTRL_")?(o=i,i=t.getTable(d.charAt(5)),"L"===d.charAt(6)&&(o=i)):(a+=d,i=o)}return a},t.getTable=function(t){switch(t){case"L":return n.LOWER;case"P":return n.PUNCT;case"M":return n.MIXED;case"D":return n.DIGIT;case"B":return n.BINARY;default:return n.UPPER}},t.getCharacter=function(e,r){switch(e){case n.UPPER:return t.UPPER_TABLE[r];case n.LOWER:return t.LOWER_TABLE[r];case n.MIXED:return t.MIXED_TABLE[r];case n.PUNCT:return t.PUNCT_TABLE[r];case n.DIGIT:return t.DIGIT_TABLE[r];default:throw new s.A("Bad table")}},t.prototype.correctBits=function(e){2>=this.ddata.getNbLayers()?(n=6,r=i.A.AZTEC_DATA_6):8>=this.ddata.getNbLayers()?(n=8,r=i.A.AZTEC_DATA_8):22>=this.ddata.getNbLayers()?(n=10,r=i.A.AZTEC_DATA_10):(n=12,r=i.A.AZTEC_DATA_12);var r,n,o=this.ddata.getNbDatablocks(),s=e.length/n;if(s<o)throw new u.A;for(var c=e.length%n,f=new Int32Array(s),h=0;h<s;h++,c+=n)f[h]=t.readCode(e,c,n);try{new a.A(r).decode(f,s-o)}catch(t){throw new u.A(t)}for(var l=(1<<n)-1,d=0,h=0;h<o;h++){var p=f[h];if(0===p||p===l)throw new u.A;(1===p||p===l-1)&&d++}for(var A=Array(o*n-d),g=0,h=0;h<o;h++){var p=f[h];if(1===p||p===l-1)A.fill(p>1,g,g+n-1),g+=n-1;else for(var y=n-1;y>=0;--y)A[g++]=(p&1<<y)!=0}return A},t.prototype.extractBits=function(t){var e=this.ddata.isCompact(),r=this.ddata.getNbLayers(),n=(e?11:14)+4*r,o=new Int32Array(n),i=Array(this.totalBitsInLayer(r,e));if(e)for(var a=0;a<o.length;a++)o[a]=a;else for(var s=n+1+2*f.A.truncDivision(f.A.truncDivision(n,2)-1,15),u=n/2,c=f.A.truncDivision(s,2),a=0;a<u;a++){var h=a+f.A.truncDivision(a,15);o[u-a-1]=c-h-1,o[u+a]=c+h+1}for(var a=0,l=0;a<r;a++){for(var d=(r-a)*4+(e?9:12),p=2*a,A=n-1-p,g=0;g<d;g++)for(var y=2*g,w=0;w<2;w++)i[l+y+w]=t.get(o[p+w],o[p+g]),i[l+2*d+y+w]=t.get(o[p+g],o[A-w]),i[l+4*d+y+w]=t.get(o[A-w],o[A-g]),i[l+6*d+y+w]=t.get(o[A-g],o[p+w]);l+=8*d}return i},t.readCode=function(t,e,r){for(var n=0,o=e;o<e+r;o++)n<<=1,t[o]&&(n|=1);return n},t.readByte=function(e,r){var n=e.length-r;return n>=8?t.readCode(e,r,8):t.readCode(e,r,n)<<8-n},t.convertBoolArrayToByteArray=function(e){for(var r=new Uint8Array((e.length+7)/8),n=0;n<r.length;n++)r[n]=t.readByte(e,8*n);return r},t.prototype.totalBitsInLayer=function(t,e){return((e?88:112)+16*t)*t},t.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],t.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],t.MIXED_TABLE=["CTRL_PS"," ","\x01","\x02","\x03","\x04","\x05","\x06","\x07","\b","	","\n","\v","\f","\r","\x1b","\x1c","\x1d","\x1e","\x1f","@","\\","^","_","`","|","~","","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],t.PUNCT_TABLE=["","\r","\r\n",". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],t.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"],t}()},64476:(t,e,r)=>{"use strict";r.d(e,{A:()=>c});var n=r(25969),o=r(438),i=r(22152),a=r(69071),s=r(56595),u=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let c=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.CODA_BAR_CHAR_SET={nnnnnww:"0",nnnnwwn:"1",nnnwnnw:"2",wwnnnnn:"3",nnwnnwn:"4",wnnnnwn:"5",nwnnnnw:"6",nwnnwnn:"7",nwwnnnn:"8",wnnwnnn:"9",nnnwwnn:"-",nnwwnnn:"$",wnnnwnw:":",wnwnnnw:"/",wnwnwnn:".",nnwwwww:"+",nnwwnwn:"A",nwnwnnw:"B",nnnwnww:"C",nnnwwwn:"D"},e}return u(e,t),e.prototype.decodeRow=function(t,e,r){var i=this.getValidRowData(e);if(!i)throw new o.A;var u=this.codaBarDecodeRow(i.row);if(!u)throw new o.A;return new a.A(u,null,0,[new s.A(i.left,t),new s.A(i.right,t)],n.A.CODABAR,new Date().getTime())},e.prototype.getValidRowData=function(t){var e=t.toArray(),r=e.indexOf(!0);if(-1===r)return null;var n=e.lastIndexOf(!0);if(n<=r)return null;e=e.slice(r,n+1);for(var o=[],i=e[0],a=1,s=1;s<e.length;s++)e[s]===i?a++:(i=e[s],o.push(a),a=1);return(o.push(a),o.length<23&&(o.length+1)%8!=0)?null:{row:o,left:r,right:n}},e.prototype.codaBarDecodeRow=function(t){for(var e=[],r=Math.ceil(t.reduce(function(t,e){return(t+e)/2},0));t.length>0;){var n=t.splice(0,8).splice(0,7).map(function(t){return t<r?"n":"w"}).join("");if(void 0===this.CODA_BAR_CHAR_SET[n])return null;e.push(this.CODA_BAR_CHAR_SET[n])}var o=e.join("");return this.validCodaBarString(o)?o:null},e.prototype.validCodaBarString=function(t){return/^[A-D].{1,}[A-D]$/.test(t)},e}(i.A)},64510:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(10997),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(e){var r=t.call(this,e.getWidth(),e.getHeight())||this;return r.delegate=e,r}return o(e,t),e.prototype.getRow=function(t,e){for(var r=this.delegate.getRow(t,e),n=this.getWidth(),o=0;o<n;o++)r[o]=255-(255&r[o]);return r},e.prototype.getMatrix=function(){for(var t=this.delegate.getMatrix(),e=this.getWidth()*this.getHeight(),r=new Uint8ClampedArray(e),n=0;n<e;n++)r[n]=255-(255&t[n]);return r},e.prototype.isCropSupported=function(){return this.delegate.isCropSupported()},e.prototype.crop=function(t,r,n,o){return new e(this.delegate.crop(t,r,n,o))},e.prototype.isRotateSupported=function(){return this.delegate.isRotateSupported()},e.prototype.invert=function(){return this.delegate},e.prototype.rotateCounterClockwise=function(){return new e(this.delegate.rotateCounterClockwise())},e.prototype.rotateCounterClockwise45=function(){return new e(this.delegate.rotateCounterClockwise45())},e}(n.A)},65649:(t,e,r)=>{"use strict";r.d(e,{A:()=>l});var n=r(25969),o=r(66950),i=r(71534),a=r(438),s=r(22152),u=r(69071),c=r(56595),f=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),h=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let l=function(t){function e(e,r){void 0===e&&(e=!1),void 0===r&&(r=!1);var n=t.call(this)||this;return n.usingCheckDigit=e,n.extendedMode=r,n.decodeRowResult="",n.counters=new Int32Array(9),n}return f(e,t),e.prototype.decodeRow=function(t,r,i){var s,f,l,d,p,A,g,y=this.counters;y.fill(0),this.decodeRowResult="";var w=e.findAsteriskPattern(r,y),v=r.getNextSet(w[1]),_=r.getSize();do{e.recordPattern(r,v,y);var C=e.toNarrowWidePattern(y);if(C<0)throw new a.A;p=e.patternToChar(C),this.decodeRowResult+=p,A=v;try{for(var m=(s=void 0,h(y)),E=m.next();!E.done;E=m.next()){var I=E.value;v+=I}}catch(t){s={error:t}}finally{try{E&&!E.done&&(f=m.return)&&f.call(m)}finally{if(s)throw s.error}}v=r.getNextSet(v)}while("*"!==p);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var S=0;try{for(var T=h(y),O=T.next();!O.done;O=T.next()){var I=O.value;S+=I}}catch(t){l={error:t}}finally{try{O&&!O.done&&(d=T.return)&&d.call(T)}finally{if(l)throw l.error}}var R=v-A-S;if(v!==_&&2*R<S)throw new a.A;if(this.usingCheckDigit){for(var b=this.decodeRowResult.length-1,N=0,D=0;D<b;D++)N+=e.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(D));if(this.decodeRowResult.charAt(b)!==e.ALPHABET_STRING.charAt(N%43))throw new o.A;this.decodeRowResult=this.decodeRowResult.substring(0,b)}if(0===this.decodeRowResult.length)throw new a.A;g=this.extendedMode?e.decodeExtended(this.decodeRowResult):this.decodeRowResult;var M=(w[1]+w[0])/2,P=A+S/2;return new u.A(g,null,0,[new c.A(M,t),new c.A(P,t)],n.A.CODE_39,new Date().getTime())},e.findAsteriskPattern=function(t,r){for(var n=t.getSize(),o=t.getNextSet(0),i=0,s=o,u=!1,c=r.length,f=o;f<n;f++)if(t.get(f)!==u)r[i]++;else{if(i===c-1){if(this.toNarrowWidePattern(r)===e.ASTERISK_ENCODING&&t.isRange(Math.max(0,s-Math.floor((f-s)/2)),s,!1))return[s,f];s+=r[0]+r[1],r.copyWithin(0,2,2+i-1),r[i-1]=0,r[i]=0,i--}else i++;r[i]=1,u=!u}throw new a.A},e.toNarrowWidePattern=function(t){var e,r,n,o=t.length,i=0;do{var a=0x7fffffff;try{for(var s=(e=void 0,h(t)),u=s.next();!u.done;u=s.next()){var c=u.value;c<a&&c>i&&(a=c)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}i=a,n=0;for(var f=0,l=0,d=0;d<o;d++){var c=t[d];c>i&&(l|=1<<o-1-d,n++,f+=c)}if(3===n){for(var d=0;d<o&&n>0;d++){var c=t[d];if(c>i&&(n--,2*c>=f))return -1}return l}}while(n>3);return -1},e.patternToChar=function(t){for(var r=0;r<e.CHARACTER_ENCODINGS.length;r++)if(e.CHARACTER_ENCODINGS[r]===t)return e.ALPHABET_STRING.charAt(r);if(t===e.ASTERISK_ENCODING)return"*";throw new a.A},e.decodeExtended=function(t){for(var e=t.length,r="",n=0;n<e;n++){var o=t.charAt(n);if("+"===o||"$"===o||"%"===o||"/"===o){var a=t.charAt(n+1),s="\0";switch(o){case"+":if(a>="A"&&a<="Z")s=String.fromCharCode(a.charCodeAt(0)+32);else throw new i.A;break;case"$":if(a>="A"&&a<="Z")s=String.fromCharCode(a.charCodeAt(0)-64);else throw new i.A;break;case"%":if(a>="A"&&a<="E")s=String.fromCharCode(a.charCodeAt(0)-38);else if(a>="F"&&a<="J")s=String.fromCharCode(a.charCodeAt(0)-11);else if(a>="K"&&a<="O")s=String.fromCharCode(a.charCodeAt(0)+16);else if(a>="P"&&a<="T")s=String.fromCharCode(a.charCodeAt(0)+43);else if("U"===a)s="\0";else if("V"===a)s="@";else if("W"===a)s="`";else if("X"===a||"Y"===a||"Z"===a)s="";else throw new i.A;break;case"/":if(a>="A"&&a<="O")s=String.fromCharCode(a.charCodeAt(0)-32);else if("Z"===a)s=":";else throw new i.A}r+=s,n++}else r+=o}return r},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",e.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],e.ASTERISK_ENCODING=148,e}(s.A)},66950:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.getChecksumInstance=function(){return new e},e.kind="ChecksumException",e}(n.A)},68139:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=function(t){function e(e,r){var n,o,i,a,s,u=this.constructor,c=t.call(this,e,r)||this;return Object.defineProperty(c,"name",{value:u.name,enumerable:!1,configurable:!0}),a=u.prototype,(s=Object.setPrototypeOf)?s(c,a):c.__proto__=a,n=c,void 0===o&&(o=n.constructor),(i=Error.captureStackTrace)&&i(n,o),c}return n(e,t),e}(Error),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let a=function(t){function e(e){void 0===e&&(e=void 0);var r=t.call(this,e)||this;return r.message=e,r}return i(e,t),e.prototype.getKind=function(){return this.constructor.kind},e.kind="Exception",e}(o)},68883:(t,e,r)=>{"use strict";r.d(e,{A:()=>y});var n=r(71534),o=r(39778),i=r(1933),a=function(){function t(t,e){e?this.decodedInformation=null:(this.finished=t,this.decodedInformation=e)}return t.prototype.getDecodedInformation=function(){return this.decodedInformation},t.prototype.isFinished=function(){return this.finished},t}(),s=function(){function t(t){this.newPosition=t}return t.prototype.getNewPosition=function(){return this.newPosition},t}(),u=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),c=function(t){function e(e,r){var n=t.call(this,e)||this;return n.value=r,n}return u(e,t),e.prototype.getValue=function(){return this.value},e.prototype.isFNC1=function(){return this.value===e.FNC1},e.FNC1="$",e}(s),f=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),h=function(t){function e(e,r,n){var o=t.call(this,e)||this;return n?(o.remaining=!0,o.remainingValue=o.remainingValue):(o.remaining=!1,o.remainingValue=0),o.newString=r,o}return f(e,t),e.prototype.getNewString=function(){return this.newString},e.prototype.isRemaining=function(){return this.remaining},e.prototype.getRemainingValue=function(){return this.remainingValue},e}(s),l=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),d=function(t){function e(e,r,o){var i=t.call(this,e)||this;if(r<0||r>10||o<0||o>10)throw new n.A;return i.firstDigit=r,i.secondDigit=o,i}return l(e,t),e.prototype.getFirstDigit=function(){return this.firstDigit},e.prototype.getSecondDigit=function(){return this.secondDigit},e.prototype.getValue=function(){return 10*this.firstDigit+this.secondDigit},e.prototype.isFirstDigitFNC1=function(){return this.firstDigit===e.FNC1},e.prototype.isSecondDigitFNC1=function(){return this.secondDigit===e.FNC1},e.prototype.isAnyFNC1=function(){return this.firstDigit===e.FNC1||this.secondDigit===e.FNC1},e.FNC1=10,e}(s),p=r(438),A=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g=function(){function t(){}return t.parseFieldsInGeneralPurpose=function(e){if(!e)return null;if(e.length<2)throw new p.A;var r,n,o,i,a,s,u,c,f=e.substring(0,2);try{for(var h=A(t.TWO_DIGIT_DATA_LENGTH),l=h.next();!l.done;l=h.next()){var d=l.value;if(d[0]===f){if(d[1]===t.VARIABLE_LENGTH)return t.processVariableAI(2,d[2],e);return t.processFixedAI(2,d[1],e)}}}catch(t){r={error:t}}finally{try{l&&!l.done&&(n=h.return)&&n.call(h)}finally{if(r)throw r.error}}if(e.length<3)throw new p.A;var g=e.substring(0,3);try{for(var y=A(t.THREE_DIGIT_DATA_LENGTH),w=y.next();!w.done;w=y.next()){var d=w.value;if(d[0]===g){if(d[1]===t.VARIABLE_LENGTH)return t.processVariableAI(3,d[2],e);return t.processFixedAI(3,d[1],e)}}}catch(t){o={error:t}}finally{try{w&&!w.done&&(i=y.return)&&i.call(y)}finally{if(o)throw o.error}}try{for(var v=A(t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH),_=v.next();!_.done;_=v.next()){var d=_.value;if(d[0]===g){if(d[1]===t.VARIABLE_LENGTH)return t.processVariableAI(4,d[2],e);return t.processFixedAI(4,d[1],e)}}}catch(t){a={error:t}}finally{try{_&&!_.done&&(s=v.return)&&s.call(v)}finally{if(a)throw a.error}}if(e.length<4)throw new p.A;var C=e.substring(0,4);try{for(var m=A(t.FOUR_DIGIT_DATA_LENGTH),E=m.next();!E.done;E=m.next()){var d=E.value;if(d[0]===C){if(d[1]===t.VARIABLE_LENGTH)return t.processVariableAI(4,d[2],e);return t.processFixedAI(4,d[1],e)}}}catch(t){u={error:t}}finally{try{E&&!E.done&&(c=m.return)&&c.call(m)}finally{if(u)throw u.error}}throw new p.A},t.processFixedAI=function(e,r,n){if(n.length<e)throw new p.A;var o=n.substring(0,e);if(n.length<e+r)throw new p.A;var i=n.substring(e,e+r),a=n.substring(e+r),s="("+o+")"+i,u=t.parseFieldsInGeneralPurpose(a);return null==u?s:s+u},t.processVariableAI=function(e,r,n){var o,i=n.substring(0,e);o=n.length<e+r?n.length:e+r;var a=n.substring(e,o),s=n.substring(o),u="("+i+")"+a,c=t.parseFieldsInGeneralPurpose(s);return null==c?u:u+c},t.VARIABLE_LENGTH=[],t.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",t.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",t.VARIABLE_LENGTH,20],["22",t.VARIABLE_LENGTH,29],["30",t.VARIABLE_LENGTH,8],["37",t.VARIABLE_LENGTH,8],["90",t.VARIABLE_LENGTH,30],["91",t.VARIABLE_LENGTH,30],["92",t.VARIABLE_LENGTH,30],["93",t.VARIABLE_LENGTH,30],["94",t.VARIABLE_LENGTH,30],["95",t.VARIABLE_LENGTH,30],["96",t.VARIABLE_LENGTH,30],["97",t.VARIABLE_LENGTH,3],["98",t.VARIABLE_LENGTH,30],["99",t.VARIABLE_LENGTH,30]],t.THREE_DIGIT_DATA_LENGTH=[["240",t.VARIABLE_LENGTH,30],["241",t.VARIABLE_LENGTH,30],["242",t.VARIABLE_LENGTH,6],["250",t.VARIABLE_LENGTH,30],["251",t.VARIABLE_LENGTH,30],["253",t.VARIABLE_LENGTH,17],["254",t.VARIABLE_LENGTH,20],["400",t.VARIABLE_LENGTH,30],["401",t.VARIABLE_LENGTH,30],["402",17],["403",t.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",t.VARIABLE_LENGTH,20],["421",t.VARIABLE_LENGTH,15],["422",3],["423",t.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],t.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",t.VARIABLE_LENGTH,15],["391",t.VARIABLE_LENGTH,18],["392",t.VARIABLE_LENGTH,15],["393",t.VARIABLE_LENGTH,18],["703",t.VARIABLE_LENGTH,30]],t.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",t.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",t.VARIABLE_LENGTH,20],["8003",t.VARIABLE_LENGTH,30],["8004",t.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",t.VARIABLE_LENGTH,30],["8008",t.VARIABLE_LENGTH,12],["8018",18],["8020",t.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",t.VARIABLE_LENGTH,70],["8200",t.VARIABLE_LENGTH,70]],t}();let y=function(){function t(t){this.buffer=new i.A,this.information=t}return t.prototype.decodeAllCodes=function(t,e){for(var r=e,n=null;;){var o=this.decodeGeneralPurposeField(r,n),i=g.parseFieldsInGeneralPurpose(o.getNewString());if(null!=i&&t.append(i),n=o.isRemaining()?""+o.getRemainingValue():null,r===o.getNewPosition())break;r=o.getNewPosition()}return t.toString()},t.prototype.isStillNumeric=function(t){if(t+7>this.information.getSize())return t+4<=this.information.getSize();for(var e=t;e<t+3;++e)if(this.information.get(e))return!0;return this.information.get(t+3)},t.prototype.decodeNumeric=function(t){if(t+7>this.information.getSize()){var e=this.extractNumericValueFromBitArray(t,4);return 0===e?new d(this.information.getSize(),d.FNC1,d.FNC1):new d(this.information.getSize(),e-1,d.FNC1)}var r=this.extractNumericValueFromBitArray(t,7);return new d(t+7,(r-8)/11,(r-8)%11)},t.prototype.extractNumericValueFromBitArray=function(e,r){return t.extractNumericValueFromBitArray(this.information,e,r)},t.extractNumericValueFromBitArray=function(t,e,r){for(var n=0,o=0;o<r;++o)t.get(e+o)&&(n|=1<<r-o-1);return n},t.prototype.decodeGeneralPurposeField=function(t,e){this.buffer.setLengthToZero(),null!=e&&this.buffer.append(e),this.current.setPosition(t);var r=this.parseBlocks();return null!=r&&r.isRemaining()?new h(this.current.getPosition(),this.buffer.toString(),r.getRemainingValue()):new h(this.current.getPosition(),this.buffer.toString())},t.prototype.parseBlocks=function(){do{var t,e,r=this.current.getPosition();if(t=this.current.isAlpha()?(e=this.parseAlphaBlock()).isFinished():this.current.isIsoIec646()?(e=this.parseIsoIec646Block()).isFinished():(e=this.parseNumericBlock()).isFinished(),r===this.current.getPosition()&&!t)break}while(!t);return e.getDecodedInformation()},t.prototype.parseNumericBlock=function(){for(;this.isStillNumeric(this.current.getPosition());){var t=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFirstDigitFNC1()){var e=void 0;return new a(!0,e=t.isSecondDigitFNC1()?new h(this.current.getPosition(),this.buffer.toString()):new h(this.current.getPosition(),this.buffer.toString(),t.getSecondDigit()))}if(this.buffer.append(t.getFirstDigit()),t.isSecondDigitFNC1()){var e=new h(this.current.getPosition(),this.buffer.toString());return new a(!0,e)}this.buffer.append(t.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new a(!1)},t.prototype.parseIsoIec646Block=function(){for(;this.isStillIsoIec646(this.current.getPosition());){var t=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1())return new a(!0,new h(this.current.getPosition(),this.buffer.toString()));this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new a(!1)},t.prototype.parseAlphaBlock=function(){for(;this.isStillAlpha(this.current.getPosition());){var t=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(t.getNewPosition()),t.isFNC1())return new a(!0,new h(this.current.getPosition(),this.buffer.toString()));this.buffer.append(t.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new a(!1)},t.prototype.isStillIsoIec646=function(t){if(t+5>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,5);if(e>=5&&e<16)return!0;if(t+7>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,7);if(r>=64&&r<116)return!0;if(t+8>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(t,8);return n>=232&&n<253},t.prototype.decodeIsoIec646=function(t){var e,r=this.extractNumericValueFromBitArray(t,5);if(15===r)return new c(t+5,c.FNC1);if(r>=5&&r<15)return new c(t+5,"0"+(r-5));var o=this.extractNumericValueFromBitArray(t,7);if(o>=64&&o<90)return new c(t+7,""+(o+1));if(o>=90&&o<116)return new c(t+7,""+(o+7));switch(this.extractNumericValueFromBitArray(t,8)){case 232:e="!";break;case 233:e='"';break;case 234:e="%";break;case 235:e="&";break;case 236:e="'";break;case 237:e="(";break;case 238:e=")";break;case 239:e="*";break;case 240:e="+";break;case 241:e=",";break;case 242:e="-";break;case 243:e=".";break;case 244:e="/";break;case 245:e=":";break;case 246:e=";";break;case 247:e="<";break;case 248:e="=";break;case 249:e=">";break;case 250:e="?";break;case 251:e="_";break;case 252:e=" ";break;default:throw new n.A}return new c(t+8,e)},t.prototype.isStillAlpha=function(t){if(t+5>this.information.getSize())return!1;var e=this.extractNumericValueFromBitArray(t,5);if(e>=5&&e<16)return!0;if(t+6>this.information.getSize())return!1;var r=this.extractNumericValueFromBitArray(t,6);return r>=16&&r<63},t.prototype.decodeAlphanumeric=function(t){var e,r=this.extractNumericValueFromBitArray(t,5);if(15===r)return new c(t+5,c.FNC1);if(r>=5&&r<15)return new c(t+5,"0"+(r-5));var n=this.extractNumericValueFromBitArray(t,6);if(n>=32&&n<58)return new c(t+6,""+(n+33));switch(n){case 58:e="*";break;case 59:e=",";break;case 60:e="-";break;case 61:e=".";break;case 62:e="/";break;default:throw new o.A("Decoding invalid alphanumeric value: "+n)}return new c(t+6,e)},t.prototype.isAlphaTo646ToAlphaLatch=function(t){if(t+1>this.information.getSize())return!1;for(var e=0;e<5&&e+t<this.information.getSize();++e)if(2===e){if(!this.information.get(t+2))return!1}else if(this.information.get(t+e))return!1;return!0},t.prototype.isAlphaOr646ToNumericLatch=function(t){if(t+3>this.information.getSize())return!1;for(var e=t;e<t+3;++e)if(this.information.get(e))return!1;return!0},t.prototype.isNumericToAlphaNumericLatch=function(t){if(t+1>this.information.getSize())return!1;for(var e=0;e<4&&e+t<this.information.getSize();++e)if(this.information.get(t+e))return!1;return!0},t}()},69071:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(322);let o=function(){function t(t,e,r,o,i,a){void 0===r&&(r=null==e?0:8*e.length),void 0===a&&(a=n.A.currentTimeMillis()),this.text=t,this.rawBytes=e,this.numBits=r,this.resultPoints=o,this.format=i,this.timestamp=a,this.text=t,this.rawBytes=e,null==r?this.numBits=null==e?0:8*e.length:this.numBits=r,this.resultPoints=o,this.format=i,this.resultMetadata=null,null==a?this.timestamp=n.A.currentTimeMillis():this.timestamp=a}return t.prototype.getText=function(){return this.text},t.prototype.getRawBytes=function(){return this.rawBytes},t.prototype.getNumBits=function(){return this.numBits},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.getBarcodeFormat=function(){return this.format},t.prototype.getResultMetadata=function(){return this.resultMetadata},t.prototype.putMetadata=function(t,e){null===this.resultMetadata&&(this.resultMetadata=new Map),this.resultMetadata.set(t,e)},t.prototype.putAllMetadata=function(t){null!==t&&(null===this.resultMetadata?this.resultMetadata=t:this.resultMetadata=new Map(t))},t.prototype.addResultPoints=function(t){var e=this.resultPoints;if(null===e)this.resultPoints=t;else if(null!==t&&t.length>0){var r=Array(e.length+t.length);n.A.arraycopy(e,0,r,0,e.length),n.A.arraycopy(t,0,r,e.length,t.length),this.resultPoints=r}},t.prototype.getTimestamp=function(){return this.timestamp},t.prototype.toString=function(){return this.text},t}()},71358:()=>{},71411:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var n,o=r(55701),i=r(85770),a=r(1933),s=r(63623),u=r(23510),c=r(71534),f=r(39778);!function(t){t[t.PAD_ENCODE=0]="PAD_ENCODE",t[t.ASCII_ENCODE=1]="ASCII_ENCODE",t[t.C40_ENCODE=2]="C40_ENCODE",t[t.TEXT_ENCODE=3]="TEXT_ENCODE",t[t.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",t[t.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",t[t.BASE256_ENCODE=6]="BASE256_ENCODE"}(n||(n={}));let h=function(){function t(){}return t.decode=function(t){var e=new i.A(t),r=new a.A,s=new a.A,u=[],f=n.ASCII_ENCODE;do if(f===n.ASCII_ENCODE)f=this.decodeAsciiSegment(e,r,s);else{switch(f){case n.C40_ENCODE:this.decodeC40Segment(e,r);break;case n.TEXT_ENCODE:this.decodeTextSegment(e,r);break;case n.ANSIX12_ENCODE:this.decodeAnsiX12Segment(e,r);break;case n.EDIFACT_ENCODE:this.decodeEdifactSegment(e,r);break;case n.BASE256_ENCODE:this.decodeBase256Segment(e,r,u);break;default:throw new c.A}f=n.ASCII_ENCODE}while(f!==n.PAD_ENCODE&&e.available()>0);return s.length()>0&&r.append(s.toString()),new o.A(t,r.toString(),0===u.length?null:u,null)},t.decodeAsciiSegment=function(t,e,r){var o=!1;do{var i=t.readBits(8);if(0===i)throw new c.A;if(i<=128){o&&(i+=128),e.append(String.fromCharCode(i-1));break}if(129===i)return n.PAD_ENCODE;else if(i<=229){var a=i-130;a<10&&e.append("0"),e.append(""+a)}else switch(i){case 230:return n.C40_ENCODE;case 231:return n.BASE256_ENCODE;case 232:e.append("\x1d");break;case 233:case 234:case 241:break;case 235:o=!0;break;case 236:e.append("[)>\x1e05\x1d"),r.insert(0,"\x1e\x04");break;case 237:e.append("[)>\x1e06\x1d"),r.insert(0,"\x1e\x04");break;case 238:return n.ANSIX12_ENCODE;case 239:return n.TEXT_ENCODE;case 240:return n.EDIFACT_ENCODE;default:if(254!==i||0!==t.available())throw new c.A}}while(t.available()>0);return n.ASCII_ENCODE},t.decodeC40Segment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var s=n[a];switch(o){case 0:if(s<3)o=s+1;else if(s<this.C40_BASIC_SET_CHARS.length){var u=this.C40_BASIC_SET_CHARS[s];r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u)}else throw new c.A;break;case 1:r?(e.append(String.fromCharCode(s+128)),r=!1):e.append(String.fromCharCode(s)),o=0;break;case 2:if(s<this.C40_SHIFT2_SET_CHARS.length){var u=this.C40_SHIFT2_SET_CHARS[s];r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u)}else switch(s){case 27:e.append("\x1d");break;case 30:r=!0;break;default:throw new c.A}o=0;break;case 3:r?(e.append(String.fromCharCode(s+224)),r=!1):e.append(String.fromCharCode(s+96)),o=0;break;default:throw new c.A}}}while(t.available()>0)},t.decodeTextSegment=function(t,e){var r=!1,n=[],o=0;do{if(8===t.available())return;var i=t.readBits(8);if(254===i)return;this.parseTwoBytes(i,t.readBits(8),n);for(var a=0;a<3;a++){var s=n[a];switch(o){case 0:if(s<3)o=s+1;else if(s<this.TEXT_BASIC_SET_CHARS.length){var u=this.TEXT_BASIC_SET_CHARS[s];r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u)}else throw new c.A;break;case 1:r?(e.append(String.fromCharCode(s+128)),r=!1):e.append(String.fromCharCode(s)),o=0;break;case 2:if(s<this.TEXT_SHIFT2_SET_CHARS.length){var u=this.TEXT_SHIFT2_SET_CHARS[s];r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u)}else switch(s){case 27:e.append("\x1d");break;case 30:r=!0;break;default:throw new c.A}o=0;break;case 3:if(s<this.TEXT_SHIFT3_SET_CHARS.length){var u=this.TEXT_SHIFT3_SET_CHARS[s];r?(e.append(String.fromCharCode(u.charCodeAt(0)+128)),r=!1):e.append(u),o=0}else throw new c.A;break;default:throw new c.A}}}while(t.available()>0)},t.decodeAnsiX12Segment=function(t,e){var r=[];do{if(8===t.available())return;var n=t.readBits(8);if(254===n)return;this.parseTwoBytes(n,t.readBits(8),r);for(var o=0;o<3;o++){var i=r[o];switch(i){case 0:e.append("\r");break;case 1:e.append("*");break;case 2:e.append(">");break;case 3:e.append(" ");break;default:if(i<14)e.append(String.fromCharCode(i+44));else if(i<40)e.append(String.fromCharCode(i+51));else throw new c.A}}}while(t.available()>0)},t.parseTwoBytes=function(t,e,r){var n=(t<<8)+e-1,o=Math.floor(n/1600);r[0]=o,n-=1600*o,o=Math.floor(n/40),r[1]=o,r[2]=n-40*o},t.decodeEdifactSegment=function(t,e){do{if(16>=t.available())return;for(var r=0;r<4;r++){var n=t.readBits(6);if(31===n){var o=8-t.getBitOffset();8!==o&&t.readBits(o);return}(32&n)==0&&(n|=64),e.append(String.fromCharCode(n))}}while(t.available()>0)},t.decodeBase256Segment=function(t,e,r){var n,o=1+t.getByteOffset(),i=this.unrandomize255State(t.readBits(8),o++);if((n=0===i?t.available()/8|0:i<250?i:250*(i-249)+this.unrandomize255State(t.readBits(8),o++))<0)throw new c.A;for(var a=new Uint8Array(n),h=0;h<n;h++){if(8>t.available())throw new c.A;a[h]=this.unrandomize255State(t.readBits(8),o++)}r.push(a);try{e.append(s.A.decode(a,u.A.ISO88591))}catch(t){throw new f.A("Platform does not support required encoding: "+t.message)}},t.unrandomize255State=function(t,e){var r=t-(149*e%255+1);return r>=0?r:r+256},t.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],t.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],t.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],t.TEXT_SHIFT2_SET_CHARS=t.C40_SHIFT2_SET_CHARS,t.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",""],t}()},71534:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.getFormatInstance=function(){return new e},e.kind="FormatException",e}(n.A)},71614:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(56451),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(e,r,n,o,i){var a=t.call(this,e,r)||this;return a.compact=n,a.nbDatablocks=o,a.nbLayers=i,a}return o(e,t),e.prototype.getNbLayers=function(){return this.nbLayers},e.prototype.getNbDatablocks=function(){return this.nbDatablocks},e.prototype.isCompact=function(){return this.compact},e}(n.A)},72106:(t,e,r)=>{"use strict";r.d(e,{A:()=>f});var n=r(69071),o=r(25969),i=r(79417),a=r(43358),s=r(322),u=r(64191),c=r(43197);let f=function(){function t(){}return t.prototype.decode=function(t,e){void 0===e&&(e=null);var r=null,i=new c.A(t.getBlackMatrix()),f=null,h=null;try{var l=i.detectMirror(!1);f=l.getPoints(),this.reportFoundResultPoints(e,f),h=new u.A().decode(l)}catch(t){r=t}if(null==h)try{var l=i.detectMirror(!0);f=l.getPoints(),this.reportFoundResultPoints(e,f),h=new u.A().decode(l)}catch(t){if(null!=r)throw r;throw t}var d=new n.A(h.getText(),h.getRawBytes(),h.getNumBits(),f,o.A.AZTEC,s.A.currentTimeMillis()),p=h.getByteSegments();null!=p&&d.putMetadata(a.A.BYTE_SEGMENTS,p);var A=h.getECLevel();return null!=A&&d.putMetadata(a.A.ERROR_CORRECTION_LEVEL,A),d},t.prototype.reportFoundResultPoints=function(t,e){if(null!=t){var r=t.get(i.A.NEED_RESULT_POINT_CALLBACK);null!=r&&e.forEach(function(t,e,n){r.foundPossibleResultPoint(t)})}},t.prototype.reset=function(){},t}()},72169:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=function(){function t(t,e,r,n,o,i,a,s){void 0===a&&(a=0),void 0===s&&(s=0),this.rectangular=t,this.dataCapacity=e,this.errorCodewords=r,this.matrixWidth=n,this.matrixHeight=o,this.dataRegions=i,this.rsBlockData=a,this.rsBlockError=s}return t.lookup=function(t,e,r,n,i){var a,s;void 0===e&&(e=0),void 0===r&&(r=null),void 0===n&&(n=null),void 0===i&&(i=!0);try{for(var c=o(u),f=c.next();!f.done;f=c.next()){var h=f.value;if(!(1===e&&h.rectangular||2===e&&!h.rectangular||null!=r&&(h.getSymbolWidth()<r.getWidth()||h.getSymbolHeight()<r.getHeight()))&&!(null!=n&&(h.getSymbolWidth()>n.getWidth()||h.getSymbolHeight()>n.getHeight()))&&t<=h.dataCapacity)return h}}catch(t){a={error:t}}finally{try{f&&!f.done&&(s=c.return)&&s.call(c)}finally{if(a)throw a.error}}if(i)throw Error("Can't find a symbol arrangement that matches the message. Data codewords: "+t);return null},t.prototype.getHorizontalDataRegions=function(){switch(this.dataRegions){case 1:return 1;case 2:case 4:return 2;case 16:return 4;case 36:return 6;default:throw Error("Cannot handle this number of data regions")}},t.prototype.getVerticalDataRegions=function(){switch(this.dataRegions){case 1:case 2:return 1;case 4:return 2;case 16:return 4;case 36:return 6;default:throw Error("Cannot handle this number of data regions")}},t.prototype.getSymbolDataWidth=function(){return this.getHorizontalDataRegions()*this.matrixWidth},t.prototype.getSymbolDataHeight=function(){return this.getVerticalDataRegions()*this.matrixHeight},t.prototype.getSymbolWidth=function(){return this.getSymbolDataWidth()+2*this.getHorizontalDataRegions()},t.prototype.getSymbolHeight=function(){return this.getSymbolDataHeight()+2*this.getVerticalDataRegions()},t.prototype.getCodewordCount=function(){return this.dataCapacity+this.errorCodewords},t.prototype.getInterleavedBlockCount=function(){return this.rsBlockData?this.dataCapacity/this.rsBlockData:1},t.prototype.getDataCapacity=function(){return this.dataCapacity},t.prototype.getErrorCodewords=function(){return this.errorCodewords},t.prototype.getDataLengthForInterleavedBlock=function(t){return this.rsBlockData},t.prototype.getErrorLengthForInterleavedBlock=function(t){return this.rsBlockError},t}();let a=i;var s=function(t){function e(){return t.call(this,!1,1558,620,22,22,36,-1,62)||this}return n(e,t),e.prototype.getInterleavedBlockCount=function(){return 10},e.prototype.getDataLengthForInterleavedBlock=function(t){return t<=8?156:155},e}(i),u=[new i(!1,3,5,8,8,1),new i(!1,5,7,10,10,1),new i(!0,5,7,16,6,1),new i(!1,8,10,12,12,1),new i(!0,10,11,14,6,2),new i(!1,12,12,14,14,1),new i(!0,16,14,24,10,1),new i(!1,18,14,16,16,1),new i(!1,22,18,18,18,1),new i(!0,22,18,16,10,2),new i(!1,30,20,20,20,1),new i(!0,32,24,16,14,2),new i(!1,36,24,22,22,1),new i(!1,44,28,24,24,1),new i(!0,49,28,22,14,2),new i(!1,62,36,14,14,4),new i(!1,86,42,16,16,4),new i(!1,114,48,18,18,4),new i(!1,144,56,20,20,4),new i(!1,174,68,22,22,4),new i(!1,204,84,24,24,4,102,42),new i(!1,280,112,14,14,16,140,56),new i(!1,368,144,16,16,16,92,36),new i(!1,456,192,18,18,16,114,48),new i(!1,576,224,20,20,16,144,56),new i(!1,696,272,22,22,16,174,68),new i(!1,816,336,24,24,16,136,56),new i(!1,1050,408,18,18,36,175,68),new i(!1,1304,496,20,20,36,163,62),new s]},73179:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="ArithmeticException",e}(n.A)},73693:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(6727);let o=function(){function t(t,e,r){this.codewords=t,this.numcols=e,this.numrows=r,this.bits=new Uint8Array(e*r),n.A.fill(this.bits,2)}return t.prototype.getNumrows=function(){return this.numrows},t.prototype.getNumcols=function(){return this.numcols},t.prototype.getBits=function(){return this.bits},t.prototype.getBit=function(t,e){return 1===this.bits[e*this.numcols+t]},t.prototype.setBit=function(t,e,r){this.bits[e*this.numcols+t]=+!!r},t.prototype.noBit=function(t,e){return 2===this.bits[e*this.numcols+t]},t.prototype.place=function(){var t=0,e=4,r=0;do{e===this.numrows&&0===r&&this.corner1(t++),e===this.numrows-2&&0===r&&this.numcols%4!=0&&this.corner2(t++),e===this.numrows-2&&0===r&&this.numcols%8==4&&this.corner3(t++),e===this.numrows+4&&2===r&&this.numcols%8==0&&this.corner4(t++);do e<this.numrows&&r>=0&&this.noBit(r,e)&&this.utah(e,r,t++),e-=2,r+=2;while(e>=0&&r<this.numcols);e++,r+=3;do e>=0&&r<this.numcols&&this.noBit(r,e)&&this.utah(e,r,t++),e+=2,r-=2;while(e<this.numrows&&r>=0);e+=3,r++}while(e<this.numrows||r<this.numcols);this.noBit(this.numcols-1,this.numrows-1)&&(this.setBit(this.numcols-1,this.numrows-1,!0),this.setBit(this.numcols-2,this.numrows-2,!0))},t.prototype.module=function(t,e,r,n){t<0&&(t+=this.numrows,e+=4-(this.numrows+4)%8),e<0&&(e+=this.numcols,t+=4-(this.numcols+4)%8);var o=this.codewords.charCodeAt(r);o&=1<<8-n,this.setBit(e,t,0!==o)},t.prototype.utah=function(t,e,r){this.module(t-2,e-2,r,1),this.module(t-2,e-1,r,2),this.module(t-1,e-2,r,3),this.module(t-1,e-1,r,4),this.module(t-1,e,r,5),this.module(t,e-2,r,6),this.module(t,e-1,r,7),this.module(t,e,r,8)},t.prototype.corner1=function(t){this.module(this.numrows-1,0,t,1),this.module(this.numrows-1,1,t,2),this.module(this.numrows-1,2,t,3),this.module(0,this.numcols-2,t,4),this.module(0,this.numcols-1,t,5),this.module(1,this.numcols-1,t,6),this.module(2,this.numcols-1,t,7),this.module(3,this.numcols-1,t,8)},t.prototype.corner2=function(t){this.module(this.numrows-3,0,t,1),this.module(this.numrows-2,0,t,2),this.module(this.numrows-1,0,t,3),this.module(0,this.numcols-4,t,4),this.module(0,this.numcols-3,t,5),this.module(0,this.numcols-2,t,6),this.module(0,this.numcols-1,t,7),this.module(1,this.numcols-1,t,8)},t.prototype.corner3=function(t){this.module(this.numrows-3,0,t,1),this.module(this.numrows-2,0,t,2),this.module(this.numrows-1,0,t,3),this.module(0,this.numcols-2,t,4),this.module(0,this.numcols-1,t,5),this.module(1,this.numcols-1,t,6),this.module(2,this.numcols-1,t,7),this.module(3,this.numcols-1,t,8)},t.prototype.corner4=function(t){this.module(this.numrows-1,0,t,1),this.module(this.numrows-1,this.numcols-1,t,2),this.module(0,this.numcols-3,t,3),this.module(0,this.numcols-2,t,4),this.module(0,this.numcols-1,t,5),this.module(1,this.numcols-3,t,6),this.module(1,this.numcols-2,t,7),this.module(1,this.numcols-1,t,8)},t}()},73977:(t,e,r)=>{"use strict";r.d(e,{A:()=>l});var n=r(25969),o=r(66950),i=r(71534),a=r(438),s=r(22152),u=r(69071),c=r(56595),f=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),h=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let l=function(t){function e(){var e=t.call(this)||this;return e.decodeRowResult="",e.counters=new Int32Array(6),e}return f(e,t),e.prototype.decodeRow=function(t,r,o){var i,s,f,l,d,p,A=this.findAsteriskPattern(r),g=r.getNextSet(A[1]),y=r.getSize(),w=this.counters;w.fill(0),this.decodeRowResult="";do{e.recordPattern(r,g,w);var v=this.toPattern(w);if(v<0)throw new a.A;d=this.patternToChar(v),this.decodeRowResult+=d,p=g;try{for(var _=(i=void 0,h(w)),C=_.next();!C.done;C=_.next()){var m=C.value;g+=m}}catch(t){i={error:t}}finally{try{C&&!C.done&&(s=_.return)&&s.call(_)}finally{if(i)throw i.error}}g=r.getNextSet(g)}while("*"!==d);this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var E=0;try{for(var I=h(w),S=I.next();!S.done;S=I.next()){var m=S.value;E+=m}}catch(t){f={error:t}}finally{try{S&&!S.done&&(l=I.return)&&l.call(I)}finally{if(f)throw f.error}}if(g===y||!r.get(g)||this.decodeRowResult.length<2)throw new a.A;this.checkChecksums(this.decodeRowResult),this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-2);var T=this.decodeExtended(this.decodeRowResult),O=(A[1]+A[0])/2,R=p+E/2;return new u.A(T,null,0,[new c.A(O,t),new c.A(R,t)],n.A.CODE_93,new Date().getTime())},e.prototype.findAsteriskPattern=function(t){var r=t.getSize(),n=t.getNextSet(0);this.counters.fill(0);for(var o=this.counters,i=n,s=!1,u=o.length,c=0,f=n;f<r;f++)if(t.get(f)!==s)o[c]++;else{if(c===u-1){if(this.toPattern(o)===e.ASTERISK_ENCODING)return new Int32Array([i,f]);i+=o[0]+o[1],o.copyWithin(0,2,2+c-1),o[c-1]=0,o[c]=0,c--}else c++;o[c]=1,s=!s}throw new a.A},e.prototype.toPattern=function(t){var e,r,n=0;try{for(var o=h(t),i=o.next();!i.done;i=o.next()){var a=i.value;n+=a}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}for(var s=0,u=t.length,c=0;c<u;c++){var f=Math.round(9*t[c]/n);if(f<1||f>4)return -1;if((1&c)==0)for(var l=0;l<f;l++)s=s<<1|1;else s<<=f}return s},e.prototype.patternToChar=function(t){for(var r=0;r<e.CHARACTER_ENCODINGS.length;r++)if(e.CHARACTER_ENCODINGS[r]===t)return e.ALPHABET_STRING.charAt(r);throw new a.A},e.prototype.decodeExtended=function(t){for(var e=t.length,r="",n=0;n<e;n++){var o=t.charAt(n);if(o>="a"&&o<="d"){if(n>=e-1)throw new i.A;var a=t.charAt(n+1),s="\0";switch(o){case"d":if(a>="A"&&a<="Z")s=String.fromCharCode(a.charCodeAt(0)+32);else throw new i.A;break;case"a":if(a>="A"&&a<="Z")s=String.fromCharCode(a.charCodeAt(0)-64);else throw new i.A;break;case"b":if(a>="A"&&a<="E")s=String.fromCharCode(a.charCodeAt(0)-38);else if(a>="F"&&a<="J")s=String.fromCharCode(a.charCodeAt(0)-11);else if(a>="K"&&a<="O")s=String.fromCharCode(a.charCodeAt(0)+16);else if(a>="P"&&a<="T")s=String.fromCharCode(a.charCodeAt(0)+43);else if("U"===a)s="\0";else if("V"===a)s="@";else if("W"===a)s="`";else if(a>="X"&&a<="Z")s="";else throw new i.A;break;case"c":if(a>="A"&&a<="O")s=String.fromCharCode(a.charCodeAt(0)-32);else if("Z"===a)s=":";else throw new i.A}r+=s,n++}else r+=o}return r},e.prototype.checkChecksums=function(t){var e=t.length;this.checkOneChecksum(t,e-2,20),this.checkOneChecksum(t,e-1,15)},e.prototype.checkOneChecksum=function(t,r,n){for(var i=1,a=0,s=r-1;s>=0;s--)a+=i*e.ALPHABET_STRING.indexOf(t.charAt(s)),++i>n&&(i=1);if(t.charAt(r)!==e.ALPHABET_STRING[a%47])throw new o.A},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*",e.CHARACTER_ENCODINGS=[276,328,324,322,296,292,290,336,274,266,424,420,418,404,402,394,360,356,354,308,282,344,332,326,300,278,436,434,428,422,406,410,364,358,310,314,302,468,466,458,366,374,430,294,474,470,306,350],e.ASTERISK_ENCODING=e.CHARACTER_ENCODINGS[47],e}(s.A)},74150:(t,e,r)=>{"use strict";r.d(e,{A:()=>A});var n=r(66950),o=r(38988),i=r(322),a=r(1933),s=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},u=function(){function t(t,e){if(0===e.length)throw new o.A;this.field=t;var r=e.length;if(r>1&&0===e[0]){for(var n=1;n<r&&0===e[n];)n++;n===r?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(r-n),i.A.arraycopy(e,n,this.coefficients,0,this.coefficients.length))}else this.coefficients=e}return t.prototype.getCoefficients=function(){return this.coefficients},t.prototype.getDegree=function(){return this.coefficients.length-1},t.prototype.isZero=function(){return 0===this.coefficients[0]},t.prototype.getCoefficient=function(t){return this.coefficients[this.coefficients.length-1-t]},t.prototype.evaluateAt=function(t){if(0===t)return this.getCoefficient(0);if(1===t){var e,r,n=0;try{for(var o=s(this.coefficients),i=o.next();!i.done;i=o.next()){var a=i.value;n=this.field.add(n,a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}for(var u=this.coefficients[0],c=this.coefficients.length,f=1;f<c;f++)u=this.field.add(this.field.multiply(t,u),this.coefficients[f]);return u},t.prototype.add=function(e){if(!this.field.equals(e.field))throw new o.A("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;var r=this.coefficients,n=e.coefficients;if(r.length>n.length){var a=r;r=n,n=a}var s=new Int32Array(n.length),u=n.length-r.length;i.A.arraycopy(n,0,s,0,u);for(var c=u;c<n.length;c++)s[c]=this.field.add(r[c-u],n[c]);return new t(this.field,s)},t.prototype.subtract=function(t){if(!this.field.equals(t.field))throw new o.A("ModulusPolys do not have same ModulusGF field");return t.isZero()?this:this.add(t.negative())},t.prototype.multiply=function(e){return e instanceof t?this.multiplyOther(e):this.multiplyScalar(e)},t.prototype.multiplyOther=function(e){if(!this.field.equals(e.field))throw new o.A("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new t(this.field,new Int32Array([0]));for(var r=this.coefficients,n=r.length,i=e.coefficients,a=i.length,s=new Int32Array(n+a-1),u=0;u<n;u++)for(var c=r[u],f=0;f<a;f++)s[u+f]=this.field.add(s[u+f],this.field.multiply(c,i[f]));return new t(this.field,s)},t.prototype.negative=function(){for(var e=this.coefficients.length,r=new Int32Array(e),n=0;n<e;n++)r[n]=this.field.subtract(0,this.coefficients[n]);return new t(this.field,r)},t.prototype.multiplyScalar=function(e){if(0===e)return new t(this.field,new Int32Array([0]));if(1===e)return this;for(var r=this.coefficients.length,n=new Int32Array(r),o=0;o<r;o++)n[o]=this.field.multiply(this.coefficients[o],e);return new t(this.field,n)},t.prototype.multiplyByMonomial=function(e,r){if(e<0)throw new o.A;if(0===r)return new t(this.field,new Int32Array([0]));for(var n=this.coefficients.length,i=new Int32Array(n+e),a=0;a<n;a++)i[a]=this.field.multiply(this.coefficients[a],r);return new t(this.field,i)},t.prototype.toString=function(){for(var t=new a.A,e=this.getDegree();e>=0;e--){var r=this.getCoefficient(e);0!==r&&(r<0?(t.append(" - "),r=-r):t.length()>0&&t.append(" + "),(0===e||1!==r)&&t.append(r),0!==e&&(1===e?t.append("x"):(t.append("x^"),t.append(e))))}return t.toString()},t}(),c=r(93770),f=r(73179),h=function(){function t(){}return t.prototype.add=function(t,e){return(t+e)%this.modulus},t.prototype.subtract=function(t,e){return(this.modulus+t-e)%this.modulus},t.prototype.exp=function(t){return this.expTable[t]},t.prototype.log=function(t){if(0===t)throw new o.A;return this.logTable[t]},t.prototype.inverse=function(t){if(0===t)throw new f.A;return this.expTable[this.modulus-this.logTable[t]-1]},t.prototype.multiply=function(t,e){return 0===t||0===e?0:this.expTable[(this.logTable[t]+this.logTable[e])%(this.modulus-1)]},t.prototype.getSize=function(){return this.modulus},t.prototype.equals=function(t){return t===this},t}(),l=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),d=function(t){function e(e,r){var n=t.call(this)||this;n.modulus=e,n.expTable=new Int32Array(e),n.logTable=new Int32Array(e);for(var o=1,i=0;i<e;i++)n.expTable[i]=o,o=o*r%e;for(var i=0;i<e-1;i++)n.logTable[n.expTable[i]]=i;return n.zero=new u(n,new Int32Array([0])),n.one=new u(n,new Int32Array([1])),n}return l(e,t),e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,e){if(t<0)throw new o.A;if(0===e)return this.zero;var r=new Int32Array(t+1);return r[0]=e,new u(this,r)},e.PDF417_GF=new e(c.A.NUMBER_OF_CODEWORDS,3),e}(h),p=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let A=function(){function t(){this.field=d.PDF417_GF}return t.prototype.decode=function(t,e,r){for(var o,i,a=new u(this.field,t),s=new Int32Array(e),c=!1,f=e;f>0;f--){var h=a.evaluateAt(this.field.exp(f));s[e-f]=h,0!==h&&(c=!0)}if(!c)return 0;var l=this.field.getOne();if(null!=r)try{for(var d=p(r),A=d.next();!A.done;A=d.next()){var g=A.value,y=this.field.exp(t.length-1-g),w=new u(this.field,new Int32Array([this.field.subtract(0,y),1]));l=l.multiply(w)}}catch(t){o={error:t}}finally{try{A&&!A.done&&(i=d.return)&&i.call(d)}finally{if(o)throw o.error}}for(var v=new u(this.field,s),_=this.runEuclideanAlgorithm(this.field.buildMonomial(e,1),v,e),C=_[0],m=_[1],E=this.findErrorLocations(C),I=this.findErrorMagnitudes(m,C,E),f=0;f<E.length;f++){var S=t.length-1-this.field.log(E[f]);if(S<0)throw n.A.getChecksumInstance();t[S]=this.field.subtract(t[S],I[f])}return E.length},t.prototype.runEuclideanAlgorithm=function(t,e,r){if(t.getDegree()<e.getDegree()){var o=t;t=e,e=o}for(var i=t,a=e,s=this.field.getZero(),u=this.field.getOne();a.getDegree()>=Math.round(r/2);){var c=i,f=s;if(i=a,s=u,i.isZero())throw n.A.getChecksumInstance();a=c;for(var h=this.field.getZero(),l=i.getCoefficient(i.getDegree()),d=this.field.inverse(l);a.getDegree()>=i.getDegree()&&!a.isZero();){var p=a.getDegree()-i.getDegree(),A=this.field.multiply(a.getCoefficient(a.getDegree()),d);h=h.add(this.field.buildMonomial(p,A)),a=a.subtract(i.multiplyByMonomial(p,A))}u=h.multiply(s).subtract(f).negative()}var g=u.getCoefficient(0);if(0===g)throw n.A.getChecksumInstance();var y=this.field.inverse(g);return[u.multiply(y),a.multiply(y)]},t.prototype.findErrorLocations=function(t){for(var e=t.getDegree(),r=new Int32Array(e),o=0,i=1;i<this.field.getSize()&&o<e;i++)0===t.evaluateAt(i)&&(r[o]=this.field.inverse(i),o++);if(o!==e)throw n.A.getChecksumInstance();return r},t.prototype.findErrorMagnitudes=function(t,e,r){for(var n=e.getDegree(),o=new Int32Array(n),i=1;i<=n;i++)o[n-i]=this.field.multiply(i,e.getCoefficient(i));for(var a=new u(this.field,o),s=r.length,c=new Int32Array(s),i=0;i<s;i++){var f=this.field.inverse(r[i]),h=this.field.subtract(0,t.evaluateAt(f)),l=this.field.inverse(a.evaluateAt(f));c[i]=this.field.multiply(h,l)}return c},t}()},74472:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(64510),o=r(10997),i=r(322),a=r(38988),s=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let u=function(t){function e(e,r,n,o,i,s,u){var c=t.call(this,r,n)||this;if(c.dataWidth=o,c.dataHeight=i,c.left=s,c.top=u,4===e.BYTES_PER_ELEMENT){for(var f=r*n,h=new Uint8ClampedArray(f),l=0;l<f;l++){var d=e[l],p=d>>16&255,A=d>>7&510,g=255&d;h[l]=(p+A+g)/4&255}c.luminances=h}else c.luminances=e;if(void 0===o&&(c.dataWidth=r),void 0===i&&(c.dataHeight=n),void 0===s&&(c.left=0),void 0===u&&(c.top=0),c.left+r>c.dataWidth||c.top+n>c.dataHeight)throw new a.A("Crop rectangle does not fit within image data.");return c}return s(e,t),e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new a.A("Requested row is outside the image: "+t);var r=this.getWidth();(null==e||e.length<r)&&(e=new Uint8ClampedArray(r));var n=(t+this.top)*this.dataWidth+this.left;return i.A.arraycopy(this.luminances,n,e,0,r),e},e.prototype.getMatrix=function(){var t=this.getWidth(),e=this.getHeight();if(t===this.dataWidth&&e===this.dataHeight)return this.luminances;var r=t*e,n=new Uint8ClampedArray(r),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return i.A.arraycopy(this.luminances,o,n,0,r),n;for(var a=0;a<e;a++){var s=a*t;i.A.arraycopy(this.luminances,o,n,s,t),o+=this.dataWidth}return n},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,r,n,o){return new e(this.luminances,n,o,this.dataWidth,this.dataHeight,this.left+t,this.top+r)},e.prototype.invert=function(){return new n.A(this)},e}(o.A)},76142:(t,e,r)=>{"use strict";r.r(e),r.d(e,{AbstractExpandedDecoder:()=>tW.A,ArgumentException:()=>i.A,ArithmeticException:()=>a.A,AztecCode:()=>tP.A,AztecCodeReader:()=>tR.A,AztecCodeWriter:()=>tb.A,AztecDecoder:()=>tB.A,AztecDetector:()=>tL.A,AztecDetectorResult:()=>tN.A,AztecEncoder:()=>tD.A,AztecHighLevelEncoder:()=>tM.A,AztecPoint:()=>tL.b,BarcodeFormat:()=>y.A,Binarizer:()=>w.A,BinaryBitmap:()=>v.A,BitArray:()=>F.A,BitMatrix:()=>k.A,BitSource:()=>V.A,CharacterSetECI:()=>H.A,ChecksumException:()=>s.A,CodaBarReader:()=>tY.A,Code128Reader:()=>tk.A,Code39Reader:()=>tH.A,Code93Reader:()=>tU.A,DataMatrixDecodedBitStreamParser:()=>to.A,DataMatrixDefaultPlacement:()=>ti.A,DataMatrixErrorCorrection:()=>ta.A,DataMatrixHighLevelEncoder:()=>ts.A,DataMatrixReader:()=>tn.A,DataMatrixSymbolInfo:()=>tu.A,DataMatrixSymbolShapeHint:()=>tc.t3,DataMatrixWriter:()=>tf.A,DecodeHintType:()=>_.A,DecoderResult:()=>U.A,DefaultGridSampler:()=>X.A,DetectorResult:()=>G.A,EAN13Reader:()=>tF.A,EncodeHintType:()=>W.A,Exception:()=>u.A,FormatException:()=>c.A,GenericGF:()=>$.A,GenericGFPoly:()=>tt.A,GlobalHistogramBinarizer:()=>z.A,GridSampler:()=>j.A,GridSamplerInstance:()=>Y.A,HybridBinarizer:()=>Z.A,ITFReader:()=>tV.A,IllegalArgumentException:()=>f.A,IllegalStateException:()=>h.A,InvertedLuminanceSource:()=>C.A,LuminanceSource:()=>m.A,MathUtils:()=>q.A,MultiFormatOneDReader:()=>tj.A,MultiFormatReader:()=>E.A,MultiFormatWriter:()=>I.A,NotFoundException:()=>l.A,OneDReader:()=>tx.A,PDF417DecodedBitStreamParser:()=>td.A,PDF417DecoderErrorCorrection:()=>tp.A,PDF417Reader:()=>th.A,PDF417ResultMetadata:()=>tl.A,PerspectiveTransform:()=>K.A,PlanarYUVLuminanceSource:()=>S.A,QRCodeByteMatrix:()=>tT.A,QRCodeDataMask:()=>tm.A,QRCodeDecodedBitStreamParser:()=>tC.A,QRCodeDecoderErrorCorrectionLevel:()=>ty.A,QRCodeDecoderFormatInformation:()=>tw.A,QRCodeEncoder:()=>tE.A,QRCodeEncoderQRCode:()=>tI.A,QRCodeMaskUtil:()=>tO.A,QRCodeMatrixUtil:()=>tS.A,QRCodeMode:()=>t_.A,QRCodeReader:()=>tA.A,QRCodeVersion:()=>tv.A,QRCodeWriter:()=>tg.A,RGBLuminanceSource:()=>R.A,RSS14Reader:()=>tX.A,RSSExpandedReader:()=>tG.A,ReaderException:()=>d.A,ReedSolomonDecoder:()=>te.A,ReedSolomonEncoder:()=>tr.A,ReedSolomonException:()=>p.A,Result:()=>T.A,ResultMetadataType:()=>O.A,ResultPoint:()=>b.A,StringUtils:()=>Q.A,UnsupportedOperationException:()=>A.A,WhiteRectangleDetector:()=>J.A,WriterException:()=>g.A,ZXingArrays:()=>B.A,ZXingCharset:()=>P.A,ZXingInteger:()=>x.A,ZXingStandardCharsets:()=>L.A,ZXingStringBuilder:()=>D.A,ZXingStringEncoding:()=>M.A,ZXingSystem:()=>N.A,createAbstractExpandedDecoder:()=>tz.$});var n=r(26530),o={};for(let t in n)0>["default","ArgumentException","ArithmeticException","ChecksumException","Exception","FormatException","IllegalArgumentException","IllegalStateException","NotFoundException","ReaderException","ReedSolomonException","UnsupportedOperationException","WriterException","BarcodeFormat","Binarizer","BinaryBitmap","DecodeHintType","InvertedLuminanceSource","LuminanceSource","MultiFormatReader","MultiFormatWriter","PlanarYUVLuminanceSource","Result","ResultMetadataType","RGBLuminanceSource","ResultPoint","ZXingSystem","ZXingStringBuilder","ZXingStringEncoding","ZXingCharset","ZXingArrays","ZXingStandardCharsets","ZXingInteger","BitArray","BitMatrix","BitSource","CharacterSetECI","DecoderResult","DefaultGridSampler","DetectorResult","EncodeHintType","GlobalHistogramBinarizer","GridSampler","GridSamplerInstance","HybridBinarizer","PerspectiveTransform","StringUtils","MathUtils","WhiteRectangleDetector","GenericGF","GenericGFPoly","ReedSolomonDecoder","ReedSolomonEncoder","DataMatrixReader","DataMatrixDecodedBitStreamParser","DataMatrixDefaultPlacement","DataMatrixErrorCorrection","DataMatrixHighLevelEncoder","DataMatrixSymbolInfo","DataMatrixSymbolShapeHint","DataMatrixWriter","PDF417Reader","PDF417ResultMetadata","PDF417DecodedBitStreamParser","PDF417DecoderErrorCorrection","QRCodeReader","QRCodeWriter","QRCodeDecoderErrorCorrectionLevel","QRCodeDecoderFormatInformation","QRCodeVersion","QRCodeMode","QRCodeDecodedBitStreamParser","QRCodeDataMask","QRCodeEncoder","QRCodeEncoderQRCode","QRCodeMatrixUtil","QRCodeByteMatrix","QRCodeMaskUtil","AztecCodeReader","AztecCodeWriter","AztecDetectorResult","AztecEncoder","AztecHighLevelEncoder","AztecCode","AztecDecoder","AztecDetector","AztecPoint","OneDReader","EAN13Reader","Code128Reader","ITFReader","Code39Reader","Code93Reader","RSS14Reader","RSSExpandedReader","AbstractExpandedDecoder","createAbstractExpandedDecoder","MultiFormatOneDReader","CodaBarReader"].indexOf(t)&&(o[t]=()=>n[t]);r.d(e,o);var i=r(19106),a=r(73179),s=r(66950),u=r(68139),c=r(71534),f=r(38988),h=r(39778),l=r(438),d=r(10646),p=r(20738),A=r(3451),g=r(93682),y=r(25969),w=r(19116),v=r(16148),_=r(79417),C=r(64510),m=r(10997),E=r(92251),I=r(47339),S=r(82965),T=r(69071),O=r(43358),R=r(74472),b=r(56595),N=r(322),D=r(1933),M=r(63623),P=r(2257),B=r(6727),L=r(91375),x=r(63479),F=r(22868),k=r(10782),V=r(85770),H=r(59612),U=r(55701),X=r(92727),G=r(56451),W=r(27217),z=r(55695),j=r(48852),Y=r(20367),Z=r(38972),K=r(39798),Q=r(23510),q=r(79886),J=r(17391),$=r(55192),tt=r(34382),te=r(60109),tr=r(29477),tn=r(41205),to=r(71411),ti=r(73693),ta=r(55277),ts=r(7779),tu=r(72169),tc=r(93782),tf=r(35509),th=r(81339),tl=r(27051),td=r(97016),tp=r(74150),tA=r(85469),tg=r(99496),ty=r(61536),tw=r(78903),tv=r(21876),t_=r(26143),tC=r(41094),tm=r(78160),tE=r(35168),tI=r(6974),tS=r(6537),tT=r(52771),tO=r(85808),tR=r(72106),tb=r(14842),tN=r(71614),tD=r(23583),tM=r(50942),tP=r(61767),tB=r(64191),tL=r(43197),tx=r(22152),tF=r(28822),tk=r(45332),tV=r(3411),tH=r(65649),tU=r(73977),tX=r(10659),tG=r(2605),tW=r(42563),tz=r(667),tj=r(10692),tY=r(64476)},78160:(t,e,r)=>{"use strict";var n;r.d(e,{A:()=>o}),function(t){t[t.DATA_MASK_000=0]="DATA_MASK_000",t[t.DATA_MASK_001=1]="DATA_MASK_001",t[t.DATA_MASK_010=2]="DATA_MASK_010",t[t.DATA_MASK_011=3]="DATA_MASK_011",t[t.DATA_MASK_100=4]="DATA_MASK_100",t[t.DATA_MASK_101=5]="DATA_MASK_101",t[t.DATA_MASK_110=6]="DATA_MASK_110",t[t.DATA_MASK_111=7]="DATA_MASK_111"}(n||(n={}));let o=function(){function t(t,e){this.value=t,this.isMasked=e}return t.prototype.unmaskBitMatrix=function(t,e){for(var r=0;r<e;r++)for(var n=0;n<e;n++)this.isMasked(r,n)&&t.flip(n,r)},t.values=new Map([[n.DATA_MASK_000,new t(n.DATA_MASK_000,function(t,e){return(t+e&1)==0})],[n.DATA_MASK_001,new t(n.DATA_MASK_001,function(t,e){return(1&t)==0})],[n.DATA_MASK_010,new t(n.DATA_MASK_010,function(t,e){return e%3==0})],[n.DATA_MASK_011,new t(n.DATA_MASK_011,function(t,e){return(t+e)%3==0})],[n.DATA_MASK_100,new t(n.DATA_MASK_100,function(t,e){return(Math.floor(t/2)+Math.floor(e/3)&1)==0})],[n.DATA_MASK_101,new t(n.DATA_MASK_101,function(t,e){return t*e%6==0})],[n.DATA_MASK_110,new t(n.DATA_MASK_110,function(t,e){return t*e%6<3})],[n.DATA_MASK_111,new t(n.DATA_MASK_111,function(t,e){return(t+e+t*e%3&1)==0})]]),t}()},78903:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(61536),o=r(63479),i=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let a=function(){function t(t){this.errorCorrectionLevel=n.A.forBits(t>>3&3),this.dataMask=7&t}return t.numBitsDiffering=function(t,e){return o.A.bitCount(t^e)},t.decodeFormatInformation=function(e,r){var n=t.doDecodeFormatInformation(e,r);return null!==n?n:t.doDecodeFormatInformation(e^t.FORMAT_INFO_MASK_QR,r^t.FORMAT_INFO_MASK_QR)},t.doDecodeFormatInformation=function(e,r){var n,o,a=Number.MAX_SAFE_INTEGER,s=0;try{for(var u=i(t.FORMAT_INFO_DECODE_LOOKUP),c=u.next();!c.done;c=u.next()){var f=c.value,h=f[0];if(h===e||h===r)return new t(f[1]);var l=t.numBitsDiffering(e,h);l<a&&(s=f[1],a=l),e!==r&&(l=t.numBitsDiffering(r,h))<a&&(s=f[1],a=l)}}catch(t){n={error:t}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(n)throw n.error}}return a<=3?new t(s):null},t.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},t.prototype.getDataMask=function(){return this.dataMask},t.prototype.hashCode=function(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask},t.prototype.equals=function(e){return e instanceof t&&this.errorCorrectionLevel===e.errorCorrectionLevel&&this.dataMask===e.dataMask},t.FORMAT_INFO_MASK_QR=21522,t.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])],t}()},79417:(t,e,r)=>{"use strict";var n;r.d(e,{A:()=>o}),function(t){t[t.OTHER=0]="OTHER",t[t.PURE_BARCODE=1]="PURE_BARCODE",t[t.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",t[t.TRY_HARDER=3]="TRY_HARDER",t[t.CHARACTER_SET=4]="CHARACTER_SET",t[t.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",t[t.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",t[t.ENABLE_CODE_39_EXTENDED_MODE=7]="ENABLE_CODE_39_EXTENDED_MODE",t[t.ASSUME_GS1=8]="ASSUME_GS1",t[t.RETURN_CODABAR_START_END=9]="RETURN_CODABAR_START_END",t[t.NEED_RESULT_POINT_CALLBACK=10]="NEED_RESULT_POINT_CALLBACK",t[t.ALLOWED_EAN_EXTENSIONS=11]="ALLOWED_EAN_EXTENSIONS"}(n||(n={}));let o=n},79540:(t,e,r)=>{"use strict";r.d(e,{c:()=>a});var n=r(11623),o=r(10692),i=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a=function(t){function e(e,r){return void 0===e&&(e=500),t.call(this,new o.A(r),e,r)||this}return i(e,t),e}(n.J)},79886:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=function(){function t(){}return t.round=function(t){return isNaN(t)?0:t<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:t>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:t+(t<0?-.5:.5)|0},t.distance=function(t,e,r,n){var o=t-r,i=e-n;return Math.sqrt(o*o+i*i)},t.sum=function(t){for(var e=0,r=0,n=t.length;r!==n;r++)e+=t[r];return e},t}()},81339:(t,e,r)=>{"use strict";r.d(e,{A:()=>V});var n=r(25969),o=r(66950),i=r(71534),a=r(438),s=r(69071),u=r(93770),c=r(63479),f=r(43358),h=r(56595),l=r(322),d=r(6727),p=function(){function t(t,e){this.bits=t,this.points=e}return t.prototype.getBits=function(){return this.bits},t.prototype.getPoints=function(){return this.points},t}(),A=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g=function(){function t(){}return t.detectMultiple=function(e,r,n){var o=e.getBlackMatrix(),i=t.detect(n,o);return i.length||((o=o.clone()).rotate180(),i=t.detect(n,o)),new p(o,i)},t.detect=function(e,r){for(var n,o,i=[],a=0,s=0,u=!1;a<r.getHeight();){var c=t.findVertices(r,a,s);if(null==c[0]&&null==c[3]){if(!u)break;u=!1,s=0;try{for(var f=(n=void 0,A(i)),h=f.next();!h.done;h=f.next()){var l=h.value;null!=l[1]&&(a=Math.trunc(Math.max(a,l[1].getY()))),null!=l[3]&&(a=Math.max(a,Math.trunc(l[3].getY())))}}catch(t){n={error:t}}finally{try{h&&!h.done&&(o=f.return)&&o.call(f)}finally{if(n)throw n.error}}a+=t.ROW_STEP;continue}if(u=!0,i.push(c),!e)break;null!=c[2]?(s=Math.trunc(c[2].getX()),a=Math.trunc(c[2].getY())):(s=Math.trunc(c[4].getX()),a=Math.trunc(c[4].getY()))}return i},t.findVertices=function(e,r,n){var o=e.getHeight(),i=e.getWidth(),a=Array(8);return t.copyToResult(a,t.findRowsWithPattern(e,o,i,r,n,t.START_PATTERN),t.INDEXES_START_PATTERN),null!=a[4]&&(n=Math.trunc(a[4].getX()),r=Math.trunc(a[4].getY())),t.copyToResult(a,t.findRowsWithPattern(e,o,i,r,n,t.STOP_PATTERN),t.INDEXES_STOP_PATTERN),a},t.copyToResult=function(t,e,r){for(var n=0;n<r.length;n++)t[r[n]]=e[n]},t.findRowsWithPattern=function(e,r,n,o,i,a){for(var s=[,,,,],u=!1,c=new Int32Array(a.length);o<r;o+=t.ROW_STEP){var f=t.findGuardPattern(e,i,o,n,!1,a,c);if(null!=f){for(;o>0;){var l=t.findGuardPattern(e,i,--o,n,!1,a,c);if(null!=l)f=l;else{o++;break}}s[0]=new h.A(f[0],o),s[1]=new h.A(f[1],o),u=!0;break}}var p=o+1;if(u){for(var A=0,l=Int32Array.from([Math.trunc(s[0].getX()),Math.trunc(s[1].getX())]);p<r;p++){var f=t.findGuardPattern(e,l[0],p,n,!1,a,c);if(null!=f&&Math.abs(l[0]-f[0])<t.MAX_PATTERN_DRIFT&&Math.abs(l[1]-f[1])<t.MAX_PATTERN_DRIFT)l=f,A=0;else if(A>t.SKIPPED_ROW_COUNT_MAX)break;else A++}p-=A+1,s[2]=new h.A(l[0],p),s[3]=new h.A(l[1],p)}return p-o<t.BARCODE_MIN_HEIGHT&&d.A.fill(s,null),s},t.findGuardPattern=function(e,r,n,o,i,a,s){d.A.fillWithin(s,0,s.length,0);for(var u=r,c=0;e.get(u,n)&&u>0&&c++<t.MAX_PIXEL_DRIFT;)u--;for(var f=u,h=0,p=a.length,A=i;f<o;f++)if(e.get(f,n)!==A)s[h]++;else{if(h===p-1){if(t.patternMatchVariance(s,a,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE)return new Int32Array([u,f]);u+=s[0]+s[1],l.A.arraycopy(s,2,s,0,h-1),s[h-1]=0,s[h]=0,h--}else h++;s[h]=1,A=!A}return h===p-1&&t.patternMatchVariance(s,a,t.MAX_INDIVIDUAL_VARIANCE)<t.MAX_AVG_VARIANCE?new Int32Array([u,f-1]):null},t.patternMatchVariance=function(t,e,r){for(var n=t.length,o=0,i=0,a=0;a<n;a++)o+=t[a],i+=e[a];if(o<i)return 1/0;var s=o/i;r*=s;for(var u=0,c=0;c<n;c++){var f=t[c],h=e[c]*s,l=f>h?f-h:h-f;if(l>r)return 1/0;u+=l}return u/o},t.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),t.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),t.MAX_AVG_VARIANCE=.42,t.MAX_INDIVIDUAL_VARIANCE=.8,t.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),t.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),t.MAX_PIXEL_DRIFT=3,t.MAX_PATTERN_DRIFT=5,t.SKIPPED_ROW_COUNT_MAX=25,t.ROW_STEP=5,t.BARCODE_MIN_HEIGHT=10,t}(),y=r(79886),w=r(74150),v=function(){function t(e,r,n,o,i){e instanceof t?this.constructor_2(e):this.constructor_1(e,r,n,o,i)}return t.prototype.constructor_1=function(t,e,r,n,o){var i=null==e||null==r,s=null==n||null==o;if(i&&s)throw new a.A;i?(e=new h.A(0,n.getY()),r=new h.A(0,o.getY())):s&&(n=new h.A(t.getWidth()-1,e.getY()),o=new h.A(t.getWidth()-1,r.getY())),this.image=t,this.topLeft=e,this.bottomLeft=r,this.topRight=n,this.bottomRight=o,this.minX=Math.trunc(Math.min(e.getX(),r.getX())),this.maxX=Math.trunc(Math.max(n.getX(),o.getX())),this.minY=Math.trunc(Math.min(e.getY(),n.getY())),this.maxY=Math.trunc(Math.max(r.getY(),o.getY()))},t.prototype.constructor_2=function(t){this.image=t.image,this.topLeft=t.getTopLeft(),this.bottomLeft=t.getBottomLeft(),this.topRight=t.getTopRight(),this.bottomRight=t.getBottomRight(),this.minX=t.getMinX(),this.maxX=t.getMaxX(),this.minY=t.getMinY(),this.maxY=t.getMaxY()},t.merge=function(e,r){return null==e?r:null==r?e:new t(e.image,e.topLeft,e.bottomLeft,r.topRight,r.bottomRight)},t.prototype.addMissingRows=function(e,r,n){var o=this.topLeft,i=this.bottomLeft,a=this.topRight,s=this.bottomRight;if(e>0){var u=n?this.topLeft:this.topRight,c=Math.trunc(u.getY()-e);c<0&&(c=0);var f=new h.A(u.getX(),c);n?o=f:a=f}if(r>0){var l=n?this.bottomLeft:this.bottomRight,d=Math.trunc(l.getY()+r);d>=this.image.getHeight()&&(d=this.image.getHeight()-1);var p=new h.A(l.getX(),d);n?i=p:s=p}return new t(this.image,o,i,a,s)},t.prototype.getMinX=function(){return this.minX},t.prototype.getMaxX=function(){return this.maxX},t.prototype.getMinY=function(){return this.minY},t.prototype.getMaxY=function(){return this.maxY},t.prototype.getTopLeft=function(){return this.topLeft},t.prototype.getTopRight=function(){return this.topRight},t.prototype.getBottomLeft=function(){return this.bottomLeft},t.prototype.getBottomRight=function(){return this.bottomRight},t}(),_=function(){function t(t,e,r,n){this.columnCount=t,this.errorCorrectionLevel=n,this.rowCountUpperPart=e,this.rowCountLowerPart=r,this.rowCount=e+r}return t.prototype.getColumnCount=function(){return this.columnCount},t.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},t.prototype.getRowCount=function(){return this.rowCount},t.prototype.getRowCountUpperPart=function(){return this.rowCountUpperPart},t.prototype.getRowCountLowerPart=function(){return this.rowCountLowerPart},t}(),C=function(){function t(){this.buffer=""}return t.form=function(t,e){var r=-1;return t.replace(/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g,function(t,n,o,i,a,s){if("%%"===t)return"%";if(void 0!==e[++r]){t=i?parseInt(i.substr(1)):void 0;var u,c=a?parseInt(a.substr(1)):void 0;switch(s){case"s":u=e[r];break;case"c":u=e[r][0];break;case"f":u=parseFloat(e[r]).toFixed(t);break;case"p":u=parseFloat(e[r]).toPrecision(t);break;case"e":u=parseFloat(e[r]).toExponential(t);break;case"x":u=parseInt(e[r]).toString(c||16);break;case"d":u=parseFloat(parseInt(e[r],c||10).toPrecision(t)).toFixed(0)}u="object"==typeof u?JSON.stringify(u):(+u).toString(c);for(var f=parseInt(o),h=o&&o[0]+""=="0"?"0":" ";u.length<f;)u=void 0!==n?u+h:h+u;return u}})},t.prototype.format=function(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];this.buffer+=t.form(e,r)},t.prototype.toString=function(){return this.buffer},t}(),m=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},E=function(){function t(t){this.boundingBox=new v(t),this.codewords=Array(t.getMaxY()-t.getMinY()+1)}return t.prototype.getCodewordNearby=function(e){var r=this.getCodeword(e);if(null!=r)return r;for(var n=1;n<t.MAX_NEARBY_DISTANCE;n++){var o=this.imageRowToCodewordIndex(e)-n;if(o>=0&&null!=(r=this.codewords[o])||(o=this.imageRowToCodewordIndex(e)+n)<this.codewords.length&&null!=(r=this.codewords[o]))return r}return null},t.prototype.imageRowToCodewordIndex=function(t){return t-this.boundingBox.getMinY()},t.prototype.setCodeword=function(t,e){this.codewords[this.imageRowToCodewordIndex(t)]=e},t.prototype.getCodeword=function(t){return this.codewords[this.imageRowToCodewordIndex(t)]},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.getCodewords=function(){return this.codewords},t.prototype.toString=function(){var t,e,r=new C,n=0;try{for(var o=m(this.codewords),i=o.next();!i.done;i=o.next()){var a=i.value;if(null==a){r.format("%3d:    |   %n",n++);continue}r.format("%3d: %3d|%3d%n",n++,a.getRowNumber(),a.getValue())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r.toString()},t.MAX_NEARBY_DISTANCE=5,t}(),I=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},S=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},T=function(){function t(){this.values=new Map}return t.prototype.setValue=function(t){t=Math.trunc(t);var e=this.values.get(t);null==e&&(e=0),e++,this.values.set(t,e)},t.prototype.getValue=function(){var t,e,r=-1,n=[],o=function(t,e){var o={getKey:function(){return t},getValue:function(){return e}};o.getValue()>r?(r=o.getValue(),(n=[]).push(o.getKey())):o.getValue()===r&&n.push(o.getKey())};try{for(var i=I(this.values.entries()),a=i.next();!a.done;a=i.next()){var s=S(a.value,2),c=s[0],f=s[1];o(c,f)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return u.A.toIntArray(n)},t.prototype.getConfidence=function(t){return this.values.get(t)},t}(),O=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),R=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},b=function(t){function e(e,r){var n=t.call(this,e)||this;return n._isLeft=r,n}return O(e,t),e.prototype.setRowNumbers=function(){var t,e;try{for(var r=R(this.getCodewords()),n=r.next();!n.done;n=r.next()){var o=n.value;null!=o&&o.setRowNumberAsRowIndicatorColumn()}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.adjustCompleteIndicatorColumnRowNumbers=function(t){var e=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(e,t);for(var r=this.getBoundingBox(),n=this._isLeft?r.getTopLeft():r.getTopRight(),o=this._isLeft?r.getBottomLeft():r.getBottomRight(),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),a=this.imageRowToCodewordIndex(Math.trunc(o.getY())),s=-1,u=1,c=0,f=i;f<a;f++)if(null!=e[f]){var h=e[f],l=h.getRowNumber()-s;if(0===l)c++;else if(1===l)u=Math.max(u,c),c=1,s=h.getRowNumber();else if(l<0||h.getRowNumber()>=t.getRowCount()||l>f)e[f]=null;else{for(var d=void 0,p=(d=u>2?(u-2)*l:l)>=f,A=1;A<=d&&!p;A++)p=null!=e[f-A];p?e[f]=null:(s=h.getRowNumber(),c=1)}}},e.prototype.getRowHeights=function(){var t,e,r=this.getBarcodeMetadata();if(null==r)return null;this.adjustIncompleteIndicatorColumnRowNumbers(r);var n=new Int32Array(r.getRowCount());try{for(var o=R(this.getCodewords()),i=o.next();!i.done;i=o.next()){var a=i.value;if(null!=a){var s=a.getRowNumber();if(s>=n.length)continue;n[s]++}}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return n},e.prototype.adjustIncompleteIndicatorColumnRowNumbers=function(t){for(var e=this.getBoundingBox(),r=this._isLeft?e.getTopLeft():e.getTopRight(),n=this._isLeft?e.getBottomLeft():e.getBottomRight(),o=this.imageRowToCodewordIndex(Math.trunc(r.getY())),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),a=this.getCodewords(),s=-1,u=1,c=0,f=o;f<i;f++)if(null!=a[f]){var h=a[f];h.setRowNumberAsRowIndicatorColumn();var l=h.getRowNumber()-s;0===l?c++:1===l?(u=Math.max(u,c),c=1,s=h.getRowNumber()):h.getRowNumber()>=t.getRowCount()?a[f]=null:(s=h.getRowNumber(),c=1)}},e.prototype.getBarcodeMetadata=function(){var t,e,r=this.getCodewords(),n=new T,o=new T,i=new T,a=new T;try{for(var s=R(r),c=s.next();!c.done;c=s.next()){var f=c.value;if(null!=f){f.setRowNumberAsRowIndicatorColumn();var h=f.getValue()%30,l=f.getRowNumber();switch(!this._isLeft&&(l+=2),l%3){case 0:o.setValue(3*h+1);break;case 1:a.setValue(h/3),i.setValue(h%3);break;case 2:n.setValue(h+1)}}}}catch(e){t={error:e}}finally{try{c&&!c.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}if(0===n.getValue().length||0===o.getValue().length||0===i.getValue().length||0===a.getValue().length||n.getValue()[0]<1||o.getValue()[0]+i.getValue()[0]<u.A.MIN_ROWS_IN_BARCODE||o.getValue()[0]+i.getValue()[0]>u.A.MAX_ROWS_IN_BARCODE)return null;var d=new _(n.getValue()[0],o.getValue()[0],i.getValue()[0],a.getValue()[0]);return this.removeIncorrectCodewords(r,d),d},e.prototype.removeIncorrectCodewords=function(t,e){for(var r=0;r<t.length;r++){var n=t[r];if(null!=t[r]){var o=n.getValue()%30,i=n.getRowNumber();if(i>e.getRowCount()){t[r]=null;continue}switch(!this._isLeft&&(i+=2),i%3){case 0:3*o+1!==e.getRowCountUpperPart()&&(t[r]=null);break;case 1:(Math.trunc(o/3)!==e.getErrorCorrectionLevel()||o%3!==e.getRowCountLowerPart())&&(t[r]=null);break;case 2:o+1!==e.getColumnCount()&&(t[r]=null)}}}},e.prototype.isLeft=function(){return this._isLeft},e.prototype.toString=function(){return"IsLeft: "+this._isLeft+"\n"+t.prototype.toString.call(this)},e}(E),N=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},D=function(){function t(t,e){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=t,this.barcodeColumnCount=t.getColumnCount(),this.boundingBox=e,this.detectionResultColumns=Array(this.barcodeColumnCount+2)}return t.prototype.getDetectionResultColumns=function(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);var t,e=u.A.MAX_CODEWORDS_IN_BARCODE;do t=e,e=this.adjustRowNumbersAndGetCount();while(e>0&&e<t);return this.detectionResultColumns},t.prototype.adjustIndicatorColumnRowNumbers=function(t){null!=t&&t.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)},t.prototype.adjustRowNumbersAndGetCount=function(){var t=this.adjustRowNumbersByRow();if(0===t)return 0;for(var e=1;e<this.barcodeColumnCount+1;e++)for(var r=this.detectionResultColumns[e].getCodewords(),n=0;n<r.length;n++)null!=r[n]&&(r[n].hasValidRowNumber()||this.adjustRowNumbers(e,n,r));return t},t.prototype.adjustRowNumbersByRow=function(){return this.adjustRowNumbersFromBothRI(),this.adjustRowNumbersFromLRI()+this.adjustRowNumbersFromRRI()},t.prototype.adjustRowNumbersFromBothRI=function(){if(null!=this.detectionResultColumns[0]&&null!=this.detectionResultColumns[this.barcodeColumnCount+1]){for(var t=this.detectionResultColumns[0].getCodewords(),e=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),r=0;r<t.length;r++)if(null!=t[r]&&null!=e[r]&&t[r].getRowNumber()===e[r].getRowNumber())for(var n=1;n<=this.barcodeColumnCount;n++){var o=this.detectionResultColumns[n].getCodewords()[r];null!=o&&(o.setRowNumber(t[r].getRowNumber()),o.hasValidRowNumber()||(this.detectionResultColumns[n].getCodewords()[r]=null))}}},t.prototype.adjustRowNumbersFromRRI=function(){if(null==this.detectionResultColumns[this.barcodeColumnCount+1])return 0;for(var e=0,r=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,a=this.barcodeColumnCount+1;a>0&&i<this.ADJUST_ROW_NUMBER_SKIP;a--){var s=this.detectionResultColumns[a].getCodewords()[n];null!=s&&(i=t.adjustRowNumberIfValid(o,i,s),!s.hasValidRowNumber()&&e++)}return e},t.prototype.adjustRowNumbersFromLRI=function(){if(null==this.detectionResultColumns[0])return 0;for(var e=0,r=this.detectionResultColumns[0].getCodewords(),n=0;n<r.length;n++)if(null!=r[n])for(var o=r[n].getRowNumber(),i=0,a=1;a<this.barcodeColumnCount+1&&i<this.ADJUST_ROW_NUMBER_SKIP;a++){var s=this.detectionResultColumns[a].getCodewords()[n];null!=s&&(i=t.adjustRowNumberIfValid(o,i,s),!s.hasValidRowNumber()&&e++)}return e},t.adjustRowNumberIfValid=function(t,e,r){return null==r||!r.hasValidRowNumber()&&(r.isValidRowNumber(t)?(r.setRowNumber(t),e=0):++e),e},t.prototype.adjustRowNumbers=function(e,r,n){if(null!=this.detectionResultColumns[e-1]){var o,i,a=n[r],s=this.detectionResultColumns[e-1].getCodewords(),u=s;null!=this.detectionResultColumns[e+1]&&(u=this.detectionResultColumns[e+1].getCodewords());var c=Array(14);c[2]=s[r],c[3]=u[r],r>0&&(c[0]=n[r-1],c[4]=s[r-1],c[5]=u[r-1]),r>1&&(c[8]=n[r-2],c[10]=s[r-2],c[11]=u[r-2]),r<n.length-1&&(c[1]=n[r+1],c[6]=s[r+1],c[7]=u[r+1]),r<n.length-2&&(c[9]=n[r+2],c[12]=s[r+2],c[13]=u[r+2]);try{for(var f=N(c),h=f.next();!h.done;h=f.next()){var l=h.value;if(t.adjustRowNumber(a,l))return}}catch(t){o={error:t}}finally{try{h&&!h.done&&(i=f.return)&&i.call(f)}finally{if(o)throw o.error}}}},t.adjustRowNumber=function(t,e){return null!=e&&!!e.hasValidRowNumber()&&e.getBucket()===t.getBucket()&&(t.setRowNumber(e.getRowNumber()),!0)},t.prototype.getBarcodeColumnCount=function(){return this.barcodeColumnCount},t.prototype.getBarcodeRowCount=function(){return this.barcodeMetadata.getRowCount()},t.prototype.getBarcodeECLevel=function(){return this.barcodeMetadata.getErrorCorrectionLevel()},t.prototype.setBoundingBox=function(t){this.boundingBox=t},t.prototype.getBoundingBox=function(){return this.boundingBox},t.prototype.setDetectionResultColumn=function(t,e){this.detectionResultColumns[t]=e},t.prototype.getDetectionResultColumn=function(t){return this.detectionResultColumns[t]},t.prototype.toString=function(){var t=this.detectionResultColumns[0];null==t&&(t=this.detectionResultColumns[this.barcodeColumnCount+1]);for(var e=new C,r=0;r<t.getCodewords().length;r++){e.format("CW %3d:",r);for(var n=0;n<this.barcodeColumnCount+2;n++){if(null==this.detectionResultColumns[n]){e.format("    |   ");continue}var o=this.detectionResultColumns[n].getCodewords()[r];if(null==o){e.format("    |   ");continue}e.format(" %3d|%3d",o.getRowNumber(),o.getValue())}e.format("%n")}return e.toString()},t}(),M=function(){function t(e,r,n,o){this.rowNumber=t.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(r),this.bucket=Math.trunc(n),this.value=Math.trunc(o)}return t.prototype.hasValidRowNumber=function(){return this.isValidRowNumber(this.rowNumber)},t.prototype.isValidRowNumber=function(e){return e!==t.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3},t.prototype.setRowNumberAsRowIndicatorColumn=function(){this.rowNumber=Math.trunc(3*Math.trunc(this.value/30)+Math.trunc(this.bucket/3))},t.prototype.getWidth=function(){return this.endX-this.startX},t.prototype.getStartX=function(){return this.startX},t.prototype.getEndX=function(){return this.endX},t.prototype.getBucket=function(){return this.bucket},t.prototype.getValue=function(){return this.value},t.prototype.getRowNumber=function(){return this.rowNumber},t.prototype.setRowNumber=function(t){this.rowNumber=t},t.prototype.toString=function(){return this.rowNumber+"|"+this.value},t.BARCODE_ROW_UNKNOWN=-1,t}(),P=r(46263),B=function(){function t(){}return t.initialize=function(){for(var e=0;e<u.A.SYMBOL_TABLE.length;e++)for(var r=u.A.SYMBOL_TABLE[e],n=1&r,o=0;o<u.A.BARS_IN_MODULE;o++){for(var i=0;(1&r)===n;)i+=1,r>>=1;n=1&r,t.RATIOS_TABLE[e]||(t.RATIOS_TABLE[e]=Array(u.A.BARS_IN_MODULE)),t.RATIOS_TABLE[e][u.A.BARS_IN_MODULE-o-1]=Math.fround(i/u.A.MODULES_IN_CODEWORD)}this.bSymbolTableReady=!0},t.getDecodedValue=function(e){var r=t.getDecodedCodewordValue(t.sampleBitCounts(e));return -1!==r?r:t.getClosestDecodedValue(e)},t.sampleBitCounts=function(t){for(var e=y.A.sum(t),r=new Int32Array(u.A.BARS_IN_MODULE),n=0,o=0,i=0;i<u.A.MODULES_IN_CODEWORD;i++){var a=e/(2*u.A.MODULES_IN_CODEWORD)+i*e/u.A.MODULES_IN_CODEWORD;o+t[n]<=a&&(o+=t[n],n++),r[n]++}return r},t.getDecodedCodewordValue=function(e){var r=t.getBitValue(e);return -1===u.A.getCodeword(r)?-1:r},t.getBitValue=function(t){for(var e=0,r=0;r<t.length;r++)for(var n=0;n<t[r];n++)e=e<<1|r%2==0;return Math.trunc(e)},t.getClosestDecodedValue=function(e){var r=y.A.sum(e),n=Array(u.A.BARS_IN_MODULE);if(r>1)for(var o=0;o<n.length;o++)n[o]=Math.fround(e[o]/r);var i=P.A.MAX_VALUE,a=-1;this.bSymbolTableReady||t.initialize();for(var s=0;s<t.RATIOS_TABLE.length;s++){for(var c=0,f=t.RATIOS_TABLE[s],h=0;h<u.A.BARS_IN_MODULE;h++){var l=Math.fround(f[h]-n[h]);if((c+=Math.fround(l*l))>=i)break}c<i&&(i=c,a=u.A.SYMBOL_TABLE[s])}return a},t.bSymbolTableReady=!1,t.RATIOS_TABLE=Array(u.A.SYMBOL_TABLE.length).map(function(t){return Array(u.A.BARS_IN_MODULE)}),t}(),L=r(97016),x=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},F=function(){function t(){}return t.decode=function(e,r,n,o,i,s,u){for(var c,f=new v(e,r,n,o,i),h=null,l=null,d=!0;;d=!1){if(null!=r&&(h=t.getRowIndicatorColumn(e,f,r,!0,s,u)),null!=o&&(l=t.getRowIndicatorColumn(e,f,o,!1,s,u)),null==(c=t.merge(h,l)))throw a.A.getNotFoundInstance();var p=c.getBoundingBox();if(d&&null!=p&&(p.getMinY()<f.getMinY()||p.getMaxY()>f.getMaxY()))f=p;else break}c.setBoundingBox(f);var A=c.getBarcodeColumnCount()+1;c.setDetectionResultColumn(0,h),c.setDetectionResultColumn(A,l);for(var g=null!=h,y=1;y<=A;y++){var w=g?y:A-y;if(void 0===c.getDetectionResultColumn(w)){var _=void 0;_=0===w||w===A?new b(f,0===w):new E(f),c.setDetectionResultColumn(w,_);for(var C=-1,m=-1,I=f.getMinY();I<=f.getMaxY();I++){if((C=t.getStartColumn(c,w,I,g))<0||C>f.getMaxX()){if(-1===m)continue;C=m}var S=t.detectCodeword(e,f.getMinX(),f.getMaxX(),g,C,I,s,u);null!=S&&(_.setCodeword(I,S),m=C,s=Math.min(s,S.getWidth()),u=Math.max(u,S.getWidth()))}}}return t.createDecoderResult(c)},t.merge=function(e,r){if(null==e&&null==r)return null;var n=t.getBarcodeMetadata(e,r);return null==n?null:new D(n,v.merge(t.adjustBoundingBox(e),t.adjustBoundingBox(r)))},t.adjustBoundingBox=function(e){if(null==e)return null;var r,n,o=e.getRowHeights();if(null==o)return null;var i=t.getMax(o),a=0;try{for(var s=x(o),u=s.next();!u.done;u=s.next()){var c=u.value;if(a+=i-c,c>0)break}}catch(t){r={error:t}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}for(var f=e.getCodewords(),h=0;a>0&&null==f[h];h++)a--;for(var l=0,h=o.length-1;h>=0&&(l+=i-o[h],!(o[h]>0));h--);for(var h=f.length-1;l>0&&null==f[h];h--)l--;return e.getBoundingBox().addMissingRows(a,l,e.isLeft())},t.getMax=function(t){var e,r,n=-1;try{for(var o=x(t),i=o.next();!i.done;i=o.next()){var a=i.value;n=Math.max(n,a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},t.getBarcodeMetadata=function(t,e){var r,n;return null==t||null==(r=t.getBarcodeMetadata())?null==e?null:e.getBarcodeMetadata():null==e||null==(n=e.getBarcodeMetadata())?r:r.getColumnCount()!==n.getColumnCount()&&r.getErrorCorrectionLevel()!==n.getErrorCorrectionLevel()&&r.getRowCount()!==n.getRowCount()?null:r},t.getRowIndicatorColumn=function(e,r,n,o,i,a){for(var s=new b(r,o),u=0;u<2;u++)for(var c=0===u?1:-1,f=Math.trunc(Math.trunc(n.getX())),h=Math.trunc(Math.trunc(n.getY()));h<=r.getMaxY()&&h>=r.getMinY();h+=c){var l=t.detectCodeword(e,0,e.getWidth(),o,f,h,i,a);null!=l&&(s.setCodeword(h,l),f=o?l.getStartX():l.getEndX())}return s},t.adjustCodewordCount=function(e,r){var n=r[0][1],o=n.getValue(),i=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-t.getNumberOfECCodeWords(e.getBarcodeECLevel());if(0===o.length){if(i<1||i>u.A.MAX_CODEWORDS_IN_BARCODE)throw a.A.getNotFoundInstance();n.setValue(i)}else o[0]!==i&&n.setValue(i)},t.createDecoderResult=function(e){var r=t.createBarcodeMatrix(e);t.adjustCodewordCount(e,r);for(var n=[],o=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),i=[],a=[],s=0;s<e.getBarcodeRowCount();s++)for(var c=0;c<e.getBarcodeColumnCount();c++){var f=r[s][c+1].getValue(),h=s*e.getBarcodeColumnCount()+c;0===f.length?n.push(h):1===f.length?o[h]=f[0]:(a.push(h),i.push(f))}for(var l=Array(i.length),d=0;d<l.length;d++)l[d]=i[d];return t.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),o,u.A.toIntArray(n),u.A.toIntArray(a),l)},t.createDecoderResultFromAmbiguousValues=function(e,r,n,i,a){for(var s=new Int32Array(i.length),u=100;u-- >0;){for(var c=0;c<s.length;c++)r[i[c]]=a[c][s[c]];try{return t.decodeCodewords(r,e,n)}catch(t){if(!(t instanceof o.A))throw t}if(0===s.length)break;for(var c=0;c<s.length;c++)if(s[c]<a[c].length-1){s[c]++;break}else if(s[c]=0,c===s.length-1)throw o.A.getChecksumInstance()}throw o.A.getChecksumInstance()},t.createBarcodeMatrix=function(t){for(var e,r,n,o,i=Array.from({length:t.getBarcodeRowCount()},function(){return Array(t.getBarcodeColumnCount()+2)}),a=0;a<i.length;a++)for(var s=0;s<i[a].length;s++)i[a][s]=new T;var u=0;try{for(var c=x(t.getDetectionResultColumns()),f=c.next();!f.done;f=c.next()){var h=f.value;if(null!=h)try{for(var l=(n=void 0,x(h.getCodewords())),d=l.next();!d.done;d=l.next()){var p=d.value;if(null!=p){var A=p.getRowNumber();if(A>=0){if(A>=i.length)continue;i[A][u].setValue(p.getValue())}}}}catch(t){n={error:t}}finally{try{d&&!d.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}u++}}catch(t){e={error:t}}finally{try{f&&!f.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}return i},t.isValidBarcodeColumn=function(t,e){return e>=0&&e<=t.getBarcodeColumnCount()+1},t.getStartColumn=function(e,r,n,o){var i,a,s=o?1:-1,u=null;if(t.isValidBarcodeColumn(e,r-s)&&(u=e.getDetectionResultColumn(r-s).getCodeword(n)),null!=u)return o?u.getEndX():u.getStartX();if(null!=(u=e.getDetectionResultColumn(r).getCodewordNearby(n)))return o?u.getStartX():u.getEndX();if(t.isValidBarcodeColumn(e,r-s)&&(u=e.getDetectionResultColumn(r-s).getCodewordNearby(n)),null!=u)return o?u.getEndX():u.getStartX();for(var c=0;t.isValidBarcodeColumn(e,r-s);){r-=s;try{for(var f=(i=void 0,x(e.getDetectionResultColumn(r).getCodewords())),h=f.next();!h.done;h=f.next()){var l=h.value;if(null!=l)return(o?l.getEndX():l.getStartX())+s*c*(l.getEndX()-l.getStartX())}}catch(t){i={error:t}}finally{try{h&&!h.done&&(a=f.return)&&a.call(f)}finally{if(i)throw i.error}}c++}return o?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()},t.detectCodeword=function(e,r,n,o,i,a,s,c){i=t.adjustCodewordStartColumn(e,r,n,o,i,a);var f,h=t.getModuleBitCount(e,r,n,o,i,a);if(null==h)return null;var l=y.A.sum(h);if(o)f=i+l;else{for(var d=0;d<h.length/2;d++){var p=h[d];h[d]=h[h.length-1-d],h[h.length-1-d]=p}i=(f=i)-l}if(!t.checkCodewordSkew(l,s,c))return null;var A=B.getDecodedValue(h),g=u.A.getCodeword(A);return -1===g?null:new M(i,f,t.getCodewordBucketNumber(A),g)},t.getModuleBitCount=function(t,e,r,n,o,i){for(var a=o,s=new Int32Array(8),u=0,c=n?1:-1,f=n;(n?a<r:a>=e)&&u<s.length;)t.get(a,i)===f?(s[u]++,a+=c):(u++,f=!f);return u===s.length||a===(n?r:e)&&u===s.length-1?s:null},t.getNumberOfECCodeWords=function(t){return 2<<t},t.adjustCodewordStartColumn=function(e,r,n,o,i,a){for(var s=i,u=o?-1:1,c=0;c<2;c++){for(;(o?s>=r:s<n)&&o===e.get(s,a);){if(Math.abs(i-s)>t.CODEWORD_SKEW_SIZE)return i;s+=u}u=-u,o=!o}return s},t.checkCodewordSkew=function(e,r,n){return r-t.CODEWORD_SKEW_SIZE<=e&&e<=n+t.CODEWORD_SKEW_SIZE},t.decodeCodewords=function(e,r,n){if(0===e.length)throw i.A.getFormatInstance();var o=1<<r+1,a=t.correctErrors(e,n,o);t.verifyCodewordCount(e,o);var s=L.A.decode(e,""+r);return s.setErrorsCorrected(a),s.setErasures(n.length),s},t.correctErrors=function(e,r,n){if(null!=r&&r.length>n/2+t.MAX_ERRORS||n<0||n>t.MAX_EC_CODEWORDS)throw o.A.getChecksumInstance();return t.errorCorrection.decode(e,n,r)},t.verifyCodewordCount=function(t,e){if(t.length<4)throw i.A.getFormatInstance();var r=t[0];if(r>t.length)throw i.A.getFormatInstance();if(0===r)if(e<t.length)t[0]=t.length-e;else throw i.A.getFormatInstance()},t.getBitCountForCodeword=function(t){for(var e=new Int32Array(8),r=0,n=e.length-1;!((1&t)!==r&&(r=1&t,--n<0));)e[n]++,t>>=1;return e},t.getCodewordBucketNumber=function(t){return t instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(t):this.getCodewordBucketNumber_number(t)},t.getCodewordBucketNumber_number=function(e){return t.getCodewordBucketNumber(t.getBitCountForCodeword(e))},t.getCodewordBucketNumber_Int32Array=function(t){return(t[0]-t[2]+t[4]-t[6]+9)%9},t.toString=function(t){for(var e=new C,r=0;r<t.length;r++){e.format("Row %2d: ",r);for(var n=0;n<t[r].length;n++){var o=t[r][n];0===o.getValue().length?e.format("        ",null):e.format("%4d(%2d)",o.getValue()[0],o.getConfidence(o.getValue()[0]))}e.format("%n")}return e.toString()},t.CODEWORD_SKEW_SIZE=2,t.MAX_ERRORS=3,t.MAX_EC_CODEWORDS=512,t.errorCorrection=new w.A,t}(),k=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let V=function(){function t(){}return t.prototype.decode=function(e,r){void 0===r&&(r=null);var n=t.decode(e,r,!1);if(null==n||0===n.length||null==n[0])throw a.A.getNotFoundInstance();return n[0]},t.prototype.decodeMultiple=function(e,r){void 0===r&&(r=null);try{return t.decode(e,r,!0)}catch(t){if(t instanceof i.A||t instanceof o.A)throw a.A.getNotFoundInstance();throw t}},t.decode=function(e,r,o){var i,a,u=[],c=g.detectMultiple(e,r,o);try{for(var h=k(c.getPoints()),l=h.next();!l.done;l=h.next()){var d=l.value,p=F.decode(c.getBits(),d[4],d[5],d[6],d[7],t.getMinCodewordWidth(d),t.getMaxCodewordWidth(d)),A=new s.A(p.getText(),p.getRawBytes(),void 0,d,n.A.PDF_417);A.putMetadata(f.A.ERROR_CORRECTION_LEVEL,p.getECLevel());var y=p.getOther();null!=y&&A.putMetadata(f.A.PDF417_EXTRA_METADATA,y),u.push(A)}}catch(t){i={error:t}}finally{try{l&&!l.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}return u.map(function(t){return t})},t.getMaxWidth=function(t,e){return null==t||null==e?0:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMinWidth=function(t,e){return null==t||null==e?c.A.MAX_VALUE:Math.trunc(Math.abs(t.getX()-e.getX()))},t.getMaxCodewordWidth=function(e){return Math.floor(Math.max(Math.max(t.getMaxWidth(e[0],e[4]),t.getMaxWidth(e[6],e[2])*u.A.MODULES_IN_CODEWORD/u.A.MODULES_IN_STOP_PATTERN),Math.max(t.getMaxWidth(e[1],e[5]),t.getMaxWidth(e[7],e[3])*u.A.MODULES_IN_CODEWORD/u.A.MODULES_IN_STOP_PATTERN)))},t.getMinCodewordWidth=function(e){return Math.floor(Math.min(Math.min(t.getMinWidth(e[0],e[4]),t.getMinWidth(e[6],e[2])*u.A.MODULES_IN_CODEWORD/u.A.MODULES_IN_STOP_PATTERN),Math.min(t.getMinWidth(e[1],e[5]),t.getMinWidth(e[7],e[3])*u.A.MODULES_IN_CODEWORD/u.A.MODULES_IN_STOP_PATTERN)))},t.prototype.reset=function(){},t}()},82965:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(322),o=r(10997),i=r(64510),a=r(38988),s=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let u=function(t){function e(e,r,n,o,i,s,u,c){var f=t.call(this,s,u)||this;if(f.yuvData=e,f.dataWidth=r,f.dataHeight=n,f.left=o,f.top=i,o+s>r||i+u>n)throw new a.A("Crop rectangle does not fit within image data.");return c&&f.reverseHorizontal(s,u),f}return s(e,t),e.prototype.getRow=function(t,e){if(t<0||t>=this.getHeight())throw new a.A("Requested row is outside the image: "+t);var r=this.getWidth();(null==e||e.length<r)&&(e=new Uint8ClampedArray(r));var o=(t+this.top)*this.dataWidth+this.left;return n.A.arraycopy(this.yuvData,o,e,0,r),e},e.prototype.getMatrix=function(){var t=this.getWidth(),e=this.getHeight();if(t===this.dataWidth&&e===this.dataHeight)return this.yuvData;var r=t*e,o=new Uint8ClampedArray(r),i=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return n.A.arraycopy(this.yuvData,i,o,0,r),o;for(var a=0;a<e;a++){var s=a*t;n.A.arraycopy(this.yuvData,i,o,s,t),i+=this.dataWidth}return o},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,r,n,o){return new e(this.yuvData,this.dataWidth,this.dataHeight,this.left+t,this.top+r,n,o,!1)},e.prototype.renderThumbnail=function(){for(var t=this.getWidth()/e.THUMBNAIL_SCALE_FACTOR,r=this.getHeight()/e.THUMBNAIL_SCALE_FACTOR,n=new Int32Array(t*r),o=this.yuvData,i=this.top*this.dataWidth+this.left,a=0;a<r;a++){for(var s=a*t,u=0;u<t;u++){var c=255&o[i+u*e.THUMBNAIL_SCALE_FACTOR];n[s+u]=0xff000000|65793*c}i+=this.dataWidth*e.THUMBNAIL_SCALE_FACTOR}return n},e.prototype.getThumbnailWidth=function(){return this.getWidth()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.getThumbnailHeight=function(){return this.getHeight()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.reverseHorizontal=function(t,e){for(var r=this.yuvData,n=0,o=this.top*this.dataWidth+this.left;n<e;n++,o+=this.dataWidth)for(var i=o+t/2,a=o,s=o+t-1;a<i;a++,s--){var u=r[a];r[a]=r[s],r[s]=u}},e.prototype.invert=function(){return new i.A(this)},e.THUMBNAIL_SCALE_FACTOR=2,e}(o.A)},85469:(t,e,r)=>{"use strict";r.d(e,{A:()=>V});var n=r(25969),o=r(10782),i=r(79417),a=r(438),s=r(69071),u=r(43358),c=r(66950),f=r(55192),h=r(60109),l=r(21876),d=r(78903),p=r(78160),A=r(71534),g=function(){function t(t){var e=t.getHeight();if(e<21||(3&e)!=1)throw new A.A;this.bitMatrix=t}return t.prototype.readFormatInformation=function(){if(null!==this.parsedFormatInfo&&void 0!==this.parsedFormatInfo)return this.parsedFormatInfo;for(var t=0,e=0;e<6;e++)t=this.copyBit(e,8,t);t=this.copyBit(7,8,t),t=this.copyBit(8,8,t),t=this.copyBit(8,7,t);for(var r=5;r>=0;r--)t=this.copyBit(8,r,t);for(var n=this.bitMatrix.getHeight(),o=0,i=n-7,r=n-1;r>=i;r--)o=this.copyBit(8,r,o);for(var e=n-8;e<n;e++)o=this.copyBit(e,8,o);if(this.parsedFormatInfo=d.A.decodeFormatInformation(t,o),null!==this.parsedFormatInfo)return this.parsedFormatInfo;throw new A.A},t.prototype.readVersion=function(){if(null!==this.parsedVersion&&void 0!==this.parsedVersion)return this.parsedVersion;var t=this.bitMatrix.getHeight(),e=Math.floor((t-17)/4);if(e<=6)return l.A.getVersionForNumber(e);for(var r=0,n=t-11,o=5;o>=0;o--)for(var i=t-9;i>=n;i--)r=this.copyBit(i,o,r);var a=l.A.decodeVersionInformation(r);if(null!==a&&a.getDimensionForVersion()===t)return this.parsedVersion=a,a;r=0;for(var i=5;i>=0;i--)for(var o=t-9;o>=n;o--)r=this.copyBit(i,o,r);if(null!==(a=l.A.decodeVersionInformation(r))&&a.getDimensionForVersion()===t)return this.parsedVersion=a,a;throw new A.A},t.prototype.copyBit=function(t,e,r){return(this.isMirror?this.bitMatrix.get(e,t):this.bitMatrix.get(t,e))?r<<1|1:r<<1},t.prototype.readCodewords=function(){var t=this.readFormatInformation(),e=this.readVersion(),r=p.A.values.get(t.getDataMask()),n=this.bitMatrix.getHeight();r.unmaskBitMatrix(this.bitMatrix,n);for(var o=e.buildFunctionPattern(),i=!0,a=new Uint8Array(e.getTotalCodewords()),s=0,u=0,c=0,f=n-1;f>0;f-=2){6===f&&f--;for(var h=0;h<n;h++)for(var l=i?n-1-h:h,d=0;d<2;d++)o.get(f-d,l)||(c++,u<<=1,this.bitMatrix.get(f-d,l)&&(u|=1),8===c&&(a[s++]=u,c=0,u=0));i=!i}if(s!==e.getTotalCodewords())throw new A.A;return a},t.prototype.remask=function(){if(null!==this.parsedFormatInfo){var t=p.A.values.get(this.parsedFormatInfo.getDataMask()),e=this.bitMatrix.getHeight();t.unmaskBitMatrix(this.bitMatrix,e)}},t.prototype.setMirror=function(t){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=t},t.prototype.mirror=function(){for(var t=this.bitMatrix,e=0,r=t.getWidth();e<r;e++)for(var n=e+1,o=t.getHeight();n<o;n++)t.get(e,n)!==t.get(n,e)&&(t.flip(n,e),t.flip(e,n))},t}(),y=r(38988),w=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},v=function(){function t(t,e){this.numDataCodewords=t,this.codewords=e}return t.getDataBlocks=function(e,r,n){if(e.length!==r.getTotalCodewords())throw new y.A;var o,i,a,s,u=r.getECBlocksForLevel(n),c=0,f=u.getECBlocks();try{for(var h=w(f),l=h.next();!l.done;l=h.next()){var d=l.value;c+=d.getCount()}}catch(t){o={error:t}}finally{try{l&&!l.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}var p=Array(c),A=0;try{for(var g=w(f),v=g.next();!v.done;v=g.next())for(var d=v.value,_=0;_<d.getCount();_++){var C=d.getDataCodewords(),m=u.getECCodewordsPerBlock()+C;p[A++]=new t(C,new Uint8Array(m))}}catch(t){a={error:t}}finally{try{v&&!v.done&&(s=g.return)&&s.call(g)}finally{if(a)throw a.error}}for(var E=p[0].codewords.length,I=p.length-1;I>=0&&p[I].codewords.length!==E;)I--;I++;for(var S=E-u.getECCodewordsPerBlock(),T=0,_=0;_<S;_++)for(var O=0;O<A;O++)p[O].codewords[_]=e[T++];for(var O=I;O<A;O++)p[O].codewords[S]=e[T++];for(var R=p[0].codewords.length,_=S;_<R;_++)for(var O=0;O<A;O++){var b=O<I?_:_+1;p[O].codewords[b]=e[T++]}return p},t.prototype.getNumDataCodewords=function(){return this.numDataCodewords},t.prototype.getCodewords=function(){return this.codewords},t}(),_=r(41094),C=function(){function t(t){this.mirrored=t}return t.prototype.isMirrored=function(){return this.mirrored},t.prototype.applyMirroredCorrection=function(t){if(this.mirrored&&null!==t&&!(t.length<3)){var e=t[0];t[0]=t[2],t[2]=e}},t}(),m=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},E=function(){function t(){this.rsDecoder=new h.A(f.A.QR_CODE_FIELD_256)}return t.prototype.decodeBooleanArray=function(t,e){return this.decodeBitMatrix(o.A.parseFromBooleanArray(t),e)},t.prototype.decodeBitMatrix=function(t,e){var r=new g(t),n=null;try{return this.decodeBitMatrixParser(r,e)}catch(t){n=t}try{r.remask(),r.setMirror(!0),r.readVersion(),r.readFormatInformation(),r.mirror();var o=this.decodeBitMatrixParser(r,e);return o.setOther(new C(!0)),o}catch(t){if(null!==n)throw n;throw t}},t.prototype.decodeBitMatrixParser=function(t,e){var r,n,o,i,a=t.readVersion(),s=t.readFormatInformation().getErrorCorrectionLevel(),u=t.readCodewords(),c=v.getDataBlocks(u,a,s),f=0;try{for(var h=m(c),l=h.next();!l.done;l=h.next()){var d=l.value;f+=d.getNumDataCodewords()}}catch(t){r={error:t}}finally{try{l&&!l.done&&(n=h.return)&&n.call(h)}finally{if(r)throw r.error}}var p=new Uint8Array(f),A=0;try{for(var g=m(c),y=g.next();!y.done;y=g.next()){var d=y.value,w=d.getCodewords(),C=d.getNumDataCodewords();this.correctErrors(w,C);for(var E=0;E<C;E++)p[A++]=w[E]}}catch(t){o={error:t}}finally{try{y&&!y.done&&(i=g.return)&&i.call(g)}finally{if(o)throw o.error}}return _.A.decode(p,a,s,e)},t.prototype.correctErrors=function(t,e){var r=new Int32Array(t);try{this.rsDecoder.decode(r,t.length-e)}catch(t){throw new c.A}for(var n=0;n<e;n++)t[n]=r[n]},t}(),I=r(79886),S=r(56451),T=r(20367),O=r(39798),R=r(56595),b=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),N=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.estimatedModuleSize=n,o}return b(e,t),e.prototype.aboutEquals=function(t,e,r){if(Math.abs(e-this.getY())<=t&&Math.abs(r-this.getX())<=t){var n=Math.abs(t-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,r,n){return new e((this.getX()+r)/2,(this.getY()+t)/2,(this.estimatedModuleSize+n)/2)},e}(R.A),D=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},M=function(){function t(t,e,r,n,o,i,a){this.image=t,this.startX=e,this.startY=r,this.width=n,this.height=o,this.moduleSize=i,this.resultPointCallback=a,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}return t.prototype.find=function(){for(var t=this.startX,e=this.height,r=t+this.width,n=this.startY+e/2,o=new Int32Array(3),i=this.image,s=0;s<e;s++){var u=n+((1&s)==0?Math.floor((s+1)/2):-Math.floor((s+1)/2));o[0]=0,o[1]=0,o[2]=0;for(var c=t;c<r&&!i.get(c,u);)c++;for(var f=0;c<r;){if(i.get(c,u))if(1===f)o[1]++;else if(2===f){if(this.foundPatternCross(o)){var h=this.handlePossibleCenter(o,u,c);if(null!==h)return h}o[0]=o[2],o[1]=1,o[2]=0,f=1}else o[++f]++;else 1===f&&f++,o[f]++;c++}if(this.foundPatternCross(o)){var h=this.handlePossibleCenter(o,u,r);if(null!==h)return h}}if(0!==this.possibleCenters.length)return this.possibleCenters[0];throw new a.A},t.centerFromEnd=function(t,e){return e-t[2]-t[1]/2},t.prototype.foundPatternCross=function(t){for(var e=this.moduleSize,r=e/2,n=0;n<3;n++)if(Math.abs(e-t[n])>=r)return!1;return!0},t.prototype.crossCheckVertical=function(e,r,n,o){var i=this.image,a=i.getHeight(),s=this.crossCheckStateCount;s[0]=0,s[1]=0,s[2]=0;for(var u=e;u>=0&&i.get(r,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&!i.get(r,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<a&&i.get(r,u)&&s[1]<=n;)s[1]++,u++;if(u===a||s[1]>n)return NaN;for(;u<a&&!i.get(r,u)&&s[2]<=n;)s[2]++,u++;return s[2]>n||5*Math.abs(s[0]+s[1]+s[2]-o)>=2*o?NaN:this.foundPatternCross(s)?t.centerFromEnd(s,u):NaN},t.prototype.handlePossibleCenter=function(e,r,n){var o,i,a=e[0]+e[1]+e[2],s=t.centerFromEnd(e,n),u=this.crossCheckVertical(r,s,2*e[1],a);if(!isNaN(u)){var c=(e[0]+e[1]+e[2])/3;try{for(var f=D(this.possibleCenters),h=f.next();!h.done;h=f.next()){var l=h.value;if(l.aboutEquals(c,u,s))return l.combineEstimate(u,s,c)}}catch(t){o={error:t}}finally{try{h&&!h.done&&(i=f.return)&&i.call(f)}finally{if(o)throw o.error}}var d=new N(s,u,c);this.possibleCenters.push(d),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(d)}return null},t}(),P=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),B=function(t){function e(e,r,n,o){var i=t.call(this,e,r)||this;return i.estimatedModuleSize=n,i.count=o,void 0===o&&(i.count=1),i}return P(e,t),e.prototype.getEstimatedModuleSize=function(){return this.estimatedModuleSize},e.prototype.getCount=function(){return this.count},e.prototype.aboutEquals=function(t,e,r){if(Math.abs(e-this.getY())<=t&&Math.abs(r-this.getX())<=t){var n=Math.abs(t-this.estimatedModuleSize);return n<=1||n<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,r,n){var o=this.count+1;return new e((this.count*this.getX()+r)/o,(this.count*this.getY()+t)/o,(this.count*this.estimatedModuleSize+n)/o,o)},e}(R.A),L=function(){function t(t){this.bottomLeft=t[0],this.topLeft=t[1],this.topRight=t[2]}return t.prototype.getBottomLeft=function(){return this.bottomLeft},t.prototype.getTopLeft=function(){return this.topLeft},t.prototype.getTopRight=function(){return this.topRight},t}(),x=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},F=function(){function t(t,e){this.image=t,this.resultPointCallback=e,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=e}return t.prototype.getImage=function(){return this.image},t.prototype.getPossibleCenters=function(){return this.possibleCenters},t.prototype.find=function(e){var r=null!=e&&void 0!==e.get(i.A.TRY_HARDER),n=null!=e&&void 0!==e.get(i.A.PURE_BARCODE),o=this.image,a=o.getHeight(),s=o.getWidth(),u=Math.floor(3*a/(4*t.MAX_MODULES));(u<t.MIN_SKIP||r)&&(u=t.MIN_SKIP);for(var c=!1,f=new Int32Array(5),h=u-1;h<a&&!c;h+=u){f[0]=0,f[1]=0,f[2]=0,f[3]=0,f[4]=0;for(var l=0,d=0;d<s;d++)if(o.get(d,h))(1&l)==1&&l++,f[l]++;else if((1&l)==0)if(4===l)if(t.foundPatternCross(f)){var p=this.handlePossibleCenter(f,h,d,n);if(!0===p)if(u=2,!0===this.hasSkipped)c=this.haveMultiplyConfirmedCenters();else{var A=this.findRowSkip();A>f[2]&&(h+=A-f[2]-u,d=s-1)}else{f[0]=f[2],f[1]=f[3],f[2]=f[4],f[3]=1,f[4]=0,l=3;continue}l=0,f[0]=0,f[1]=0,f[2]=0,f[3]=0,f[4]=0}else f[0]=f[2],f[1]=f[3],f[2]=f[4],f[3]=1,f[4]=0,l=3;else f[++l]++;else f[l]++;if(t.foundPatternCross(f)){var p=this.handlePossibleCenter(f,h,s,n);!0===p&&(u=f[0],this.hasSkipped&&(c=this.haveMultiplyConfirmedCenters()))}}var g=this.selectBestPatterns();return R.A.orderBestPatterns(g),new L(g)},t.centerFromEnd=function(t,e){return e-t[4]-t[3]-t[2]/2},t.foundPatternCross=function(t){for(var e=0,r=0;r<5;r++){var n=t[r];if(0===n)return!1;e+=n}if(e<7)return!1;var o=e/7,i=o/2;return Math.abs(o-t[0])<i&&Math.abs(o-t[1])<i&&Math.abs(3*o-t[2])<3*i&&Math.abs(o-t[3])<i&&Math.abs(o-t[4])<i},t.prototype.getCrossCheckStateCount=function(){var t=this.crossCheckStateCount;return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t},t.prototype.crossCheckDiagonal=function(e,r,n,o){for(var i=this.getCrossCheckStateCount(),a=0,s=this.image;e>=a&&r>=a&&s.get(r-a,e-a);)i[2]++,a++;if(e<a||r<a)return!1;for(;e>=a&&r>=a&&!s.get(r-a,e-a)&&i[1]<=n;)i[1]++,a++;if(e<a||r<a||i[1]>n)return!1;for(;e>=a&&r>=a&&s.get(r-a,e-a)&&i[0]<=n;)i[0]++,a++;if(i[0]>n)return!1;var u=s.getHeight(),c=s.getWidth();for(a=1;e+a<u&&r+a<c&&s.get(r+a,e+a);)i[2]++,a++;if(e+a>=u||r+a>=c)return!1;for(;e+a<u&&r+a<c&&!s.get(r+a,e+a)&&i[3]<n;)i[3]++,a++;if(e+a>=u||r+a>=c||i[3]>=n)return!1;for(;e+a<u&&r+a<c&&s.get(r+a,e+a)&&i[4]<n;)i[4]++,a++;return!(i[4]>=n)&&Math.abs(i[0]+i[1]+i[2]+i[3]+i[4]-o)<2*o&&t.foundPatternCross(i)},t.prototype.crossCheckVertical=function(e,r,n,o){for(var i=this.image,a=i.getHeight(),s=this.getCrossCheckStateCount(),u=e;u>=0&&i.get(r,u);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!i.get(r,u)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&i.get(r,u)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<a&&i.get(r,u);)s[2]++,u++;if(u===a)return NaN;for(;u<a&&!i.get(r,u)&&s[3]<n;)s[3]++,u++;if(u===a||s[3]>=n)return NaN;for(;u<a&&i.get(r,u)&&s[4]<n;)s[4]++,u++;return s[4]>=n||5*Math.abs(s[0]+s[1]+s[2]+s[3]+s[4]-o)>=2*o?NaN:t.foundPatternCross(s)?t.centerFromEnd(s,u):NaN},t.prototype.crossCheckHorizontal=function(e,r,n,o){for(var i=this.image,a=i.getWidth(),s=this.getCrossCheckStateCount(),u=e;u>=0&&i.get(u,r);)s[2]++,u--;if(u<0)return NaN;for(;u>=0&&!i.get(u,r)&&s[1]<=n;)s[1]++,u--;if(u<0||s[1]>n)return NaN;for(;u>=0&&i.get(u,r)&&s[0]<=n;)s[0]++,u--;if(s[0]>n)return NaN;for(u=e+1;u<a&&i.get(u,r);)s[2]++,u++;if(u===a)return NaN;for(;u<a&&!i.get(u,r)&&s[3]<n;)s[3]++,u++;if(u===a||s[3]>=n)return NaN;for(;u<a&&i.get(u,r)&&s[4]<n;)s[4]++,u++;return s[4]>=n||5*Math.abs(s[0]+s[1]+s[2]+s[3]+s[4]-o)>=o?NaN:t.foundPatternCross(s)?t.centerFromEnd(s,u):NaN},t.prototype.handlePossibleCenter=function(e,r,n,o){var i=e[0]+e[1]+e[2]+e[3]+e[4],a=t.centerFromEnd(e,n),s=this.crossCheckVertical(r,Math.floor(a),e[2],i);if(!isNaN(s)&&!isNaN(a=this.crossCheckHorizontal(Math.floor(a),Math.floor(s),e[2],i))&&(!o||this.crossCheckDiagonal(Math.floor(s),Math.floor(a),e[2],i))){for(var u=i/7,c=!1,f=this.possibleCenters,h=0,l=f.length;h<l;h++){var d=f[h];if(d.aboutEquals(u,s,a)){f[h]=d.combineEstimate(s,a,u),c=!0;break}}if(!c){var p=new B(a,s,u);f.push(p),null!==this.resultPointCallback&&void 0!==this.resultPointCallback&&this.resultPointCallback.foundPossibleResultPoint(p)}return!0}return!1},t.prototype.findRowSkip=function(){if(this.possibleCenters.length<=1)return 0;var e,r,n=null;try{for(var o=x(this.possibleCenters),i=o.next();!i.done;i=o.next()){var a=i.value;if(a.getCount()>=t.CENTER_QUORUM)if(null!=n)return this.hasSkipped=!0,Math.floor((Math.abs(n.getX()-a.getX())-Math.abs(n.getY()-a.getY()))/2);else n=a}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return 0},t.prototype.haveMultiplyConfirmedCenters=function(){var e,r,n,o,i=0,a=0,s=this.possibleCenters.length;try{for(var u=x(this.possibleCenters),c=u.next();!c.done;c=u.next()){var f=c.value;f.getCount()>=t.CENTER_QUORUM&&(i++,a+=f.getEstimatedModuleSize())}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}if(i<3)return!1;var h=a/s,l=0;try{for(var d=x(this.possibleCenters),p=d.next();!p.done;p=d.next()){var f=p.value;l+=Math.abs(f.getEstimatedModuleSize()-h)}}catch(t){n={error:t}}finally{try{p&&!p.done&&(o=d.return)&&o.call(d)}finally{if(n)throw n.error}}return l<=.05*a},t.prototype.selectBestPatterns=function(){var t,e,r,n,o,i=this.possibleCenters.length;if(i<3)throw new a.A;var s=this.possibleCenters;if(i>3){var u=0,c=0;try{for(var f=x(this.possibleCenters),h=f.next();!h.done;h=f.next()){var l=h.value.getEstimatedModuleSize();u+=l,c+=l*l}}catch(e){t={error:e}}finally{try{h&&!h.done&&(e=f.return)&&e.call(f)}finally{if(t)throw t.error}}o=u/i;var d=Math.sqrt(c/i-o*o);s.sort(function(t,e){var r=Math.abs(e.getEstimatedModuleSize()-o),n=Math.abs(t.getEstimatedModuleSize()-o);return r<n?-1:+(r>n)});for(var p=Math.max(.2*o,d),A=0;A<s.length&&s.length>3;A++)Math.abs(s[A].getEstimatedModuleSize()-o)>p&&(s.splice(A,1),A--)}if(s.length>3){var u=0;try{for(var g=x(s),y=g.next();!y.done;y=g.next()){var w=y.value;u+=w.getEstimatedModuleSize()}}catch(t){r={error:t}}finally{try{y&&!y.done&&(n=g.return)&&n.call(g)}finally{if(r)throw r.error}}o=u/s.length,s.sort(function(t,e){if(e.getCount()!==t.getCount())return e.getCount()-t.getCount();var r=Math.abs(e.getEstimatedModuleSize()-o),n=Math.abs(t.getEstimatedModuleSize()-o);return r<n?1:r>n?-1:0}),s.splice(3)}return[s[0],s[1],s[2]]},t.CENTER_QUORUM=2,t.MIN_SKIP=3,t.MAX_MODULES=57,t}(),k=function(){function t(t){this.image=t}return t.prototype.getImage=function(){return this.image},t.prototype.getResultPointCallback=function(){return this.resultPointCallback},t.prototype.detect=function(t){this.resultPointCallback=null==t?null:t.get(i.A.NEED_RESULT_POINT_CALLBACK);var e=new F(this.image,this.resultPointCallback).find(t);return this.processFinderPatternInfo(e)},t.prototype.processFinderPatternInfo=function(e){var r,n=e.getTopLeft(),o=e.getTopRight(),i=e.getBottomLeft(),s=this.calculateModuleSize(n,o,i);if(s<1)throw new a.A("No pattern found in proccess finder.");var u=t.computeDimension(n,o,i,s),c=l.A.getProvisionalVersionForDimension(u),f=c.getDimensionForVersion()-7,h=null;if(c.getAlignmentPatternCenters().length>0)for(var d=o.getX()-n.getX()+i.getX(),p=o.getY()-n.getY()+i.getY(),A=1-3/f,g=Math.floor(n.getX()+A*(d-n.getX())),y=Math.floor(n.getY()+A*(p-n.getY())),w=4;w<=16;w<<=1)try{h=this.findAlignmentInRegion(s,g,y,w);break}catch(t){if(!(t instanceof a.A))throw t}var v=t.createTransform(n,o,i,h,u),_=t.sampleGrid(this.image,v,u);return r=null===h?[i,n,o]:[i,n,o,h],new S.A(_,r)},t.createTransform=function(t,e,r,n,o){var i,a,s,u,c=o-3.5;return null!==n?(i=n.getX(),a=n.getY(),u=s=c-3):(i=e.getX()-t.getX()+r.getX(),a=e.getY()-t.getY()+r.getY(),s=c,u=c),O.A.quadrilateralToQuadrilateral(3.5,3.5,c,3.5,s,u,3.5,c,t.getX(),t.getY(),e.getX(),e.getY(),i,a,r.getX(),r.getY())},t.sampleGrid=function(t,e,r){return T.A.getInstance().sampleGridWithTransform(t,r,r,e)},t.computeDimension=function(t,e,r,n){var o=Math.floor((I.A.round(R.A.distance(t,e)/n)+I.A.round(R.A.distance(t,r)/n))/2)+7;switch(3&o){case 0:o++;break;case 2:o--;break;case 3:throw new a.A("Dimensions could be not found.")}return o},t.prototype.calculateModuleSize=function(t,e,r){return(this.calculateModuleSizeOneWay(t,e)+this.calculateModuleSizeOneWay(t,r))/2},t.prototype.calculateModuleSizeOneWay=function(t,e){var r=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY())),n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY()));return isNaN(r)?n/7:isNaN(n)?r/7:(r+n)/14},t.prototype.sizeOfBlackWhiteBlackRunBothWays=function(t,e,r,n){var o=this.sizeOfBlackWhiteBlackRun(t,e,r,n),i=1,a=t-(r-t);a<0?(i=t/(t-a),a=0):a>=this.image.getWidth()&&(i=(this.image.getWidth()-1-t)/(a-t),a=this.image.getWidth()-1);var s=Math.floor(e-(n-e)*i);return i=1,s<0?(i=e/(e-s),s=0):s>=this.image.getHeight()&&(i=(this.image.getHeight()-1-e)/(s-e),s=this.image.getHeight()-1),a=Math.floor(t+(a-t)*i),(o+=this.sizeOfBlackWhiteBlackRun(t,e,a,s))-1},t.prototype.sizeOfBlackWhiteBlackRun=function(t,e,r,n){var o=Math.abs(n-e)>Math.abs(r-t);if(o){var i=t;t=e,e=i,i=r,r=n,n=i}for(var a=Math.abs(r-t),s=Math.abs(n-e),u=-a/2,c=t<r?1:-1,f=e<n?1:-1,h=0,l=r+c,d=t,p=e;d!==l;d+=c){var A=o?p:d,g=o?d:p;if(1===h===this.image.get(A,g)){if(2===h)return I.A.distance(d,p,t,e);h++}if((u+=s)>0){if(p===n)break;p+=f,u-=a}}return 2===h?I.A.distance(r+c,n,t,e):NaN},t.prototype.findAlignmentInRegion=function(t,e,r,n){var o=Math.floor(n*t),i=Math.max(0,e-o),s=Math.min(this.image.getWidth()-1,e+o);if(s-i<3*t)throw new a.A("Alignment top exceeds estimated module size.");var u=Math.max(0,r-o),c=Math.min(this.image.getHeight()-1,r+o);if(c-u<3*t)throw new a.A("Alignment bottom exceeds estimated module size.");return new M(this.image,i,u,s-i,c-u,t,this.resultPointCallback).find()},t}();let V=function(){function t(){this.decoder=new E}return t.prototype.getDecoder=function(){return this.decoder},t.prototype.decode=function(e,r){if(null!=r&&void 0!==r.get(i.A.PURE_BARCODE)){var o,a,c=t.extractPureBits(e.getBlackMatrix());o=this.decoder.decodeBitMatrix(c,r),a=t.NO_POINTS}else{var f=new k(e.getBlackMatrix()).detect(r);o=this.decoder.decodeBitMatrix(f.getBits(),r),a=f.getPoints()}o.getOther()instanceof C&&o.getOther().applyMirroredCorrection(a);var h=new s.A(o.getText(),o.getRawBytes(),void 0,a,n.A.QR_CODE,void 0),l=o.getByteSegments();null!==l&&h.putMetadata(u.A.BYTE_SEGMENTS,l);var d=o.getECLevel();return null!==d&&h.putMetadata(u.A.ERROR_CORRECTION_LEVEL,d),o.hasStructuredAppend()&&(h.putMetadata(u.A.STRUCTURED_APPEND_SEQUENCE,o.getStructuredAppendSequenceNumber()),h.putMetadata(u.A.STRUCTURED_APPEND_PARITY,o.getStructuredAppendParity())),h},t.prototype.reset=function(){},t.extractPureBits=function(t){var e=t.getTopLeftOnBit(),r=t.getBottomRightOnBit();if(null===e||null===r)throw new a.A;var n=this.moduleSize(e,t),i=e[1],s=r[1],u=e[0],c=r[0];if(u>=c||i>=s||s-i!=c-u&&(c=u+(s-i))>=t.getWidth())throw new a.A;var f=Math.round((c-u+1)/n),h=Math.round((s-i+1)/n);if(f<=0||h<=0||h!==f)throw new a.A;var l=Math.floor(n/2);i+=l;var d=(u+=l)+Math.floor((f-1)*n)-c;if(d>0){if(d>l)throw new a.A;u-=d}var p=i+Math.floor((h-1)*n)-s;if(p>0){if(p>l)throw new a.A;i-=p}for(var A=new o.A(f,h),g=0;g<h;g++)for(var y=i+Math.floor(g*n),w=0;w<f;w++)t.get(u+Math.floor(w*n),y)&&A.set(w,g);return A},t.moduleSize=function(t,e){for(var r=e.getHeight(),n=e.getWidth(),o=t[0],i=t[1],s=!0,u=0;o<n&&i<r;){if(s!==e.get(o,i)){if(5==++u)break;s=!s}o++,i++}if(o===n||i===r)throw new a.A;return(o-t[0])/7},t.NO_POINTS=[],t}()},85770:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(38988);let o=function(){function t(t){this.bytes=t,this.byteOffset=0,this.bitOffset=0}return t.prototype.getBitOffset=function(){return this.bitOffset},t.prototype.getByteOffset=function(){return this.byteOffset},t.prototype.readBits=function(t){if(t<1||t>32||t>this.available())throw new n.A(""+t);var e=0,r=this.bitOffset,o=this.byteOffset,i=this.bytes;if(r>0){var a=8-r,s=t<a?t:a,u=a-s,c=255>>8-s<<u;e=(i[o]&c)>>u,t-=s,8===(r+=s)&&(r=0,o++)}if(t>0){for(;t>=8;)e=e<<8|255&i[o],o++,t-=8;if(t>0){var u=8-t,c=255>>u<<u;e=e<<t|(i[o]&c)>>u,r+=t}}return this.bitOffset=r,this.byteOffset=o,e},t.prototype.available=function(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset},t}()},85808:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(38988);let o=function(){function t(){}return t.applyMaskPenaltyRule1=function(e){return t.applyMaskPenaltyRule1Internal(e,!0)+t.applyMaskPenaltyRule1Internal(e,!1)},t.applyMaskPenaltyRule2=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i-1;a++)for(var s=n[a],u=0;u<o-1;u++){var c=s[u];c===s[u+1]&&c===n[a+1][u]&&c===n[a+1][u+1]&&r++}return t.N2*r},t.applyMaskPenaltyRule3=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i;a++)for(var s=0;s<o;s++){var u=n[a];s+6<o&&1===u[s]&&0===u[s+1]&&1===u[s+2]&&1===u[s+3]&&1===u[s+4]&&0===u[s+5]&&1===u[s+6]&&(t.isWhiteHorizontal(u,s-4,s)||t.isWhiteHorizontal(u,s+7,s+11))&&r++,a+6<i&&1===n[a][s]&&0===n[a+1][s]&&1===n[a+2][s]&&1===n[a+3][s]&&1===n[a+4][s]&&0===n[a+5][s]&&1===n[a+6][s]&&(t.isWhiteVertical(n,s,a-4,a)||t.isWhiteVertical(n,s,a+7,a+11))&&r++}return r*t.N3},t.isWhiteHorizontal=function(t,e,r){e=Math.max(e,0),r=Math.min(r,t.length);for(var n=e;n<r;n++)if(1===t[n])return!1;return!0},t.isWhiteVertical=function(t,e,r,n){r=Math.max(r,0),n=Math.min(n,t.length);for(var o=r;o<n;o++)if(1===t[o][e])return!1;return!0},t.applyMaskPenaltyRule4=function(e){for(var r=0,n=e.getArray(),o=e.getWidth(),i=e.getHeight(),a=0;a<i;a++)for(var s=n[a],u=0;u<o;u++)1===s[u]&&r++;var c=e.getHeight()*e.getWidth();return Math.floor(10*Math.abs(2*r-c)/c)*t.N4},t.getDataMaskBit=function(t,e,r){var o,i;switch(t){case 0:o=r+e&1;break;case 1:o=1&r;break;case 2:o=e%3;break;case 3:o=(r+e)%3;break;case 4:o=Math.floor(r/2)+Math.floor(e/3)&1;break;case 5:o=(1&(i=r*e))+i%3;break;case 6:o=(1&(i=r*e))+i%3&1;break;case 7:o=(i=r*e)%3+(r+e&1)&1;break;default:throw new n.A("Invalid mask pattern: "+t)}return 0===o},t.applyMaskPenaltyRule1Internal=function(e,r){for(var n=0,o=r?e.getHeight():e.getWidth(),i=r?e.getWidth():e.getHeight(),a=e.getArray(),s=0;s<o;s++){for(var u=0,c=-1,f=0;f<i;f++){var h=r?a[s][f]:a[f][s];h===c?u++:(u>=5&&(n+=t.N1+(u-5)),u=1,c=h)}u>=5&&(n+=t.N1+(u-5))}return n},t.N1=3,t.N2=3,t.N3=40,t.N4=10,t}()},85917:(t,e,r)=>{"use strict";r.d(e,{A:()=>_});var n=r(25969),o=r(79417),i=r(69071),a=r(43358),s=r(56595),u=r(22152),c=r(438),f=r(71534),h=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.decodeRowStringBuffer="",e}return h(e,t),e.findStartGuardPattern=function(t){for(var r,n=!1,o=0,i=Int32Array.from([0,0,0]);!n;){i=Int32Array.from([0,0,0]);var a=(r=e.findGuardPattern(t,o,!1,this.START_END_PATTERN,i))[0],s=a-((o=r[1])-a);s>=0&&(n=t.isRange(s,a,!1))}return r},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var r=t.length;if(0===r)return!1;var n=parseInt(t.charAt(r-1),10);return e.getStandardUPCEANChecksum(t.substring(0,r-1))===n},e.getStandardUPCEANChecksum=function(t){for(var e=t.length,r=0,n=e-1;n>=0;n-=2){var o=t.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new f.A;r+=o}r*=3;for(var n=e-2;n>=0;n-=2){var o=t.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new f.A;r+=o}return(1e3-r)%10},e.decodeEnd=function(t,r){return e.findGuardPattern(t,r,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e.findGuardPatternWithoutCounters=function(t,e,r,n){return this.findGuardPattern(t,e,r,n,new Int32Array(n.length))},e.findGuardPattern=function(t,r,n,o,i){var a=t.getSize();r=n?t.getNextUnset(r):t.getNextSet(r);for(var s=0,f=r,h=o.length,l=n,d=r;d<a;d++)if(t.get(d)!==l)i[s]++;else{if(s===h-1){if(u.A.patternMatchVariance(i,o,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return Int32Array.from([f,d]);f+=i[0]+i[1];for(var p=i.slice(2,i.length),A=0;A<s-1;A++)i[A]=p[A];i[s-1]=0,i[s]=0,s--}else s++;i[s]=1,l=!l}throw new c.A},e.decodeDigit=function(t,r,n,o){this.recordPattern(t,n,r);for(var i=this.MAX_AVG_VARIANCE,a=-1,s=o.length,f=0;f<s;f++){var h=o[f],l=u.A.patternMatchVariance(r,h,e.MAX_INDIVIDUAL_VARIANCE);l<i&&(i=l,a=f)}if(a>=0)return a;throw new c.A},e.MAX_AVG_VARIANCE=.48,e.MAX_INDIVIDUAL_VARIANCE=.7,e.START_END_PATTERN=Int32Array.from([1,1,1]),e.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),e.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])],e}(u.A),d=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},p=function(){function t(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return t.prototype.decodeRow=function(e,r,o){var a=this.decodeRowStringBuffer,u=this.decodeMiddle(r,o,a),c=a.toString(),f=t.parseExtensionString(c),h=[new s.A((o[0]+o[1])/2,e),new s.A(u,e)],l=new i.A(c,null,0,h,n.A.UPC_EAN_EXTENSION,new Date().getTime());return null!=f&&l.putAllMetadata(f),l},t.prototype.decodeMiddle=function(e,r,n){var o,i,a=this.decodeMiddleCounters;a[0]=0,a[1]=0,a[2]=0,a[3]=0;for(var s=e.getSize(),u=r[1],f=0,h=0;h<5&&u<s;h++){var p=l.decodeDigit(e,a,u,l.L_AND_G_PATTERNS);n+=String.fromCharCode(48+p%10);try{for(var A=(o=void 0,d(a)),g=A.next();!g.done;g=A.next()){var y=g.value;u+=y}}catch(t){o={error:t}}finally{try{g&&!g.done&&(i=A.return)&&i.call(A)}finally{if(o)throw o.error}}p>=10&&(f|=1<<4-h),4!==h&&(u=e.getNextSet(u),u=e.getNextUnset(u))}if(5!==n.length)throw new c.A;var w=this.determineCheckDigit(f);if(t.extensionChecksum(n.toString())!==w)throw new c.A;return u},t.extensionChecksum=function(t){for(var e=t.length,r=0,n=e-2;n>=0;n-=2)r+=t.charAt(n).charCodeAt(0)-48;r*=3;for(var n=e-1;n>=0;n-=2)r+=t.charAt(n).charCodeAt(0)-48;return(r*=3)%10},t.prototype.determineCheckDigit=function(t){for(var e=0;e<10;e++)if(t===this.CHECK_DIGIT_ENCODINGS[e])return e;throw new c.A},t.parseExtensionString=function(e){if(5!==e.length)return null;var r=t.parseExtension5String(e);return null==r?null:new Map([[a.A.SUGGESTED_PRICE,r]])},t.parseExtension5String=function(t){switch(t.charAt(0)){case"0":e="\xa3";break;case"5":e="$";break;case"9":switch(t){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}e="";break;default:e=""}var e,r=parseInt(t.substring(1)),n=(r/100).toString(),o=r%100;return e+n+"."+(o<10?"0"+o:o.toString())},t}(),A=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g=function(){function t(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return t.prototype.decodeRow=function(e,r,o){var a=this.decodeRowStringBuffer,u=this.decodeMiddle(r,o,a),c=a.toString(),f=t.parseExtensionString(c),h=[new s.A((o[0]+o[1])/2,e),new s.A(u,e)],l=new i.A(c,null,0,h,n.A.UPC_EAN_EXTENSION,new Date().getTime());return null!=f&&l.putAllMetadata(f),l},t.prototype.decodeMiddle=function(t,e,r){var n,o,i=this.decodeMiddleCounters;i[0]=0,i[1]=0,i[2]=0,i[3]=0;for(var a=t.getSize(),s=e[1],u=0,f=0;f<2&&s<a;f++){var h=l.decodeDigit(t,i,s,l.L_AND_G_PATTERNS);r+=String.fromCharCode(48+h%10);try{for(var d=(n=void 0,A(i)),p=d.next();!p.done;p=d.next()){var g=p.value;s+=g}}catch(t){n={error:t}}finally{try{p&&!p.done&&(o=d.return)&&o.call(d)}finally{if(n)throw n.error}}h>=10&&(u|=1<<1-f),1!==f&&(s=t.getNextSet(s),s=t.getNextUnset(s))}if(2!==r.length||parseInt(r.toString())%4!==u)throw new c.A;return s},t.parseExtensionString=function(t){return 2!==t.length?null:new Map([[a.A.ISSUE_NUMBER,parseInt(t)]])},t}(),y=function(){function t(){}return t.decodeRow=function(t,e,r){var n=l.findGuardPattern(e,r,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{return new p().decodeRow(t,e,n)}catch(r){return new g().decodeRow(t,e,n)}},t.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]),t}(),w=r(66950),v=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let _=function(t){function e(){var r=t.call(this)||this;r.decodeRowStringBuffer="",e.L_AND_G_PATTERNS=e.L_PATTERNS.map(function(t){return Int32Array.from(t)});for(var n=10;n<20;n++){for(var o=e.L_PATTERNS[n-10],i=new Int32Array(o.length),a=0;a<o.length;a++)i[a]=o[o.length-a-1];e.L_AND_G_PATTERNS[n]=i}return r}return v(e,t),e.prototype.decodeRow=function(t,r,u){var h=e.findStartGuardPattern(r),l=null==u?null:u.get(o.A.NEED_RESULT_POINT_CALLBACK);if(null!=l){var d=new s.A((h[0]+h[1])/2,t);l.foundPossibleResultPoint(d)}var p=this.decodeMiddle(r,h,this.decodeRowStringBuffer),A=p.rowOffset,g=p.resultString;if(null!=l){var v=new s.A(A,t);l.foundPossibleResultPoint(v)}var _=e.decodeEnd(r,A);if(null!=l){var C=new s.A((_[0]+_[1])/2,t);l.foundPossibleResultPoint(C)}var m=_[1],E=m+(m-_[0]);if(E>=r.getSize()||!r.isRange(m,E,!1))throw new c.A;var I=g.toString();if(I.length<8)throw new f.A;if(!e.checkChecksum(I))throw new w.A;var S=(h[1]+h[0])/2,T=(_[1]+_[0])/2,O=this.getBarcodeFormat(),R=[new s.A(S,t),new s.A(T,t)],b=new i.A(I,null,0,R,O,new Date().getTime()),N=0;try{var D=y.decodeRow(t,r,_[1]);b.putMetadata(a.A.UPC_EAN_EXTENSION,D.getText()),b.putAllMetadata(D.getResultMetadata()),b.addResultPoints(D.getResultPoints()),N=D.getText().length}catch(t){}var M=null==u?null:u.get(o.A.ALLOWED_EAN_EXTENSIONS);if(null!=M){var P=!1;for(var B in M)if(N.toString()===B){P=!0;break}if(!P)throw new c.A}return O===n.A.EAN_13||n.A.UPC_A,b},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var r=t.length;if(0===r)return!1;var n=parseInt(t.charAt(r-1),10);return e.getStandardUPCEANChecksum(t.substring(0,r-1))===n},e.getStandardUPCEANChecksum=function(t){for(var e=t.length,r=0,n=e-1;n>=0;n-=2){var o=t.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new f.A;r+=o}r*=3;for(var n=e-2;n>=0;n-=2){var o=t.charAt(n).charCodeAt(0)-48;if(o<0||o>9)throw new f.A;r+=o}return(1e3-r)%10},e.decodeEnd=function(t,r){return e.findGuardPattern(t,r,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e}(l)},87932:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(56595);let o=function(){function t(t,e,r,o,i){this.value=t,this.startEnd=e,this.value=t,this.startEnd=e,this.resultPoints=[],this.resultPoints.push(new n.A(r,i)),this.resultPoints.push(new n.A(o,i))}return t.prototype.getValue=function(){return this.value},t.prototype.getStartEnd=function(){return this.startEnd},t.prototype.getResultPoints=function(){return this.resultPoints},t.prototype.equals=function(e){return e instanceof t&&this.value===e.value},t.prototype.hashCode=function(){return this.value},t}()},88690:()=>{},91375:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(59612);let o=function(){function t(){}return t.ISO_8859_1=n.A.ISO8859_1,t}()},92251:(t,e,r)=>{"use strict";r.d(e,{A:()=>d});var n=r(79417),o=r(25969),i=r(85469),a=r(72106),s=r(10692),u=r(41205),c=r(438),f=r(81339),h=r(10646),l=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let d=function(){function t(){}return t.prototype.decode=function(t,e){return this.setHints(e),this.decodeInternal(t)},t.prototype.decodeWithState=function(t){return(null===this.readers||void 0===this.readers)&&this.setHints(null),this.decodeInternal(t)},t.prototype.setHints=function(t){this.hints=t;var e=null!=t&&void 0!==t.get(n.A.TRY_HARDER),r=null==t?null:t.get(n.A.POSSIBLE_FORMATS),c=[];if(null!=r){var h=r.some(function(t){return t===o.A.UPC_A||t===o.A.UPC_E||t===o.A.EAN_13||t===o.A.EAN_8||t===o.A.CODABAR||t===o.A.CODE_39||t===o.A.CODE_93||t===o.A.CODE_128||t===o.A.ITF||t===o.A.RSS_14||t===o.A.RSS_EXPANDED});h&&!e&&c.push(new s.A(t)),r.includes(o.A.QR_CODE)&&c.push(new i.A),r.includes(o.A.DATA_MATRIX)&&c.push(new u.A),r.includes(o.A.AZTEC)&&c.push(new a.A),r.includes(o.A.PDF_417)&&c.push(new f.A),h&&e&&c.push(new s.A(t))}0===c.length&&(e||c.push(new s.A(t)),c.push(new i.A),c.push(new u.A),c.push(new a.A),c.push(new f.A),e&&c.push(new s.A(t))),this.readers=c},t.prototype.reset=function(){var t,e;if(null!==this.readers)try{for(var r=l(this.readers),n=r.next();!n.done;n=r.next())n.value.reset()}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},t.prototype.decodeInternal=function(t){var e,r;if(null===this.readers)throw new h.A("No readers where selected, nothing can be read.");try{for(var n=l(this.readers),o=n.next();!o.done;o=n.next()){var i=o.value;try{return i.decode(t,this.hints)}catch(t){if(t instanceof h.A)continue}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}throw new c.A("No MultiFormat Readers were able to detect the code.")},t}()},92727:(t,e,r)=>{"use strict";r.d(e,{A:()=>u});var n=r(48852),o=r(10782),i=r(39798),a=r(438),s=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return s(e,t),e.prototype.sampleGrid=function(t,e,r,n,o,a,s,u,c,f,h,l,d,p,A,g,y,w,v){var _=i.A.quadrilateralToQuadrilateral(n,o,a,s,u,c,f,h,l,d,p,A,g,y,w,v);return this.sampleGridWithTransform(t,e,r,_)},e.prototype.sampleGridWithTransform=function(t,e,r,i){if(e<=0||r<=0)throw new a.A;for(var s=new o.A(e,r),u=new Float32Array(2*e),c=0;c<r;c++){for(var f=u.length,h=c+.5,l=0;l<f;l+=2)u[l]=l/2+.5,u[l+1]=h;i.transformPoints(u),n.A.checkAndNudgePoints(t,u);try{for(var l=0;l<f;l+=2)t.get(Math.floor(u[l]),Math.floor(u[l+1]))&&s.set(l/2,c)}catch(t){throw new a.A}}return s},e}(n.A)},93682:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=r(68139),o=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();let i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.kind="WriterException",e}(n.A)},93770:(t,e,r)=>{"use strict";r.d(e,{A:()=>a});var n=r(6727),o=r(79886),i=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};let a=function(){function t(){}return t.prototype.PDF417Common=function(){},t.getBitCountSum=function(t){return o.A.sum(t)},t.toIntArray=function(e){if(null==e||!e.length)return t.EMPTY_INT_ARRAY;var r,n,o=new Int32Array(e.length),a=0;try{for(var s=i(e),u=s.next();!u.done;u=s.next()){var c=u.value;o[a++]=c}}catch(t){r={error:t}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return o},t.getCodeword=function(e){var r=n.A.binarySearch(t.SYMBOL_TABLE,262143&e);return r<0?-1:(t.CODEWORD_TABLE[r]-1)%t.NUMBER_OF_CODEWORDS},t.NUMBER_OF_CODEWORDS=929,t.MAX_CODEWORDS_IN_BARCODE=t.NUMBER_OF_CODEWORDS-1,t.MIN_ROWS_IN_BARCODE=3,t.MAX_ROWS_IN_BARCODE=90,t.MODULES_IN_CODEWORD=17,t.MODULES_IN_STOP_PATTERN=18,t.BARS_IN_MODULE=8,t.EMPTY_INT_ARRAY=new Int32Array([]),t.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),t.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]),t}()},93782:(t,e,r)=>{"use strict";r.d(e,{$9:()=>s,KX:()=>u,OM:()=>v,Qe:()=>A,Qw:()=>c,TG:()=>m,VK:()=>T,VL:()=>S,X7:()=>f,XQ:()=>a,ah:()=>h,d2:()=>E,dn:()=>g,eB:()=>C,eb:()=>w,fG:()=>I,gE:()=>i,gn:()=>l,h_:()=>_,ij:()=>y,mD:()=>p,mt:()=>R,t3:()=>o,tf:()=>d,uf:()=>O});var n,o,i=[5,7,10,11,12,14,18,20,24,28,36,42,48,56,62,68],a=[[228,48,15,111,62],[23,68,144,134,240,92,254],[28,24,185,166,223,248,116,255,110,61],[175,138,205,12,194,168,39,245,60,97,120],[41,153,158,91,61,42,142,213,97,178,100,242],[156,97,192,252,95,9,157,119,138,45,18,186,83,185],[83,195,100,39,188,75,66,61,241,213,109,129,94,254,225,48,90,188],[15,195,244,9,233,71,168,2,188,160,153,145,253,79,108,82,27,174,186,172],[52,190,88,205,109,39,176,21,155,197,251,223,155,21,5,172,254,124,12,181,184,96,50,193],[211,231,43,97,71,96,103,174,37,151,170,53,75,34,249,121,17,138,110,213,141,136,120,151,233,168,93,255],[245,127,242,218,130,250,162,181,102,120,84,179,220,251,80,182,229,18,2,4,68,33,101,137,95,119,115,44,175,184,59,25,225,98,81,112],[77,193,137,31,19,38,22,153,247,105,122,2,245,133,242,8,175,95,100,9,167,105,214,111,57,121,21,1,253,57,54,101,248,202,69,50,150,177,226,5,9,5],[245,132,172,223,96,32,117,22,238,133,238,231,205,188,237,87,191,106,16,147,118,23,37,90,170,205,131,88,120,100,66,138,186,240,82,44,176,87,187,147,160,175,69,213,92,253,225,19],[175,9,223,238,12,17,220,208,100,29,175,170,230,192,215,235,150,159,36,223,38,200,132,54,228,146,218,234,117,203,29,232,144,238,22,150,201,117,62,207,164,13,137,245,127,67,247,28,155,43,203,107,233,53,143,46],[242,93,169,50,144,210,39,118,202,188,201,189,143,108,196,37,185,112,134,230,245,63,197,190,250,106,185,221,175,64,114,71,161,44,147,6,27,218,51,63,87,10,40,130,188,17,163,31,176,170,4,107,232,7,94,166,224,124,86,47,11,204],[220,228,173,89,251,149,159,56,89,33,147,244,154,36,73,127,213,136,248,180,234,197,158,177,68,122,93,213,15,160,227,236,66,139,153,185,202,167,179,25,220,232,96,210,231,136,223,239,181,241,59,52,172,25,49,232,211,189,64,54,108,153,132,63,96,103,82,186]],s=(n=function(t,e){for(var r=1,n=0;n<255;n++)e[n]=r,t[r]=n,(r*=2)>=256&&(r^=301);return{LOG:t,ALOG:e}}([],[])).LOG,u=n.ALOG;!function(t){t[t.FORCE_NONE=0]="FORCE_NONE",t[t.FORCE_SQUARE=1]="FORCE_SQUARE",t[t.FORCE_RECTANGLE=2]="FORCE_RECTANGLE"}(o||(o={}));var c=129,f=230,h=231,l=235,d=236,p=237,A=238,g=239,y=240,w=254,v=254,_="[)>\x1e05\x1d",C="[)>\x1e06\x1d",m="\x1e\x04",E=0,I=1,S=2,T=3,O=4,R=5},95108:(t,e,r)=>{"use strict";r.d(e,{Q:()=>u});var n=r(27217),o=r(35168),i=r(61536),a=r(38988),s=r(39778),u=function(){function t(){}return t.prototype.write=function(e,r,s,u){if(void 0===u&&(u=null),0===e.length)throw new a.A("Found empty contents");if(r<0||s<0)throw new a.A("Requested dimensions are too small: "+r+"x"+s);var c=i.A.L,f=t.QUIET_ZONE_SIZE;null!==u&&(void 0!==u.get(n.A.ERROR_CORRECTION)&&(c=i.A.fromString(u.get(n.A.ERROR_CORRECTION).toString())),void 0!==u.get(n.A.MARGIN)&&(f=Number.parseInt(u.get(n.A.MARGIN).toString(),10)));var h=o.A.encode(e,c,u);return this.renderResult(h,r,s,f)},t.prototype.writeToDom=function(t,e,r,n,o){void 0===o&&(o=null),"string"==typeof t&&(t=document.querySelector(t));var i=this.write(e,r,n,o);t&&t.appendChild(i)},t.prototype.renderResult=function(t,e,r,n){var o=t.getMatrix();if(null===o)throw new s.A;for(var i=o.getWidth(),a=o.getHeight(),u=i+2*n,c=a+2*n,f=Math.max(e,u),h=Math.max(r,c),l=Math.min(Math.floor(f/u),Math.floor(h/c)),d=Math.floor((f-i*l)/2),p=Math.floor((h-a*l)/2),A=this.createSVGElement(f,h),g=0,y=p;g<a;g++,y+=l)for(var w=0,v=d;w<i;w++,v+=l)if(1===o.get(w,g)){var _=this.createSvgRectElement(v,y,l,l);A.appendChild(_)}return A},t.prototype.createSVGElement=function(e,r){var n=document.createElementNS(t.SVG_NS,"svg");return n.setAttributeNS(null,"height",e.toString()),n.setAttributeNS(null,"width",r.toString()),n},t.prototype.createSvgRectElement=function(e,r,n,o){var i=document.createElementNS(t.SVG_NS,"rect");return i.setAttributeNS(null,"x",e.toString()),i.setAttributeNS(null,"y",r.toString()),i.setAttributeNS(null,"height",n.toString()),i.setAttributeNS(null,"width",o.toString()),i.setAttributeNS(null,"fill","#000000"),i},t.QUIET_ZONE_SIZE=4,t.SVG_NS="http://www.w3.org/2000/svg",t}()},96768:(t,e,r)=>{"use strict";r.d(e,{K:()=>n});var n=function(){function t(t,e,r){this.deviceId=t,this.label=e,this.kind="videoinput",this.groupId=r||void 0}return t.prototype.toJSON=function(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}},t}()},97016:(t,e,r)=>{"use strict";r.d(e,{A:()=>O});var n,o,i=r(71534),a=r(59612),s=r(55701),u=r(27051),c=r(6727),f=r(1933),h=r(63479),l=function(){function t(){}return t.parseLong=function(t,e){return void 0===e&&(e=void 0),parseInt(t,e)},t}(),d=r(52067),p=r(68139),A=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return A(e,t),e.kind="NullPointerException",e}(p.A),y=function(){function t(){}return t.prototype.writeBytes=function(t){this.writeBytesOffset(t,0,t.length)},t.prototype.writeBytesOffset=function(t,e,r){if(null==t)throw new g;if(e<0||e>t.length||r<0||e+r>t.length||e+r<0)throw new d.A;if(0!==r)for(var n=0;n<r;n++)this.write(t[e+n])},t.prototype.flush=function(){},t.prototype.close=function(){},t}(),w=r(38988),v=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return v(e,t),e}(p.A),C=r(322),m=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(e,r)};return function(e,r){function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),E=function(t){function e(e){void 0===e&&(e=32);var r=t.call(this)||this;if(r.count=0,e<0)throw new w.A("Negative initial size: "+e);return r.buf=new Uint8Array(e),r}return m(e,t),e.prototype.ensureCapacity=function(t){t-this.buf.length>0&&this.grow(t)},e.prototype.grow=function(t){var e=this.buf.length<<1;if(e-t<0&&(e=t),e<0){if(t<0)throw new _;e=h.A.MAX_VALUE}this.buf=c.A.copyOfUint8Array(this.buf,e)},e.prototype.write=function(t){this.ensureCapacity(this.count+1),this.buf[this.count]=t,this.count+=1},e.prototype.writeBytesOffset=function(t,e,r){if(e<0||e>t.length||r<0||e+r-t.length>0)throw new d.A;this.ensureCapacity(this.count+r),C.A.arraycopy(t,e,this.buf,this.count,r),this.count+=r},e.prototype.writeTo=function(t){t.writeBytesOffset(this.buf,0,this.count)},e.prototype.reset=function(){this.count=0},e.prototype.toByteArray=function(){return c.A.copyOfUint8Array(this.buf,this.count)},e.prototype.size=function(){return this.count},e.prototype.toString=function(t){return t?"string"==typeof t?this.toString_string(t):this.toString_number(t):this.toString_void()},e.prototype.toString_void=function(){return new String(this.buf).toString()},e.prototype.toString_string=function(t){return new String(this.buf).toString()},e.prototype.toString_number=function(t){return new String(this.buf).toString()},e.prototype.close=function(){},e}(y),I=r(63623);function S(){if("undefined"!=typeof window)return window.BigInt||null;if(void 0!==r.g)return r.g.BigInt||null;if("undefined"!=typeof self)return self.BigInt||null;throw Error("Can't search globals for BigInt!")}function T(t){if(void 0===o&&(o=S()),null===o)throw Error("BigInt is not supported!");return o(t)}!function(t){t[t.ALPHA=0]="ALPHA",t[t.LOWER=1]="LOWER",t[t.MIXED=2]="MIXED",t[t.PUNCT=3]="PUNCT",t[t.ALPHA_SHIFT=4]="ALPHA_SHIFT",t[t.PUNCT_SHIFT=5]="PUNCT_SHIFT"}(n||(n={}));let O=function(){function t(){}return t.decode=function(e,r){var n=new f.A(""),o=a.A.ISO8859_1;n.enableDecoding(o);for(var c=1,h=e[c++],l=new u.A;c<e[0];){switch(h){case t.TEXT_COMPACTION_MODE_LATCH:c=t.textCompaction(e,c,n);break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:c=t.byteCompaction(h,e,o,c,n);break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[c++]);break;case t.NUMERIC_COMPACTION_MODE_LATCH:c=t.numericCompaction(e,c,n);break;case t.ECI_CHARSET:a.A.getCharacterSetECIByValue(e[c++]);break;case t.ECI_GENERAL_PURPOSE:c+=2;break;case t.ECI_USER_DEFINED:c++;break;case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:c=t.decodeMacroBlock(e,c,l);break;case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:throw new i.A;default:c--,c=t.textCompaction(e,c,n)}if(c<e.length)h=e[c++];else throw i.A.getFormatInstance()}if(0===n.length())throw i.A.getFormatInstance();var d=new s.A(null,n.toString(),null,r);return d.setOther(l),d},t.decodeMacroBlock=function(e,r,n){if(r+t.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw i.A.getFormatInstance();for(var o=new Int32Array(t.NUMBER_OF_SEQUENCE_CODEWORDS),a=0;a<t.NUMBER_OF_SEQUENCE_CODEWORDS;a++,r++)o[a]=e[r];n.setSegmentIndex(h.A.parseInt(t.decodeBase900toBase10(o,t.NUMBER_OF_SEQUENCE_CODEWORDS)));var s=new f.A;r=t.textCompaction(e,r,s),n.setFileId(s.toString());var u=-1;for(e[r]===t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(u=r+1);r<e[0];)switch(e[r]){case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(e[++r]){case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:var d=new f.A;r=t.textCompaction(e,r+1,d),n.setFileName(d.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SENDER:var p=new f.A;r=t.textCompaction(e,r+1,p),n.setSender(p.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:var A=new f.A;r=t.textCompaction(e,r+1,A),n.setAddressee(A.toString());break;case t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:var g=new f.A;r=t.numericCompaction(e,r+1,g),n.setSegmentCount(h.A.parseInt(g.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:var y=new f.A;r=t.numericCompaction(e,r+1,y),n.setTimestamp(l.parseLong(y.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:var w=new f.A;r=t.numericCompaction(e,r+1,w),n.setChecksum(h.A.parseInt(w.toString()));break;case t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:var v=new f.A;r=t.numericCompaction(e,r+1,v),n.setFileSize(l.parseLong(v.toString()));break;default:throw i.A.getFormatInstance()}break;case t.MACRO_PDF417_TERMINATOR:r++,n.setLastSegment(!0);break;default:throw i.A.getFormatInstance()}if(-1!==u){var _=r-u;n.isLastSegment()&&_--,n.setOptionalData(c.A.copyOfRange(e,u,u+_))}return r},t.textCompaction=function(e,r,n){for(var o=new Int32Array((e[0]-r)*2),i=new Int32Array((e[0]-r)*2),a=0,s=!1;r<e[0]&&!s;){var u=e[r++];if(u<t.TEXT_COMPACTION_MODE_LATCH)o[a]=u/30,o[a+1]=u%30,a+=2;else switch(u){case t.TEXT_COMPACTION_MODE_LATCH:o[a++]=t.TEXT_COMPACTION_MODE_LATCH;break;case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,s=!0;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:o[a]=t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,u=e[r++],i[a]=u,a++}}return t.decodeTextCompaction(o,i,a,n),r},t.decodeTextCompaction=function(e,r,o,i){for(var a=n.ALPHA,s=n.ALPHA,u=0;u<o;){var c=e[u],f="";switch(a){case n.ALPHA:if(c<26)f=String.fromCharCode(65+c);else switch(c){case 26:f=" ";break;case t.LL:a=n.LOWER;break;case t.ML:a=n.MIXED;break;case t.PS:s=a,a=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[u]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.LOWER:if(c<26)f=String.fromCharCode(97+c);else switch(c){case 26:f=" ";break;case t.AS:s=a,a=n.ALPHA_SHIFT;break;case t.ML:a=n.MIXED;break;case t.PS:s=a,a=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[u]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.MIXED:if(c<t.PL)f=t.MIXED_CHARS[c];else switch(c){case t.PL:a=n.PUNCT;break;case 26:f=" ";break;case t.LL:a=n.LOWER;break;case t.AL:a=n.ALPHA;break;case t.PS:s=a,a=n.PUNCT_SHIFT;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[u]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.PUNCT:if(c<t.PAL)f=t.PUNCT_CHARS[c];else switch(c){case t.PAL:a=n.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[u]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.ALPHA_SHIFT:if(a=s,c<26)f=String.fromCharCode(65+c);else switch(c){case 26:f=" ";break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}break;case n.PUNCT_SHIFT:if(a=s,c<t.PAL)f=t.PUNCT_CHARS[c];else switch(c){case t.PAL:a=n.ALPHA;break;case t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(r[u]);break;case t.TEXT_COMPACTION_MODE_LATCH:a=n.ALPHA}}""!==f&&i.append(f),u++}},t.byteCompaction=function(e,r,n,o,i){var a=new E,s=0,u=0,c=!1;switch(e){case t.BYTE_COMPACTION_MODE_LATCH:for(var f=new Int32Array(6),h=r[o++];o<r[0]&&!c;)switch(f[s++]=h,u=900*u+h,h=r[o++]){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,c=!0;break;default:if(s%5==0&&s>0){for(var l=0;l<6;++l)a.write(Number(T(u)>>T(8*(5-l))));u=0,s=0}}o===r[0]&&h<t.TEXT_COMPACTION_MODE_LATCH&&(f[s++]=h);for(var d=0;d<s;d++)a.write(f[d]);break;case t.BYTE_COMPACTION_MODE_LATCH_6:for(;o<r[0]&&!c;){var p=r[o++];if(p<t.TEXT_COMPACTION_MODE_LATCH)s++,u=900*u+p;else switch(p){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.NUMERIC_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:o--,c=!0}if(s%5==0&&s>0){for(var l=0;l<6;++l)a.write(Number(T(u)>>T(8*(5-l))));u=0,s=0}}}return i.append(I.A.decode(a.toByteArray(),n)),o},t.numericCompaction=function(e,r,n){for(var o=0,i=!1,a=new Int32Array(t.MAX_NUMERIC_CODEWORDS);r<e[0]&&!i;){var s=e[r++];if(r===e[0]&&(i=!0),s<t.TEXT_COMPACTION_MODE_LATCH)a[o]=s,o++;else switch(s){case t.TEXT_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH:case t.BYTE_COMPACTION_MODE_LATCH_6:case t.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case t.MACRO_PDF417_TERMINATOR:r--,i=!0}(o%t.MAX_NUMERIC_CODEWORDS==0||s===t.NUMERIC_COMPACTION_MODE_LATCH||i)&&o>0&&(n.append(t.decodeBase900toBase10(a,o)),o=0)}return r},t.decodeBase900toBase10=function(e,r){for(var n=T(0),o=0;o<r;o++)n+=t.EXP900[r-o-1]*T(e[o]);var a=n.toString();if("1"!==a.charAt(0))throw new i.A;return a.substring(1)},t.TEXT_COMPACTION_MODE_LATCH=900,t.BYTE_COMPACTION_MODE_LATCH=901,t.NUMERIC_COMPACTION_MODE_LATCH=902,t.BYTE_COMPACTION_MODE_LATCH_6=924,t.ECI_USER_DEFINED=925,t.ECI_GENERAL_PURPOSE=926,t.ECI_CHARSET=927,t.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,t.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,t.MACRO_PDF417_TERMINATOR=922,t.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,t.MAX_NUMERIC_CODEWORDS=15,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,t.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,t.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,t.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,t.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,t.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,t.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,t.PL=25,t.LL=27,t.AS=27,t.ML=28,t.AL=28,t.PS=29,t.PAL=29,t.PUNCT_CHARS=";<>@[\\]_`~!\r	,:\n-.$/\"|*()?{}'",t.MIXED_CHARS="0123456789&\r	,:#-.$/+%*=^",t.EXP900=S()?function(){var t=[];t[0]=T(1);var e=T(900);t[1]=e;for(var r=2;r<16;r++)t[r]=t[r-1]*e;return t}():[],t.NUMBER_OF_SEQUENCE_CODEWORDS=2,t}()},99496:(t,e,r)=>{"use strict";r.d(e,{A:()=>f});var n=r(25969),o=r(27217),i=r(10782),a=r(61536),s=r(35168),u=r(38988),c=r(39778);let f=function(){function t(){}return t.prototype.encode=function(e,r,i,c,f){if(0===e.length)throw new u.A("Found empty contents");if(r!==n.A.QR_CODE)throw new u.A("Can only encode QR_CODE, but got "+r);if(i<0||c<0)throw new u.A("Requested dimensions are too small: "+i+"x"+c);var h=a.A.L,l=t.QUIET_ZONE_SIZE;null!==f&&(void 0!==f.get(o.A.ERROR_CORRECTION)&&(h=a.A.fromString(f.get(o.A.ERROR_CORRECTION).toString())),void 0!==f.get(o.A.MARGIN)&&(l=Number.parseInt(f.get(o.A.MARGIN).toString(),10)));var d=s.A.encode(e,h,f);return t.renderResult(d,i,c,l)},t.renderResult=function(t,e,r,n){var o=t.getMatrix();if(null===o)throw new c.A;for(var a=o.getWidth(),s=o.getHeight(),u=a+2*n,f=s+2*n,h=Math.max(e,u),l=Math.max(r,f),d=Math.min(Math.floor(h/u),Math.floor(l/f)),p=Math.floor((h-a*d)/2),A=Math.floor((l-s*d)/2),g=new i.A(h,l),y=0,w=A;y<s;y++,w+=d)for(var v=0,_=p;v<a;v++,_+=d)1===o.get(v,y)&&g.setRegion(_,w,d,d);return g},t.QUIET_ZONE_SIZE=4,t}()}}]);