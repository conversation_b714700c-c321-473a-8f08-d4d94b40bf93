# AstroConnect Database Setup

This directory contains the database schema and setup files for the AstroConnect application.

## Files

- `schema.sql` - Main database schema with tables, indexes, and RLS policies
- `sample_data.sql` - Sample data for testing and development
- `README.md` - This documentation file

## Database Schema

### Tables

1. **users** - User profiles and authentication data
2. **qr_code_mappings** - Maps QR tokens to users
3. **horoscopes** - Daily, weekly, and monthly horoscope content
4. **daily_guides** - Daily guidance with lucky numbers, colors, etc.
5. **translation_cache** - Cached translations to reduce API calls

### Setup Instructions

1. Create a new Supabase project at https://supabase.com
2. Go to the SQL Editor in your Supabase dashboard
3. Run the `schema.sql` file to create the database structure
4. Optionally run `sample_data.sql` to add test data
5. Update your `.env.local` file with the Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

### Row Level Security (RLS)

The database uses RLS policies to ensure data security:

- Users can only access their own profile data
- QR mappings are restricted to the associated user
- Horoscopes and daily guides are publicly readable
- Translation cache is publicly readable for performance

### Automatic Features

- QR tokens are automatically generated for new users
- QR code mappings are automatically created when users are added
- Updated timestamps are automatically maintained
- Unique constraints prevent duplicate horoscopes and guides

### Indexes

Optimized indexes are created for:
- QR token lookups
- User email searches
- Horoscope queries by zodiac sign and date
- Daily guide queries by zodiac sign and date
- Translation cache lookups

## Environment Variables Required

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
GEMINI_API_KEY=your_gemini_api_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```
